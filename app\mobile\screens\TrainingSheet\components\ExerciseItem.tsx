import {useState} from 'react';
import {Text, View} from 'react-native';
import SpeakButton from '../../../components/SpeakButton/SpeakButton';

export const ExerciseItem = ({item, index}: {item: string; index: number}) => {
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const onPress = () => {
    console.log(item);
  };
  return (
    <View
      key={index}
      style={[
        {
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
        },
        index % 2 !== 0 ? {backgroundColor: 'rgba(60, 128, 243, 0.1)'} : {},
      ]}>
      <Text
        style={{
          fontSize: 35,
          marginVertical: 10,
          paddingLeft: '10%',
          fontWeight: 'bold',
          color: '#000',
        }}>
        {item}
      </Text>
    </View>
  );
};
