import { <PERSON><PERSON><PERSON><PERSON>, Svg<PERSON><PERSON> } from '@wuba/react-native-echarts';
import { Radar<PERSON><PERSON> } from 'echarts/charts';
import { LegendComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { useTheme } from 'react-native-paper';
import { AVQIChartModal, ABIChartModal } from './ChartModals';

// Register the required components
echarts.use([SV<PERSON>enderer, RadarChart, TooltipComponent, LegendComponent]);

type ChartProps = {
  data: string;
};

// Define a type for the detailed metric data
export type DetailedMetric = {
  key: string;
  name: string;
  value: number;
  normalizedValue: number;
  threshold: number;
  thresholdContinuousSpeech?: number;
  isNormal: boolean;
  higherIsBetter: boolean;
  description: string;
  min: number;
  max: number;
  chartType: 'AVQI' | 'ABI';
};

const Chart: React.FC<ChartProps> = ({ data }) => {
  const svgRef = useRef<any>(null);
  const chartRef = useRef<echarts.ECharts | null>(null);
  const theme = useTheme();

  // State for the detail modal and chart type
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState<DetailedMetric | null>(null);
  const [avqiMetrics, setAVQIMetrics] = useState<DetailedMetric[]>([]);
  const [abiMetrics, setABIMetrics] = useState<DetailedMetric[]>([]);
  const [chartType, setChartType] = useState<'AVQI' | 'ABI'>('AVQI');

  // Reference ranges for metrics based on clinical research
  const referenceRanges = {
    // AVQI metrics
    avqi: {
      min: 0,
      max: 10,
      threshold: 3.0, // Language-specific cutoff (ranges from ~2.0-3.5)
      higherIsBetter: false,
      name: 'AVQI',
      description: 'Acoustic Voice Quality Index - Composite measure of overall voice quality',
    },
    cpps: {
      min: 0,
      max: 25,
      threshold: 14.5, // For sustained vowels (ABI chart)
      thresholdContinuousSpeech: 9.3, // For connected speech (AVQI chart)
      higherIsBetter: true,
      name: 'CPPS',
      description: 'Cepstral Peak Prominence Smoothed - Measures voice periodicity',
    },
    hnr: {
      min: 0,
      max: 30,
      threshold: 20.0, // Updated based on research
      higherIsBetter: true,
      name: 'HNR',
      description: 'Harmonics-to-Noise Ratio - Measures ratio of harmonic to noise components',
    },
    shimmer_percent: {
      min: 0,
      max: 10,
      threshold: 5.0, // Clinical standard
      higherIsBetter: false,
      name: 'Shimmer %',
      description: 'Measures amplitude stability of vocal fold vibration (percentage)',
    },
    shimmer_db: {
      min: 0,
      max: 1,
      threshold: 0.5, // Updated based on research
      higherIsBetter: false,
      name: 'Shimmer dB',
      description: 'Measures amplitude stability of vocal fold vibration (decibels)',
    },
    slope: {
      min: -20,
      max: 0,
      threshold: -10.0, // Moderate high-frequency roll-off
      higherIsBetter: false,
      name: 'Slope',
      description: 'Spectral slope (dB/octave) - Measures energy distribution across frequencies',
    },
    tilt: {
      min: 0,
      max: 30,
      threshold: 15.0, // Moderate tilt (low-frequency energy moderately higher than high-frequency)
      higherIsBetter: false,
      name: 'Tilt',
      description: 'Spectral tilt - Balance between low and high frequency energy',
    },

    // ABI metrics
    abi: {
      min: 0,
      max: 10,
      threshold: 3.0, // Updated from 2.35 to 3.0 based on research
      higherIsBetter: false,
      name: 'ABI',
      description: 'Acoustic Breathiness Index - Quantifies breathiness in voice',
    },
    gne: {
      min: 0,
      max: 1,
      threshold: 0.5, // Values below 0.5 suggest pathological level of glottal noise
      higherIsBetter: true,
      name: 'GNE',
      description: 'Glottal-to-Noise Excitation Ratio - Measures ratio of glottal excitation to noise',
    },
    h1h2: {
      min: -5,
      max: 15,
      threshold: 6.0, // Updated based on research (higher values indicate breathier voice)
      higherIsBetter: false,
      name: 'H1-H2',
      description: 'First minus Second Harmonic (dB) - Correlate of glottal closure and open quotient',
    },
    hnrd: {
      min: 0,
      max: 30,
      threshold: 20.0, // High HNR (≥20 dB) for sustained vowel phonation in healthy voices
      higherIsBetter: true,
      name: 'HNRD',
      description: 'Harmonic-to-Noise Ratio in dB - Measures noise in the voice signal',
    },
    jitter: {
      min: 0,
      max: 3,
      threshold: 0.5, // Values below 0.5% considered normal
      higherIsBetter: false,
      name: 'Jitter',
      description: 'Measures frequency stability of vocal fold vibration',
    },
  };

  // Priority metrics for each chart type
  const avqiPriorityMetrics = [
    'avqi',           // AVQI
    'cpps',           // CPPS
    'hnr',            // HNR
    'shimmer_db',     // Shimmer dB
    'shimmer_percent', // Shimmer %
    'slope',          // Spectral Slope
    'tilt',           // Spectral Tilt
  ];

  const abiPriorityMetrics = [
    'abi',            // ABI
    'cpps',           // CPPS
    'gne',            // GNE
    'h1h2',           // H1-H2
    'hnrd',           // HNRD (HNR in dB)
    'jitter',         // Jitter %
    'shimmer_db',     // Shimmer dB
    'shimmer_percent', // Shimmer %
  ];

  // Chart click handler
  const handleChartClick = useCallback(() => {
    const currentMetrics = chartType === 'AVQI' ? avqiMetrics : abiMetrics;

    if (currentMetrics.length > 0) {
      setSelectedMetric(currentMetrics[0]);
      setModalVisible(true);
    }
  }, [chartType, avqiMetrics, abiMetrics]);



  // Process data and create chart
  useEffect(() => {
    if (!data) return;

    try {
      processChartData(JSON.parse(data));
    } catch (error) {
      console.error('Error processing chart data:', error);
    }
  }, [data]);

  // Update chart when chart type changes
  useEffect(() => {
    if (!chartRef.current) return;
    updateChart();
  }, [chartType, avqiMetrics, abiMetrics, theme.colors.primary]);

  // Process the data from the API
  const processChartData = (convertData: any) => {
    const chartValues: Record<string, number> = {};

    const hasAVQI =
      convertData.hasOwnProperty('value') ||
      convertData.hasOwnProperty('avqi') ||
      convertData.hasOwnProperty('cpps') ||
      convertData.hasOwnProperty('hnr');

    const hasABI =
      convertData.hasOwnProperty('abi') ||
      (convertData.abi && convertData.abi.value !== undefined) ||
      convertData.hasOwnProperty('abi_cpps') ||
      convertData.hasOwnProperty('abi_gne') ||
      convertData.hasOwnProperty('gne') ||
      convertData.hasOwnProperty('h1h2') ||
      convertData.hasOwnProperty('abi_h1h2') ||
      convertData.hasOwnProperty('hnrd') ||
      convertData.hasOwnProperty('abi_hnrd') ||
      convertData.hasOwnProperty('jitter') ||
      convertData.hasOwnProperty('abi_jitter') ||
      convertData.hasOwnProperty('shimmer_percent') ||
      convertData.hasOwnProperty('abi_shimmer_percent');

    const isABIData =
      convertData._chartType === 'ABI' ||
      (hasABI &&
        (Object.keys(convertData).includes('abi') ||
          !hasAVQI ||
          Object.keys(convertData).filter(k => k.startsWith('abi_')).length > 0));

    if (isABIData) {
      setChartType('ABI');
    } else if (hasAVQI) {
      setChartType('AVQI');
    } else if (hasABI) {
      setChartType('ABI');
    }

    extractMetrics(convertData, chartValues, hasAVQI, hasABI);
  };

  const extractMetrics = (convertData: any, chartValues: Record<string, number>, hasAVQI: boolean, hasABI: boolean) => {
    if (hasAVQI) {
      extractAVQIMetrics(convertData, chartValues);
    }

    if (hasABI) {
      extractABIMetrics(convertData, chartValues);
    }

    createDetailedMetrics(chartValues, hasAVQI, hasABI);
  };

  const extractAVQIMetrics = (convertData: any, chartValues: Record<string, number>) => {
    const avqiKeysToExtract = [
      'value', 'avqi', 'cpps', 'hnr', 'shimmer_percent', 'shimmer_db', 'slope', 'tilt',
    ];

    avqiKeysToExtract.forEach(key => {
      if (convertData[key] !== undefined) {
        const normalizedKey = (key === 'value' || key === 'avqi') ? 'avqi' : key;
        chartValues[normalizedKey] = typeof convertData[key] === 'string'
          ? parseFloat(convertData[key])
          : convertData[key];
      }
    });
  };

  const extractABIMetrics = (convertData: any, chartValues: Record<string, number>) => {
    const directMetrics: Record<string, string> = {
      'abi': 'abi',
      'h1h2': 'h1h2',
      'hnrd': 'hnrd'
    };

    Object.entries(directMetrics).forEach(([key, targetKey]) => {
      if (convertData[key] !== undefined) {
        chartValues[targetKey] = typeof convertData[key] === 'string'
          ? parseFloat(convertData[key])
          : convertData[key];
      }
    });

    const prefixedMetrics: Record<string, [string, string]> = {
      'cpps': ['abi_cpps', 'cpps'],
      'gne': ['abi_gne', 'gne'],
      'jitter': ['abi_jitter', 'jitter'],
      'shimmer_percent': ['abi_shimmer_percent', 'shimmer_percent'],
      'shimmer_db': ['abi_shimmer_db', 'shimmer_db']
    };

    Object.entries(prefixedMetrics).forEach(([targetKey, [prefixedKey, nonPrefixedKey]]) => {
      const value = convertData[prefixedKey] !== undefined ? convertData[prefixedKey] : convertData[nonPrefixedKey];
      if (value !== undefined) {
        chartValues[targetKey] = typeof value === 'string' ? parseFloat(value) : value;
      }
    });

    const abiKeysMap = {
      abi: 'abi',
      abi_cpps: 'cpps',
      cpps: 'cpps',
      abi_jitter: 'jitter',
      jitter: 'jitter',
      abi_gne: 'gne',
      gne: 'gne',
      abi_shimmer_db: 'shimmer_db',
      shimmer_db: 'shimmer_db',
      abi_shimmer_percent: 'shimmer_percent',
      shimmer_percent: 'shimmer_percent',
      abi_h1h2: 'h1h2',
      h1h2: 'h1h2',
      abi_hnrd: 'hnrd',
      hnrd: 'hnrd',
    };

    Object.entries(abiKeysMap).forEach(([sourceKey, targetKey]) => {
      if (convertData[sourceKey] !== undefined) {
        chartValues[targetKey] = typeof convertData[sourceKey] === 'string'
          ? parseFloat(convertData[sourceKey])
          : convertData[sourceKey];
      }
    });

    if (!chartValues.hasOwnProperty('abi')) {
      if (convertData.abi && convertData.abi.value !== undefined) {
        chartValues['abi'] = typeof convertData.abi.value === 'string'
          ? parseFloat(convertData.abi.value)
          : convertData.abi.value;
      }
    }
  };

  const createDetailedMetrics = (chartValues: Record<string, number>, hasAVQI: boolean, hasABI: boolean) => {
    if (hasAVQI) {
      const avqiMetricsData = createMetricsForType(chartValues, avqiPriorityMetrics, 'AVQI');
      setAVQIMetrics(avqiMetricsData);
    }

    if (hasABI) {
      const abiMetricsData = createMetricsForType(chartValues, abiPriorityMetrics, 'ABI');
      setABIMetrics(abiMetricsData);
    }
  };

  const createMetricsForType = (chartValues: Record<string, number>, priorityMetrics: string[], chartType: 'AVQI' | 'ABI'): DetailedMetric[] => {
    const filteredEntries = Object.entries(chartValues)
      .filter(([key]) => priorityMetrics.includes(key))
      .sort((a, b) => {
        if (a[0] === 'avqi' || a[0] === 'abi') return -1;
        if (b[0] === 'avqi' || b[0] === 'abi') return 1;
        return priorityMetrics.indexOf(a[0]) - priorityMetrics.indexOf(b[0]);
      });

    let entries = [...filteredEntries];
    if (entries.length < 3) {
      const placeholders: [string, number][] = [
        ['placeholder1', 0],
        ['placeholder2', 0],
        ['placeholder3', 0]
      ];

      for (let i = 0; i < 3 - entries.length; i++) {
        entries.push(placeholders[i]);
      }
    }

    return entries.map(([key, value]) => {
      const range = referenceRanges[key as keyof typeof referenceRanges];

      if (!range) {
        return {
          key,
          name: key.startsWith('placeholder') ? `Metric ${key.slice(-1)}` : key.toUpperCase(),
          value,
          normalizedValue: 0,
          threshold: 0,
          isNormal: true,
          higherIsBetter: true,
          description: 'Placeholder metric',
          min: 0,
          max: 1,
          chartType
        };
      }

      let threshold = range.threshold;
      if (key === 'cpps' && chartType === 'AVQI' && 'thresholdContinuousSpeech' in range) {
        threshold = range.thresholdContinuousSpeech;
      }

      // Determine if the value is within normal range
      let isNormal;

      // Special cases for metrics with specific normal ranges
      switch (key) {
        case 'slope':
          // For Slope: -6 to -12 dB/octave is normal
          isNormal = value >= -12 && value <= -6;
          break;
        case 'tilt':
          // For Tilt: ~10-20 dB is normal
          isNormal = value >= 10 && value <= 20;
          break;
        case 'h1h2':
          // For H1-H2: ~2-6 dB is normal
          isNormal = value >= 2 && value <= 6;
          break;
        default:
          // Standard higher/lower is better logic
          isNormal = range.higherIsBetter
            ? value >= threshold
            : value <= threshold;
      }

      const normalizedValue = range.higherIsBetter
        ? Math.max(0, Math.min(1, (value - range.min) / (range.max - range.min)))
        : Math.max(0, Math.min(1, 1 - (value - range.min) / (range.max - range.min)));

      const metricObject: DetailedMetric = {
        key,
        name: range.name,
        value,
        normalizedValue,
        threshold: range.threshold,
        isNormal,
        higherIsBetter: range.higherIsBetter,
        description: range.description,
        min: range.min,
        max: range.max,
        chartType
      };

      if ('thresholdContinuousSpeech' in range) {
        metricObject.thresholdContinuousSpeech = range.thresholdContinuousSpeech;
      }

      return metricObject;
    });
  };

  const updateChart = () => {
    if (!chartRef.current) return;

    const currentMetrics = chartType === 'AVQI' ? avqiMetrics : abiMetrics;
    if (currentMetrics.length === 0) return;

    const indicators = currentMetrics.map(metric => ({
      name: metric.name,
      min: 0,
      max: 1,
      axisLabel: {
        formatter: function (value: number) {
          return `${Math.round(value * 100)}%`;
        },
      },
    }));

    const normalizedValues = currentMetrics.map(metric => metric.normalizedValue);

    const normalizedThresholds = currentMetrics.map(metric => {
      const threshold = metric.key === 'cpps' && chartType === 'AVQI' && metric.thresholdContinuousSpeech
        ? metric.thresholdContinuousSpeech
        : metric.threshold;

      const range = metric.higherIsBetter
        ? (threshold - metric.min) / (metric.max - metric.min)
        : 1 - (threshold - metric.min) / (metric.max - metric.min);

      return Math.max(0, Math.min(1, range));
    });

    const option = {
      tooltip: {
        show: false,
      },
      legend: {
        data: ['Patient Values', 'Normal Range'],
        bottom: 30,
        itemWidth: 12,
        itemHeight: 8,
        textStyle: {
          color: '#333',
          fontSize: 11,
        },
      },
      radar: {
        shape: 'polygon',
        radius: '50%',
        center: ['50%', '45%'],
        splitNumber: 5,
        scale: true,
        splitArea: {
          areaStyle: {
            color: ['rgba(255, 255, 255, 0.1)', 'rgba(245, 245, 245, 0.2)'],
          },
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(120, 120, 120, 0.6)',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(120, 120, 120, 0.4)',
          },
        },
        axisName: {
          formatter: '{value}',
          color: '#333',
          fontSize: 12,
        },
        indicator: indicators,
        startAngle: 90,
        triggerEvent: true,
      },
      series: [
        {
          name: 'Patient Values',
          type: 'radar',
          data: [
            {
              value: normalizedValues,
              name: 'Patient Values',
              areaStyle: {
                color: 'rgba(77, 208, 225, 0.4)',
              },
            },
          ],
          lineStyle: {
            color: 'rgba(77, 208, 225, 0.9)',
            width: 2,
          },
          itemStyle: {
            color: 'rgba(77, 208, 225, 1)',
          },
          symbol: 'circle',
          symbolSize: 6,
        },
        {
          name: 'Normal Range',
          type: 'radar',
          data: [
            {
              value: normalizedThresholds,
              name: 'Normal Range',
              areaStyle: {
                color: 'rgba(144, 238, 144, 0.2)',
              },
            },
          ],
          lineStyle: {
            color: 'rgba(76, 175, 80, 0.7)',
            width: 1,
            type: 'dashed',
          },
          itemStyle: {
            color: 'rgba(76, 175, 80, 0.8)',
          },
          symbol: 'circle',
          symbolSize: 4,
        },
      ],
    };

    chartRef.current.setOption(option, true);
  };

  // Initialize the chart
  useEffect(() => {
    if (svgRef.current) {
      try {
        const chart = echarts.init(svgRef.current, 'light', {
          renderer: 'svg',
          width: 400,
          height: 400,
        });

        chartRef.current = chart;

        chart.getZr().on('click', handleChartClick);

        if ((chartType === 'AVQI' && avqiMetrics.length > 0) ||
          (chartType === 'ABI' && abiMetrics.length > 0)) {
          updateChart();
        }

        return () => {
          chart.dispose();
          chartRef.current = null;
        };
      } catch (error) {
        console.error('Error initializing chart:', error);
      }
    }
  }, [handleChartClick, chartType, avqiMetrics, abiMetrics]);

  return (
    <View>
      <View
        style={styles.chartContainer}
        onLayout={() => {
          setTimeout(() => {
            if (!chartRef.current && svgRef.current) {
              try {
                const chart = echarts.init(svgRef.current, 'light', {
                  renderer: 'svg',
                  width: 400,
                  height: 400,
                });
                chartRef.current = chart;
                chart.getZr().on('click', handleChartClick);
                updateChart();
              } catch (error) {
                console.error('Error initializing chart:', error);
              }
            }
          }, 100);
        }}
      >
        <SvgChart
          useRNGH
          style={{
            marginVertical: 0,
            alignSelf: 'center',
            width: '100%',
            height: 400,
          }}
          ref={svgRef}
        />
      </View>

      <Text style={styles.tapHint}>
        Tap on the chart for detailed information
      </Text>

      {/* AVQI Chart Modal */}
      <AVQIChartModal
        visible={modalVisible && chartType === 'AVQI'}
        onClose={() => setModalVisible(false)}
        selectedMetric={selectedMetric}
        metrics={avqiMetrics.filter(metric => !metric.key.startsWith('placeholder'))}
        onSelectMetric={setSelectedMetric}
      />

      {/* ABI Chart Modal */}
      <ABIChartModal
        visible={modalVisible && chartType === 'ABI'}
        onClose={() => setModalVisible(false)}
        selectedMetric={selectedMetric}
        metrics={abiMetrics.filter(metric => !metric.key.startsWith('placeholder'))}
        onSelectMetric={setSelectedMetric}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  chartContainer: {
    position: 'relative',
    width: '100%',
  },
  tapHint: {
    textAlign: 'center',
    color: '#666',
    fontSize: 12,
    marginTop: -20,
  },
});

export default Chart;