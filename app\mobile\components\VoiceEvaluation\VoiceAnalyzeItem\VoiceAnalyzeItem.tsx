import { ChevronRight, Trash } from 'lucide-react-native';
import { useEffect, useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import RNFS from 'react-native-fs';
import { Text } from 'react-native-paper';
import { GestureDetector } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import SQLite from 'react-native-sqlite-storage';
import { AudioFile, deleteAudioFile } from '../../../services/audioFileManageService';
import { useSorting } from '../../SortingContextProvider/SortingContextProvider';
import { FileFormat } from '../RequestService';
import useVoiceAnalyzeItemStyle from './VoiceAnalyzeItemStyle';
import { useVoiceEvaluationNavigation } from '../../../utils/useAppNavigation';

type VoiceAnalyzeItemProps = {
  audioFile: AudioFile;
};

const StatusEnum = {
  FAILED: 'failed',
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
};

const StatusColor = {
  [StatusEnum.FAILED]: '#FFE2E2',
  [StatusEnum.PENDING]: '#FAFABF',
  [StatusEnum.PROCESSING]: '#E6E8FB',
  [StatusEnum.COMPLETED]: '#E2FFEA',
};

const StatusTextColor = {
  [StatusEnum.FAILED]: '#FF0004',
  [StatusEnum.PENDING]: '#FFFF00',
  [StatusEnum.PROCESSING]: '#0022FF',
  [StatusEnum.COMPLETED]: '#00BA22',
};

const capitalizeFirstLetter = (status: string) => {
  return status.charAt(0).toUpperCase() + status.slice(1);
};

const StatusBadge = ({ status, styles }: { status: string; styles: any }) => {
  const capitalizeStatus = capitalizeFirstLetter(status);

  const getBackgroundColor = (status: string) => {
    return StatusColor[status.toLowerCase()] || '#f0f0f0';
  };

  const getTextColor = (status: string) => {
    return StatusTextColor[status.toLowerCase()] || '#333';
  };

  return (
    <View style={[styles.statusBadge, { backgroundColor: getBackgroundColor(status) }]}>
      <Text style={[styles.statusText, { color: getTextColor(status) }]}>
        {capitalizeStatus}
      </Text>
    </View>
  );
};

const formatDateFromName = (name: string) => {
  const dateMatch = name.match(/(\d{1,2})(st|nd|rd|th)\s+(\w+),\s+(\d{4})\s+(\d{2}):(\d{2})/);
  if (dateMatch) {
    const [, day, , month, year, hour, minute] = dateMatch;

    const hour24 = parseInt(hour);
    const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
    const ampm = hour24 >= 12 ? 'PM' : 'AM';

    return {
      date: `${month} ${day}th, ${year}`,
      time: `${hour12}:${minute} ${ampm}`
    };
  }
  return { date: 'Unknown Date', time: '9:00 AM' };
};

const VoiceAnalyzeItem: React.FC<VoiceAnalyzeItemProps> = ({ audioFile }) => {
  const { setAudioFiles } = useSorting();
  const navigation = useVoiceEvaluationNavigation();
  const [audio, setAudio] = useState<AudioFile>(audioFile);
  const [fileCs, setFileCs] = useState('');
  const [fileSv, setFileSv] = useState('');

  const handleDelete = async () => {
    await handleDeleteFile(audio);
  };

  const {
    styles,
    panGesture,
    rDeleteIconStyle,
    rStyle,
    bgAnimatedStyle,
    rTaskContainerStyle,
  } = useVoiceAnalyzeItemStyle({ handleDelete });

  useEffect(() => {
    setAudio(audioFile);

    // Decode audio files for playback
    (async () => {
      try {
        if (audio.filePath) {
          const fileCsPath = await decodeAudioFile(audio.filePath);
          if (fileCsPath) {
            setFileCs(fileCsPath);
          }
        }

        if (audio.fileSvPath) {
          const fileSvPath = await decodeAudioFile(audio.fileSvPath);
          if (fileSvPath) {
            setFileSv(fileSvPath);
          }
        }
      } catch (error) {
        console.error('Error decoding audio files:', error);
      }
    })();
  }, [audioFile]);

  const decodeAudioFile = async (base64String: string) => {
    try {
      if (!base64String || base64String === 'null') {
        console.log('No file data provided');
        return null;
      }

      let fileData: FileFormat;
      try {
        fileData = JSON.parse(base64String);
      } catch (parseError) {
        console.error('Error parsing file data JSON:', parseError);
        return null;
      }

      if (!fileData || !fileData.uri) {
        console.log('Invalid file data or missing URI');
        return null;
      }

      if (fileData.uri.startsWith('file://')) {
        return fileData.uri;
      }

      let base64Content = '';
      if (fileData.uri.includes('base64,')) {
        base64Content = fileData.uri.split('base64,')[1];
      } else {
        console.log('URI is not a base64 data URI, using as is');
        return fileData.uri;
      }

      const fileName = fileData.name || `audio_${Date.now()}.wav`;
      const filePath = `file://${RNFS.CachesDirectoryPath}/${fileName}`;

      await RNFS.writeFile(filePath, base64Content, 'base64');
      console.log('File successfully written:', filePath);
      return filePath;
    } catch (error) {
      console.error('Error writing file from base64:', error);
      return null;
    }
  };

  const handleDeleteFile = async (audioFile: AudioFile) => {
    try {
      const db = await SQLite.openDatabase({
        name: 'audioFiles.db',
        location: 'default',
      });
      await deleteAudioFile(db, audioFile, setAudioFiles);
    } catch (err) {
      console.error('Error deleting file:', err);
    }
  };

  const handleViewAnalysis = () => {
    navigation.navigate('VoiceAnalysis', {
      audioFile: audio,
      fileCs,
      fileSv
    });
  };

  const handleItemPress = () => {
    if (audio.status.toLowerCase() === 'completed') {
      handleViewAnalysis();
    }
  };

  const { date, time } = formatDateFromName(audio.name);

  return (
    <Animated.View style={[rTaskContainerStyle]}>
      <Animated.View style={[bgAnimatedStyle, { flex: 1 }]}>
        <Animated.View style={[styles.iconContainer, rDeleteIconStyle]}>
          <Trash size={25} color="white" />
        </Animated.View>

        <GestureDetector gesture={panGesture}>
          <Animated.View style={[rStyle]}>
            <TouchableOpacity
              style={styles.taskContainer}
              onPress={handleItemPress}
              disabled={audio.status.toLowerCase() !== 'completed'}
            >
              <View style={styles.leftContent}>
                <Text style={styles.dateText}>{date}</Text>
                <Text style={styles.timeText}>{time}</Text>
              </View>

              <View style={styles.rightContent}>
                <StatusBadge status={audio.status} styles={styles} />

                <ChevronRight
                  size={25}
                  color={audio.status.toLowerCase() === 'completed' ? "#4A90E2" : "#CCCCCC"}
                />
              </View>
            </TouchableOpacity>
          </Animated.View>
        </GestureDetector>
      </Animated.View>
    </Animated.View>
  );
};

export default VoiceAnalyzeItem;