import React, {useEffect, useState} from 'react';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useTheme} from 'react-native-paper';
import countryFlags from '../../../utils/language';
import {Phrase} from '../../../services/phraseManageService';

export const useVoiceAmplification = () => {
  const [showModal, setShowModal] = React.useState(false);
  const [currentTime, setCurrentTime] = useState('');

  const theme = useTheme();
  const [isListening, setIsListening] = useState(false);

  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const {iconImages} = countryFlags();

  const [playingPreview, setPlayingPreview] = useState<boolean>(false);
  const [currentPhrase, setCurrentPhrase] = useState<Phrase>();

  const [highlightWord, setHighlightWord] = useState<number>(-1);
  const [highlightedWordIndex, setHighlightWordIndex] = useState<number>(-1);

  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);

  const {
    phrases,
    sortOption,
    lessons,
    setPhrases,
    setExercises,
    setLessons,
    setLessonCount,
    setGraph,
    setToken,
  } = useSorting();

  useEffect(() => {
    setHighlightWordIndex(highlightedWordIndex + 1);
  }, [highlightWord]);

  const filteredPhrases = phrases.filter(phrase =>
    phrase.text.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const firstNotLikedIndex = phrases.findIndex(phrase => !phrase.isLiked);

  const handleShowModal = () => {
    setShowModal(true);
    setIsListening(true);
  };

  const handleDismissModal = () => {
    setShowModal(false);
    setIsListening(false);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date().toTimeString());
    }, 300000); // Update every minute

    return () => clearInterval(interval); // Cleanup on unmount
  }, []);

  useEffect(() => {
    setSearchQuery('');
    // setFilteredPhrases(phrases);
  }, [isSearching]);

  useEffect(() => {
    console.log('Playing preview: ', playingPreview);
  }, [playingPreview]);

  return {
    isSearching,
    setIsSearching,
    searchQuery,
    setSearchQuery,
    firstNotLikedIndex,
    filteredPhrases,
    currentTime,
    theme,
    isListening,
    setIsListening,
    handleDismissModal,
    handleShowModal,
    showModal,
    setShowModal,
    playingPreview,
    setPlayingPreview,
    iconImages,
    currentPhrase,
    setCurrentPhrase,
    setHighlightWord,
    setHighlightWordIndex,
    highlightedWordIndex,
    isSpeaking,
    setIsSpeaking,
  };
};
