"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGFuZGluZ19wYWdlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanM/Y2UyMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection auto */ // packages/react/collection/src/Collection.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => refs.forEach((ref) => setRef(ref, node));\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhDQUFpQjtBQUMxQjtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYW5kaW5nX3BhZ2UvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcz81ZTc5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2NvbXBvc2UtcmVmcy9zcmMvY29tcG9zZVJlZnMudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHNldFJlZihyZWYsIHZhbHVlKSB7XG4gIGlmICh0eXBlb2YgcmVmID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICByZWYodmFsdWUpO1xuICB9IGVsc2UgaWYgKHJlZiAhPT0gbnVsbCAmJiByZWYgIT09IHZvaWQgMCkge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH1cbn1cbmZ1bmN0aW9uIGNvbXBvc2VSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIChub2RlKSA9PiByZWZzLmZvckVhY2goKHJlZikgPT4gc2V0UmVmKHJlZiwgbm9kZSkpO1xufVxuZnVuY3Rpb24gdXNlQ29tcG9zZWRSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIFJlYWN0LnVzZUNhbGxiYWNrKGNvbXBvc2VSZWZzKC4uLnJlZnMpLCByZWZzKTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VSZWZzLFxuICB1c2VDb21wb3NlZFJlZnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  function Provider(props) {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  }\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  Provider.displayName = rootComponentName + \"Provider\";\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    function Provider(props) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    }\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    Provider.displayName = rootComponentName + \"Provider\";\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/Direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYW5kaW5nX3BhZ2UvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcz8yOWI0Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2RpcmVjdGlvbi9zcmMvRGlyZWN0aW9uLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBEaXJlY3Rpb25Db250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh2b2lkIDApO1xudmFyIERpcmVjdGlvblByb3ZpZGVyID0gKHByb3BzKSA9PiB7XG4gIGNvbnN0IHsgZGlyLCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KERpcmVjdGlvbkNvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IGRpciwgY2hpbGRyZW4gfSk7XG59O1xuZnVuY3Rpb24gdXNlRGlyZWN0aW9uKGxvY2FsRGlyKSB7XG4gIGNvbnN0IGdsb2JhbERpciA9IFJlYWN0LnVzZUNvbnRleHQoRGlyZWN0aW9uQ29udGV4dCk7XG4gIHJldHVybiBsb2NhbERpciB8fCBnbG9iYWxEaXIgfHwgXCJsdHJcIjtcbn1cbnZhciBQcm92aWRlciA9IERpcmVjdGlvblByb3ZpZGVyO1xuZXhwb3J0IHtcbiAgRGlyZWN0aW9uUHJvdmlkZXIsXG4gIFByb3ZpZGVyLFxuICB1c2VEaXJlY3Rpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // packages/react/dismissable-layer/src/DismissableLayer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2xhbmRpbmdfcGFnZS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtaWQvZGlzdC9pbmRleC5tanM/MGU5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9pZC9zcmMvaWQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbnZhciB1c2VSZWFjdElkID0gUmVhY3RbXCJ1c2VJZFwiLnRvU3RyaW5nKCldIHx8ICgoKSA9PiB2b2lkIDApO1xudmFyIGNvdW50ID0gMDtcbmZ1bmN0aW9uIHVzZUlkKGRldGVybWluaXN0aWNJZCkge1xuICBjb25zdCBbaWQsIHNldElkXSA9IFJlYWN0LnVzZVN0YXRlKHVzZVJlYWN0SWQoKSk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFkZXRlcm1pbmlzdGljSWQpIHNldElkKChyZWFjdElkKSA9PiByZWFjdElkID8/IFN0cmluZyhjb3VudCsrKSk7XG4gIH0sIFtkZXRlcm1pbmlzdGljSWRdKTtcbiAgcmV0dXJuIGRldGVybWluaXN0aWNJZCB8fCAoaWQgPyBgcmFkaXgtJHtpZH1gIDogXCJcIik7XG59XG5leHBvcnQge1xuICB1c2VJZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   List: () => (/* binding */ List),\n/* harmony export */   NavigationMenu: () => (/* binding */ NavigationMenu),\n/* harmony export */   NavigationMenuContent: () => (/* binding */ NavigationMenuContent),\n/* harmony export */   NavigationMenuIndicator: () => (/* binding */ NavigationMenuIndicator),\n/* harmony export */   NavigationMenuItem: () => (/* binding */ NavigationMenuItem),\n/* harmony export */   NavigationMenuLink: () => (/* binding */ NavigationMenuLink),\n/* harmony export */   NavigationMenuList: () => (/* binding */ NavigationMenuList),\n/* harmony export */   NavigationMenuSub: () => (/* binding */ NavigationMenuSub),\n/* harmony export */   NavigationMenuTrigger: () => (/* binding */ NavigationMenuTrigger),\n/* harmony export */   NavigationMenuViewport: () => (/* binding */ NavigationMenuViewport),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createNavigationMenuScope: () => (/* binding */ createNavigationMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Content,Indicator,Item,Link,List,NavigationMenu,NavigationMenuContent,NavigationMenuIndicator,NavigationMenuItem,NavigationMenuLink,NavigationMenuList,NavigationMenuSub,NavigationMenuTrigger,NavigationMenuViewport,Root,Sub,Trigger,Viewport,createNavigationMenuScope auto */ // packages/react/navigation-menu/src/NavigationMenu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar NAVIGATION_MENU_NAME = \"NavigationMenu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(NAVIGATION_MENU_NAME);\nvar [createNavigationMenuContext, createNavigationMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(NAVIGATION_MENU_NAME, [\n    createCollectionScope,\n    createFocusGroupCollectionScope\n]);\nvar [NavigationMenuProviderImpl, useNavigationMenuContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext(NAVIGATION_MENU_NAME);\nvar NavigationMenu = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, delayDuration = 200, skipDelayDuration = 300, orientation = \"horizontal\", dir, ...NavigationMenuProps } = props;\n    const [navigationMenu, setNavigationMenu] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node)=>setNavigationMenu(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const [value = \"\", setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: (value2)=>{\n            const isOpen = value2 !== \"\";\n            const hasSkipDelayDuration = skipDelayDuration > 0;\n            if (isOpen) {\n                window.clearTimeout(skipDelayTimerRef.current);\n                if (hasSkipDelayDuration) setIsOpenDelayed(false);\n            } else {\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout(()=>setIsOpenDelayed(true), skipDelayDuration);\n            }\n            onValueChange?.(value2);\n        },\n        defaultProp: defaultValue\n    });\n    const startCloseTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerRef.current = window.setTimeout(()=>setValue(\"\"), 150);\n    }, [\n        setValue\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>{\n        window.clearTimeout(closeTimerRef.current);\n        setValue(itemValue);\n    }, [\n        setValue\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((itemValue)=>{\n        const isOpenItem = value === itemValue;\n        if (isOpenItem) {\n            window.clearTimeout(closeTimerRef.current);\n        } else {\n            openTimerRef.current = window.setTimeout(()=>{\n                window.clearTimeout(closeTimerRef.current);\n                setValue(itemValue);\n            }, delayDuration);\n        }\n    }, [\n        value,\n        setValue,\n        delayDuration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            window.clearTimeout(openTimerRef.current);\n            window.clearTimeout(closeTimerRef.current);\n            window.clearTimeout(skipDelayTimerRef.current);\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: true,\n        value,\n        dir: direction,\n        orientation,\n        rootNavigationMenu: navigationMenu,\n        onTriggerEnter: (itemValue)=>{\n            window.clearTimeout(openTimerRef.current);\n            if (isOpenDelayed) handleDelayedOpen(itemValue);\n            else handleOpen(itemValue);\n        },\n        onTriggerLeave: ()=>{\n            window.clearTimeout(openTimerRef.current);\n            startCloseTimer();\n        },\n        onContentEnter: ()=>window.clearTimeout(closeTimerRef.current),\n        onContentLeave: startCloseTimer,\n        onItemSelect: (itemValue)=>{\n            setValue((prevValue)=>prevValue === itemValue ? \"\" : itemValue);\n        },\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.nav, {\n            \"aria-label\": \"Main\",\n            \"data-orientation\": orientation,\n            dir: direction,\n            ...NavigationMenuProps,\n            ref: composedRef\n        })\n    });\n});\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\nvar SUB_NAME = \"NavigationMenuSub\";\nvar NavigationMenuSub = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, onValueChange, defaultValue, orientation = \"horizontal\", ...subProps } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value = \"\", setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        onChange: onValueChange,\n        defaultProp: defaultValue\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProvider, {\n        scope: __scopeNavigationMenu,\n        isRootMenu: false,\n        value,\n        dir: context.dir,\n        orientation,\n        rootNavigationMenu: context.rootNavigationMenu,\n        onTriggerEnter: (itemValue)=>setValue(itemValue),\n        onItemSelect: (itemValue)=>setValue(itemValue),\n        onItemDismiss: ()=>setValue(\"\"),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            \"data-orientation\": orientation,\n            ...subProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuSub.displayName = SUB_NAME;\nvar NavigationMenuProvider = (props)=>{\n    const { scope, isRootMenu, rootNavigationMenu, dir, orientation, children, value, onItemSelect, onItemDismiss, onTriggerEnter, onTriggerLeave, onContentEnter, onContentLeave } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewportContent, setViewportContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Map());\n    const [indicatorTrack, setIndicatorTrack] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuProviderImpl, {\n        scope,\n        isRootMenu,\n        rootNavigationMenu,\n        value,\n        previousValue: (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_9__.usePrevious)(value),\n        baseId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)(),\n        dir,\n        orientation,\n        viewport,\n        onViewportChange: setViewport,\n        indicatorTrack,\n        onIndicatorTrackChange: setIndicatorTrack,\n        onTriggerEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerEnter),\n        onTriggerLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onTriggerLeave),\n        onContentEnter: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentEnter),\n        onContentLeave: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onContentLeave),\n        onItemSelect: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemSelect),\n        onItemDismiss: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onItemDismiss),\n        onViewportContentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((contentValue, contentData)=>{\n            setViewportContent((prevContent)=>{\n                prevContent.set(contentValue, contentData);\n                return new Map(prevContent);\n            });\n        }, []),\n        onViewportContentRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((contentValue)=>{\n            setViewportContent((prevContent)=>{\n                if (!prevContent.has(contentValue)) return prevContent;\n                prevContent.delete(contentValue);\n                return new Map(prevContent);\n            });\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n            scope,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentProvider, {\n                scope,\n                items: viewportContent,\n                children\n            })\n        })\n    });\n};\nvar LIST_NAME = \"NavigationMenuList\";\nvar NavigationMenuList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n    const list = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.ul, {\n        \"data-orientation\": context.orientation,\n        ...listProps,\n        ref: forwardedRef\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        style: {\n            position: \"relative\"\n        },\n        ref: context.onIndicatorTrackChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: context.isRootMenu ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n                asChild: true,\n                children: list\n            }) : list\n        })\n    });\n});\nNavigationMenuList.displayName = LIST_NAME;\nvar ITEM_NAME = \"NavigationMenuItem\";\nvar [NavigationMenuItemContextProvider, useNavigationMenuItemContext] = createNavigationMenuContext(ITEM_NAME);\nvar NavigationMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const value = valueProp || autoValue || \"LEGACY_REACT_AUTO_VALUE\";\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const focusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const restoreContentTabOrderRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    const wasEscapeCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleContentEntry = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((side = \"start\")=>{\n        if (contentRef.current) {\n            restoreContentTabOrderRef.current();\n            const candidates = getTabbableCandidates(contentRef.current);\n            if (candidates.length) focusFirst(side === \"start\" ? candidates : candidates.reverse());\n        }\n    }, []);\n    const handleContentExit = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (contentRef.current) {\n            const candidates = getTabbableCandidates(contentRef.current);\n            if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n        }\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuItemContextProvider, {\n        scope: __scopeNavigationMenu,\n        value,\n        triggerRef,\n        contentRef,\n        focusProxyRef,\n        wasEscapeCloseRef,\n        onEntryKeyDown: handleContentEntry,\n        onFocusProxyEnter: handleContentEntry,\n        onRootContentClose: handleContentExit,\n        onContentFocusOutside: handleContentExit,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.li, {\n            ...itemProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuItem.displayName = ITEM_NAME;\nvar TRIGGER_NAME = \"NavigationMenuTrigger\";\nvar NavigationMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n    const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, itemContext.triggerRef, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, itemContext.value);\n    const contentId = makeContentId(context.baseId, itemContext.value);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const wasClickCloseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const open = itemContext.value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                scope: __scopeNavigationMenu,\n                value: itemContext.value,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n                    asChild: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n                        id: triggerId,\n                        disabled,\n                        \"data-disabled\": disabled ? \"\" : void 0,\n                        \"data-state\": getOpenState(open),\n                        \"aria-expanded\": open,\n                        \"aria-controls\": contentId,\n                        ...triggerProps,\n                        ref: composedRefs,\n                        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, ()=>{\n                            wasClickCloseRef.current = false;\n                            itemContext.wasEscapeCloseRef.current = false;\n                        }),\n                        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse(()=>{\n                            if (disabled || wasClickCloseRef.current || itemContext.wasEscapeCloseRef.current || hasPointerMoveOpenedRef.current) return;\n                            context.onTriggerEnter(itemContext.value);\n                            hasPointerMoveOpenedRef.current = true;\n                        })),\n                        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(()=>{\n                            if (disabled) return;\n                            context.onTriggerLeave();\n                            hasPointerMoveOpenedRef.current = false;\n                        })),\n                        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, ()=>{\n                            context.onItemSelect(itemContext.value);\n                            wasClickCloseRef.current = open;\n                        }),\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                            const verticalEntryKey = context.dir === \"rtl\" ? \"ArrowLeft\" : \"ArrowRight\";\n                            const entryKey = {\n                                horizontal: \"ArrowDown\",\n                                vertical: verticalEntryKey\n                            }[context.orientation];\n                            if (open && event.key === entryKey) {\n                                itemContext.onEntryKeyDown();\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            }),\n            open && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        \"aria-hidden\": true,\n                        tabIndex: 0,\n                        ref: itemContext.focusProxyRef,\n                        onFocus: (event)=>{\n                            const content = itemContext.contentRef.current;\n                            const prevFocusedElement = event.relatedTarget;\n                            const wasTriggerFocused = prevFocusedElement === ref.current;\n                            const wasFocusFromContent = content?.contains(prevFocusedElement);\n                            if (wasTriggerFocused || !wasFocusFromContent) {\n                                itemContext.onFocusProxyEnter(wasTriggerFocused ? \"start\" : \"end\");\n                            }\n                        }\n                    }),\n                    context.viewport && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"span\", {\n                        \"aria-owns\": contentId\n                    })\n                ]\n            })\n        ]\n    });\n});\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\nvar LINK_NAME = \"NavigationMenuLink\";\nvar LINK_SELECT = \"navigationMenu.linkSelect\";\nvar NavigationMenuLink = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupItem, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.a, {\n            \"data-active\": active ? \"\" : void 0,\n            \"aria-current\": active ? \"page\" : void 0,\n            ...linkProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, (event)=>{\n                const target = event.target;\n                const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                target.addEventListener(LINK_SELECT, (event2)=>onSelect?.(event2), {\n                    once: true\n                });\n                (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, linkSelectEvent);\n                if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n                    const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                        bubbles: true,\n                        cancelable: true\n                    });\n                    (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.dispatchDiscreteCustomEvent)(target, rootContentDismissEvent);\n                }\n            }, {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nNavigationMenuLink.displayName = LINK_NAME;\nvar INDICATOR_NAME = \"NavigationMenuIndicator\";\nvar NavigationMenuIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n    const isVisible = Boolean(context.value);\n    return context.indicatorTrack ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || isVisible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuIndicatorImpl, {\n            ...indicatorProps,\n            ref: forwardedRef\n        })\n    }), context.indicatorTrack) : null;\n});\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\nvar NavigationMenuIndicatorImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...indicatorProps } = props;\n    const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const [activeTrigger, setActiveTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [position, setPosition] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isHorizontal = context.orientation === \"horizontal\";\n    const isVisible = Boolean(context.value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const items = getItems();\n        const triggerNode = items.find((item)=>item.value === context.value)?.ref.current;\n        if (triggerNode) setActiveTrigger(triggerNode);\n    }, [\n        getItems,\n        context.value\n    ]);\n    const handlePositionChange = ()=>{\n        if (activeTrigger) {\n            setPosition({\n                size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n                offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop\n            });\n        }\n    };\n    useResizeObserver(activeTrigger, handlePositionChange);\n    useResizeObserver(context.indicatorTrack, handlePositionChange);\n    return position ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"aria-hidden\": true,\n        \"data-state\": isVisible ? \"visible\" : \"hidden\",\n        \"data-orientation\": context.orientation,\n        ...indicatorProps,\n        ref: forwardedRef,\n        style: {\n            position: \"absolute\",\n            ...isHorizontal ? {\n                left: 0,\n                width: position.size + \"px\",\n                transform: `translateX(${position.offset}px)`\n            } : {\n                top: 0,\n                height: position.size + \"px\",\n                transform: `translateY(${position.offset}px)`\n            },\n            ...indicatorProps.style\n        }\n    }) : null;\n});\nvar CONTENT_NAME = \"NavigationMenuContent\";\nvar NavigationMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(itemContext.contentRef, forwardedRef);\n    const open = itemContext.value === context.value;\n    const commonProps = {\n        value: itemContext.value,\n        triggerRef: itemContext.triggerRef,\n        focusProxyRef: itemContext.focusProxyRef,\n        wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n        onContentFocusOutside: itemContext.onContentFocusOutside,\n        onRootContentClose: itemContext.onRootContentClose,\n        ...contentProps\n    };\n    return !context.viewport ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n            \"data-state\": getOpenState(open),\n            ...commonProps,\n            ref: composedRefs,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n            style: {\n                // Prevent interaction when animating out\n                pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n                ...commonProps.style\n            }\n        })\n    }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ViewportContentMounter, {\n        forceMount,\n        ...commonProps,\n        ref: composedRefs\n    });\n});\nNavigationMenuContent.displayName = CONTENT_NAME;\nvar ViewportContentMounter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const { onViewportContentChange, onViewportContentRemove } = context;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        onViewportContentChange(props.value, {\n            ref: forwardedRef,\n            ...props\n        });\n    }, [\n        props,\n        forwardedRef,\n        onViewportContentChange\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        return ()=>onViewportContentRemove(props.value);\n    }, [\n        props.value,\n        onViewportContentRemove\n    ]);\n    return null;\n});\nvar ROOT_CONTENT_DISMISS = \"navigationMenu.rootContentDismiss\";\nvar NavigationMenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, value, triggerRef, focusProxyRef, wasEscapeCloseRef, onRootContentClose, onContentFocusOutside, ...contentProps } = props;\n    const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(ref, forwardedRef);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const getItems = useCollection(__scopeNavigationMenu);\n    const prevMotionAttributeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { onItemDismiss } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = ref.current;\n        if (context.isRootMenu && content) {\n            const handleClose = ()=>{\n                onItemDismiss();\n                onRootContentClose();\n                if (content.contains(document.activeElement)) triggerRef.current?.focus();\n            };\n            content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n            return ()=>content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose);\n        }\n    }, [\n        context.isRootMenu,\n        props.value,\n        triggerRef,\n        onItemDismiss,\n        onRootContentClose\n    ]);\n    const motionAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const items = getItems();\n        const values = items.map((item)=>item.value);\n        if (context.dir === \"rtl\") values.reverse();\n        const index = values.indexOf(context.value);\n        const prevIndex = values.indexOf(context.previousValue);\n        const isSelected = value === context.value;\n        const wasSelected = prevIndex === values.indexOf(value);\n        if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n        const attribute = (()=>{\n            if (index !== prevIndex) {\n                if (isSelected && prevIndex !== -1) return index > prevIndex ? \"from-end\" : \"from-start\";\n                if (wasSelected && index !== -1) return index > prevIndex ? \"to-start\" : \"to-end\";\n            }\n            return null;\n        })();\n        prevMotionAttributeRef.current = attribute;\n        return attribute;\n    }, [\n        context.previousValue,\n        context.value,\n        context.dir,\n        getItems,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroup, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            id: contentId,\n            \"aria-labelledby\": triggerId,\n            \"data-motion\": motionAttribute,\n            \"data-orientation\": context.orientation,\n            ...contentProps,\n            ref: composedRefs,\n            disableOutsidePointerEvents: false,\n            onDismiss: ()=>{\n                const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n                    bubbles: true,\n                    cancelable: true\n                });\n                ref.current?.dispatchEvent(rootContentDismissEvent);\n            },\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                onContentFocusOutside();\n                const target = event.target;\n                if (context.rootNavigationMenu?.contains(target)) event.preventDefault();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                const target = event.target;\n                const isTrigger = getItems().some((item)=>item.ref.current?.contains(target));\n                const isRootViewport = context.isRootMenu && context.viewport?.contains(target);\n                if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const candidates = getTabbableCandidates(event.currentTarget);\n                    const focusedElement = document.activeElement;\n                    const index = candidates.findIndex((candidate)=>candidate === focusedElement);\n                    const isMovingBackwards = event.shiftKey;\n                    const nextCandidates = isMovingBackwards ? candidates.slice(0, index).reverse() : candidates.slice(index + 1, candidates.length);\n                    if (focusFirst(nextCandidates)) {\n                        event.preventDefault();\n                    } else {\n                        focusProxyRef.current?.focus();\n                    }\n                }\n            }),\n            onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                wasEscapeCloseRef.current = true;\n            })\n        })\n    });\n});\nvar VIEWPORT_NAME = \"NavigationMenuViewport\";\nvar NavigationMenuViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...viewportProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n    const open = Boolean(context.value);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuViewportImpl, {\n            ...viewportProps,\n            ref: forwardedRef\n        })\n    });\n});\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\nvar NavigationMenuViewportImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n    const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.onViewportChange);\n    const viewportContentContext = useViewportContentContext(CONTENT_NAME, props.__scopeNavigationMenu);\n    const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const viewportWidth = size ? size?.width + \"px\" : void 0;\n    const viewportHeight = size ? size?.height + \"px\" : void 0;\n    const open = Boolean(context.value);\n    const activeContentValue = open ? context.value : context.previousValue;\n    const handleSizeChange = ()=>{\n        if (content) setSize({\n            width: content.offsetWidth,\n            height: content.offsetHeight\n        });\n    };\n    useResizeObserver(content, handleSizeChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n        \"data-state\": getOpenState(open),\n        \"data-orientation\": context.orientation,\n        ...viewportImplProps,\n        ref: composedRefs,\n        style: {\n            // Prevent interaction when animating out\n            pointerEvents: !open && context.isRootMenu ? \"none\" : void 0,\n            [\"--radix-navigation-menu-viewport-width\"]: viewportWidth,\n            [\"--radix-navigation-menu-viewport-height\"]: viewportHeight,\n            ...viewportImplProps.style\n        },\n        onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerEnter, context.onContentEnter),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse(context.onContentLeave)),\n        children: Array.from(viewportContentContext.items).map(([value, { ref, forceMount, ...props2 }])=>{\n            const isActive = activeContentValue === value;\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_14__.Presence, {\n                present: forceMount || isActive,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(NavigationMenuContentImpl, {\n                    ...props2,\n                    ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.composeRefs)(ref, (node)=>{\n                        if (isActive && node) setContent(node);\n                    })\n                })\n            }, value);\n        })\n    });\n});\nvar FOCUS_GROUP_NAME = \"FocusGroup\";\nvar FocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Provider, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.Slot, {\n            scope: __scopeNavigationMenu,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n                dir: context.dir,\n                ...groupProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar ARROW_KEYS = [\n    \"ArrowRight\",\n    \"ArrowLeft\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar FOCUS_GROUP_ITEM_NAME = \"FocusGroupItem\";\nvar FocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusGroupCollection.ItemSlot, {\n        scope: __scopeNavigationMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.button, {\n            ...groupProps,\n            ref: forwardedRef,\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isFocusNavigationKey = [\n                    \"Home\",\n                    \"End\",\n                    ...ARROW_KEYS\n                ].includes(event.key);\n                if (isFocusNavigationKey) {\n                    let candidateNodes = getItems().map((item)=>item.ref.current);\n                    const prevItemKey = context.dir === \"rtl\" ? \"ArrowRight\" : \"ArrowLeft\";\n                    const prevKeys = [\n                        prevItemKey,\n                        \"ArrowUp\",\n                        \"End\"\n                    ];\n                    if (prevKeys.includes(event.key)) candidateNodes.reverse();\n                    if (ARROW_KEYS.includes(event.key)) {\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nfunction removeFromTabOrder(candidates) {\n    candidates.forEach((candidate)=>{\n        candidate.dataset.tabindex = candidate.getAttribute(\"tabindex\") || \"\";\n        candidate.setAttribute(\"tabindex\", \"-1\");\n    });\n    return ()=>{\n        candidates.forEach((candidate)=>{\n            const prevTabIndex = candidate.dataset.tabindex;\n            candidate.setAttribute(\"tabindex\", prevTabIndex);\n        });\n    };\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_15__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction makeTriggerId(baseId, value) {\n    return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n    return `${baseId}-content-${value}`;\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root2 = NavigationMenu;\nvar Sub = NavigationMenuSub;\nvar List = NavigationMenuList;\nvar Item = NavigationMenuItem;\nvar Trigger = NavigationMenuTrigger;\nvar Link = NavigationMenuLink;\nvar Indicator = NavigationMenuIndicator;\nvar Content = NavigationMenuContent;\nvar Viewport = NavigationMenuViewport;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ // packages/react/presence/src/Presence.tsx\n\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>{\n        if (node) {\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>send(\"ANIMATION_END\"));\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            if (node2) stylesRef.current = getComputedStyle(node2);\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByZXNlbmNlL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1QjtBQUNHO0FBQ007QUFDQTs7QUNIVDtBQVdoQixTQUFTSSxnQkFDZEMsWUFBQSxFQUNBQyxPQUFBO0lBRUEsT0FBYU4sNkNBQUEsQ0FBVyxDQUFDUSxPQUF3QkM7UUFDL0MsTUFBTUMsWUFBYUosT0FBQSxDQUFRRSxNQUFLLENBQVVDLE1BQUs7UUFDL0MsT0FBT0MsYUFBYUY7SUFDdEIsR0FBR0g7QUFDTDs7QURSQSxJQUFNTSxXQUFvQyxDQUFDQztJQUN6QyxNQUFNLEVBQUVDLE9BQUEsRUFBU0MsUUFBQSxFQUFTLEdBQUlGO0lBQzlCLE1BQU1HLFdBQVdDLFlBQVlIO0lBRTdCLE1BQU1JLFFBQ0osT0FBT0gsYUFBYSxhQUNoQkEsU0FBUztRQUFFRCxTQUFTRSxTQUFTRyxTQUFBO0lBQVUsS0FDakNDLDJDQUFBLENBQVNFLElBQUEsQ0FBS1A7SUFHMUIsTUFBTVEsTUFBTXBCLDZFQUFlQSxDQUFDYSxTQUFTTyxHQUFBLEVBQUtDLGNBQWNOO0lBQ3hELE1BQU1PLGFBQWEsT0FBT1YsYUFBYTtJQUN2QyxPQUFPVSxjQUFjVCxTQUFTRyxTQUFBLGlCQUFrQkMsK0NBQUEsQ0FBYUYsT0FBTztRQUFFSztJQUFJLEtBQUs7QUFDakY7QUFFQVgsU0FBU2UsV0FBQSxHQUFjO0FBTXZCLFNBQVNWLFlBQVlILE9BQUE7SUFDbkIsTUFBTSxDQUFDYyxNQUFNQyxRQUFPLEdBQVVULDJDQUFBO0lBQzlCLE1BQU1XLFlBQWtCWCx5Q0FBQSxDQUE0QixDQUFDO0lBQ3JELE1BQU1hLGlCQUF1QmIseUNBQUEsQ0FBT047SUFDcEMsTUFBTW9CLHVCQUE2QmQseUNBQUEsQ0FBZTtJQUNsRCxNQUFNZCxlQUFlUSxVQUFVLFlBQVk7SUFDM0MsTUFBTSxDQUFDTCxPQUFPMEIsS0FBSSxHQUFJOUIsZ0JBQWdCQyxjQUFjO1FBQ2xEOEIsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLGVBQWU7UUFDakI7UUFDQUMsa0JBQWtCO1lBQ2hCQyxPQUFPO1lBQ1BDLGVBQWU7UUFDakI7UUFDQUMsV0FBVztZQUNURixPQUFPO1FBQ1Q7SUFDRjtJQUVNcEIsNENBQUEsQ0FBVTtRQUNkLE1BQU13Qix1QkFBdUJDLGlCQUFpQmQsVUFBVWUsT0FBTztRQUMvRFoscUJBQXFCWSxPQUFBLEdBQVVyQyxVQUFVLFlBQVltQyx1QkFBdUI7SUFDOUUsR0FBRztRQUFDbkM7S0FBTTtJQUVWTCxrRkFBZUEsQ0FBQztRQUNkLE1BQU0yQyxTQUFTaEIsVUFBVWUsT0FBQTtRQUN6QixNQUFNRSxhQUFhZixlQUFlYSxPQUFBO1FBQ2xDLE1BQU1HLG9CQUFvQkQsZUFBZWxDO1FBRXpDLElBQUltQyxtQkFBbUI7WUFDckIsTUFBTUMsb0JBQW9CaEIscUJBQXFCWSxPQUFBO1lBQy9DLE1BQU1GLHVCQUF1QkMsaUJBQWlCRTtZQUU5QyxJQUFJakMsU0FBUztnQkFDWHFCLEtBQUs7WUFDUCxXQUFXUyx5QkFBeUIsVUFBVUcsUUFBUUksWUFBWSxRQUFRO2dCQUd4RWhCLEtBQUs7WUFDUCxPQUFPO2dCQU9MLE1BQU1pQixjQUFjRixzQkFBc0JOO2dCQUUxQyxJQUFJSSxjQUFjSSxhQUFhO29CQUM3QmpCLEtBQUs7Z0JBQ1AsT0FBTztvQkFDTEEsS0FBSztnQkFDUDtZQUNGO1lBRUFGLGVBQWVhLE9BQUEsR0FBVWhDO1FBQzNCO0lBQ0YsR0FBRztRQUFDQTtRQUFTcUI7S0FBSztJQUVsQi9CLGtGQUFlQSxDQUFDO1FBQ2QsSUFBSXdCLE1BQU07WUFNUixNQUFNeUIscUJBQXFCLENBQUMzQztnQkFDMUIsTUFBTWtDLHVCQUF1QkMsaUJBQWlCZCxVQUFVZSxPQUFPO2dCQUMvRCxNQUFNUSxxQkFBcUJWLHFCQUFxQlcsUUFBQSxDQUFTN0MsTUFBTThDLGFBQWE7Z0JBQzVFLElBQUk5QyxNQUFNK0MsTUFBQSxLQUFXN0IsUUFBUTBCLG9CQUFvQjtvQkFJdENwRCxnREFBQSxDQUFVLElBQU1pQyxLQUFLO2dCQUNoQztZQUNGO1lBQ0EsTUFBTXdCLHVCQUF1QixDQUFDakQ7Z0JBQzVCLElBQUlBLE1BQU0rQyxNQUFBLEtBQVc3QixNQUFNO29CQUV6Qk0scUJBQXFCWSxPQUFBLEdBQVVELGlCQUFpQmQsVUFBVWUsT0FBTztnQkFDbkU7WUFDRjtZQUNBbEIsS0FBS2dDLGdCQUFBLENBQWlCLGtCQUFrQkQ7WUFDeEMvQixLQUFLZ0MsZ0JBQUEsQ0FBaUIsbUJBQW1CUDtZQUN6Q3pCLEtBQUtnQyxnQkFBQSxDQUFpQixnQkFBZ0JQO1lBQ3RDLE9BQU87Z0JBQ0x6QixLQUFLaUMsbUJBQUEsQ0FBb0Isa0JBQWtCRjtnQkFDM0MvQixLQUFLaUMsbUJBQUEsQ0FBb0IsbUJBQW1CUjtnQkFDNUN6QixLQUFLaUMsbUJBQUEsQ0FBb0IsZ0JBQWdCUjtZQUMzQztRQUNGLE9BQU87WUFHTGxCLEtBQUs7UUFDUDtJQUNGLEdBQUc7UUFBQ1A7UUFBTU87S0FBSztJQUVmLE9BQU87UUFDTGhCLFdBQVc7WUFBQztZQUFXO1NBQWtCLENBQUVvQyxRQUFBLENBQVM5QztRQUNwRGMsS0FBV0gsOENBQUEsQ0FBWSxDQUFDUTtZQUN0QixJQUFJQSxPQUFNRyxVQUFVZSxPQUFBLEdBQVVpQixpQkFBaUJuQztZQUMvQ0MsUUFBUUQ7UUFDVixHQUFHLEVBQUU7SUFDUDtBQUNGO0FBSUEsU0FBU2lCLGlCQUFpQkUsTUFBQTtJQUN4QixPQUFPQSxRQUFRUyxpQkFBaUI7QUFDbEM7QUFPQSxTQUFTaEMsY0FBY3dDLE9BQUE7SUFFckIsSUFBSUMsU0FBU0MsT0FBT0Msd0JBQUEsQ0FBeUJILFFBQVFuRCxLQUFBLEVBQU8sUUFBUXVEO0lBQ3BFLElBQUlDLFVBQVVKLFVBQVUsb0JBQW9CQSxVQUFVQSxPQUFPSyxjQUFBO0lBQzdELElBQUlELFNBQVM7UUFDWCxPQUFRTCxRQUFnQnpDLEdBQUE7SUFDMUI7SUFHQTBDLFNBQVNDLE9BQU9DLHdCQUFBLENBQXlCSCxTQUFTLFFBQVFJO0lBQzFEQyxVQUFVSixVQUFVLG9CQUFvQkEsVUFBVUEsT0FBT0ssY0FBQTtJQUN6RCxJQUFJRCxTQUFTO1FBQ1gsT0FBT0wsUUFBUW5ELEtBQUEsQ0FBTVUsR0FBQTtJQUN2QjtJQUdBLE9BQU95QyxRQUFRbkQsS0FBQSxDQUFNVSxHQUFBLElBQVF5QyxRQUFnQnpDLEdBQUE7QUFDL0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYW5kaW5nX3BhZ2UvLi4vc3JjL1ByZXNlbmNlLnRzeD84YjM5Iiwid2VicGFjazovL2xhbmRpbmdfcGFnZS8uLi9zcmMvdXNlU3RhdGVNYWNoaW5lLnRzeD9hMzIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCAqIGFzIFJlYWN0RE9NIGZyb20gJ3JlYWN0LWRvbSc7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzJztcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdCc7XG5pbXBvcnQgeyB1c2VTdGF0ZU1hY2hpbmUgfSBmcm9tICcuL3VzZVN0YXRlTWFjaGluZSc7XG5cbmludGVyZmFjZSBQcmVzZW5jZVByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0RWxlbWVudCB8ICgocHJvcHM6IHsgcHJlc2VudDogYm9vbGVhbiB9KSA9PiBSZWFjdC5SZWFjdEVsZW1lbnQpO1xuICBwcmVzZW50OiBib29sZWFuO1xufVxuXG5jb25zdCBQcmVzZW5jZTogUmVhY3QuRkM8UHJlc2VuY2VQcm9wcz4gPSAocHJvcHMpID0+IHtcbiAgY29uc3QgeyBwcmVzZW50LCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIGNvbnN0IHByZXNlbmNlID0gdXNlUHJlc2VuY2UocHJlc2VudCk7XG5cbiAgY29uc3QgY2hpbGQgPSAoXG4gICAgdHlwZW9mIGNoaWxkcmVuID09PSAnZnVuY3Rpb24nXG4gICAgICA/IGNoaWxkcmVuKHsgcHJlc2VudDogcHJlc2VuY2UuaXNQcmVzZW50IH0pXG4gICAgICA6IFJlYWN0LkNoaWxkcmVuLm9ubHkoY2hpbGRyZW4pXG4gICkgYXMgUmVhY3QuUmVhY3RFbGVtZW50O1xuXG4gIGNvbnN0IHJlZiA9IHVzZUNvbXBvc2VkUmVmcyhwcmVzZW5jZS5yZWYsIGdldEVsZW1lbnRSZWYoY2hpbGQpKTtcbiAgY29uc3QgZm9yY2VNb3VudCA9IHR5cGVvZiBjaGlsZHJlbiA9PT0gJ2Z1bmN0aW9uJztcbiAgcmV0dXJuIGZvcmNlTW91bnQgfHwgcHJlc2VuY2UuaXNQcmVzZW50ID8gUmVhY3QuY2xvbmVFbGVtZW50KGNoaWxkLCB7IHJlZiB9KSA6IG51bGw7XG59O1xuXG5QcmVzZW5jZS5kaXNwbGF5TmFtZSA9ICdQcmVzZW5jZSc7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIHVzZVByZXNlbmNlXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmZ1bmN0aW9uIHVzZVByZXNlbmNlKHByZXNlbnQ6IGJvb2xlYW4pIHtcbiAgY29uc3QgW25vZGUsIHNldE5vZGVdID0gUmVhY3QudXNlU3RhdGU8SFRNTEVsZW1lbnQ+KCk7XG4gIGNvbnN0IHN0eWxlc1JlZiA9IFJlYWN0LnVzZVJlZjxDU1NTdHlsZURlY2xhcmF0aW9uPih7fSBhcyBhbnkpO1xuICBjb25zdCBwcmV2UHJlc2VudFJlZiA9IFJlYWN0LnVzZVJlZihwcmVzZW50KTtcbiAgY29uc3QgcHJldkFuaW1hdGlvbk5hbWVSZWYgPSBSZWFjdC51c2VSZWY8c3RyaW5nPignbm9uZScpO1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBwcmVzZW50ID8gJ21vdW50ZWQnIDogJ3VubW91bnRlZCc7XG4gIGNvbnN0IFtzdGF0ZSwgc2VuZF0gPSB1c2VTdGF0ZU1hY2hpbmUoaW5pdGlhbFN0YXRlLCB7XG4gICAgbW91bnRlZDoge1xuICAgICAgVU5NT1VOVDogJ3VubW91bnRlZCcsXG4gICAgICBBTklNQVRJT05fT1VUOiAndW5tb3VudFN1c3BlbmRlZCcsXG4gICAgfSxcbiAgICB1bm1vdW50U3VzcGVuZGVkOiB7XG4gICAgICBNT1VOVDogJ21vdW50ZWQnLFxuICAgICAgQU5JTUFUSU9OX0VORDogJ3VubW91bnRlZCcsXG4gICAgfSxcbiAgICB1bm1vdW50ZWQ6IHtcbiAgICAgIE1PVU5UOiAnbW91bnRlZCcsXG4gICAgfSxcbiAgfSk7XG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjdXJyZW50QW5pbWF0aW9uTmFtZSA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgIHByZXZBbmltYXRpb25OYW1lUmVmLmN1cnJlbnQgPSBzdGF0ZSA9PT0gJ21vdW50ZWQnID8gY3VycmVudEFuaW1hdGlvbk5hbWUgOiAnbm9uZSc7XG4gIH0sIFtzdGF0ZV0pO1xuXG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc3R5bGVzID0gc3R5bGVzUmVmLmN1cnJlbnQ7XG4gICAgY29uc3Qgd2FzUHJlc2VudCA9IHByZXZQcmVzZW50UmVmLmN1cnJlbnQ7XG4gICAgY29uc3QgaGFzUHJlc2VudENoYW5nZWQgPSB3YXNQcmVzZW50ICE9PSBwcmVzZW50O1xuXG4gICAgaWYgKGhhc1ByZXNlbnRDaGFuZ2VkKSB7XG4gICAgICBjb25zdCBwcmV2QW5pbWF0aW9uTmFtZSA9IHByZXZBbmltYXRpb25OYW1lUmVmLmN1cnJlbnQ7XG4gICAgICBjb25zdCBjdXJyZW50QW5pbWF0aW9uTmFtZSA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzKTtcblxuICAgICAgaWYgKHByZXNlbnQpIHtcbiAgICAgICAgc2VuZCgnTU9VTlQnKTtcbiAgICAgIH0gZWxzZSBpZiAoY3VycmVudEFuaW1hdGlvbk5hbWUgPT09ICdub25lJyB8fCBzdHlsZXM/LmRpc3BsYXkgPT09ICdub25lJykge1xuICAgICAgICAvLyBJZiB0aGVyZSBpcyBubyBleGl0IGFuaW1hdGlvbiBvciB0aGUgZWxlbWVudCBpcyBoaWRkZW4sIGFuaW1hdGlvbnMgd29uJ3QgcnVuXG4gICAgICAgIC8vIHNvIHdlIHVubW91bnQgaW5zdGFudGx5XG4gICAgICAgIHNlbmQoJ1VOTU9VTlQnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBXaGVuIGBwcmVzZW50YCBjaGFuZ2VzIHRvIGBmYWxzZWAsIHdlIGNoZWNrIGNoYW5nZXMgdG8gYW5pbWF0aW9uLW5hbWUgdG9cbiAgICAgICAgICogZGV0ZXJtaW5lIHdoZXRoZXIgYW4gYW5pbWF0aW9uIGhhcyBzdGFydGVkLiBXZSBjaG9zZSB0aGlzIGFwcHJvYWNoIChyZWFkaW5nXG4gICAgICAgICAqIGNvbXB1dGVkIHN0eWxlcykgYmVjYXVzZSB0aGVyZSBpcyBubyBgYW5pbWF0aW9ucnVuYCBldmVudCBhbmQgYGFuaW1hdGlvbnN0YXJ0YFxuICAgICAgICAgKiBmaXJlcyBhZnRlciBgYW5pbWF0aW9uLWRlbGF5YCBoYXMgZXhwaXJlZCB3aGljaCB3b3VsZCBiZSB0b28gbGF0ZS5cbiAgICAgICAgICovXG4gICAgICAgIGNvbnN0IGlzQW5pbWF0aW5nID0gcHJldkFuaW1hdGlvbk5hbWUgIT09IGN1cnJlbnRBbmltYXRpb25OYW1lO1xuXG4gICAgICAgIGlmICh3YXNQcmVzZW50ICYmIGlzQW5pbWF0aW5nKSB7XG4gICAgICAgICAgc2VuZCgnQU5JTUFUSU9OX09VVCcpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNlbmQoJ1VOTU9VTlQnKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBwcmV2UHJlc2VudFJlZi5jdXJyZW50ID0gcHJlc2VudDtcbiAgICB9XG4gIH0sIFtwcmVzZW50LCBzZW5kXSk7XG5cbiAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAobm9kZSkge1xuICAgICAgLyoqXG4gICAgICAgKiBUcmlnZ2VyaW5nIGFuIEFOSU1BVElPTl9PVVQgZHVyaW5nIGFuIEFOSU1BVElPTl9JTiB3aWxsIGZpcmUgYW4gYGFuaW1hdGlvbmNhbmNlbGBcbiAgICAgICAqIGV2ZW50IGZvciBBTklNQVRJT05fSU4gYWZ0ZXIgd2UgaGF2ZSBlbnRlcmVkIGB1bm1vdW50U3VzcGVuZGVkYCBzdGF0ZS4gU28sIHdlXG4gICAgICAgKiBtYWtlIHN1cmUgd2Ugb25seSB0cmlnZ2VyIEFOSU1BVElPTl9FTkQgZm9yIHRoZSBjdXJyZW50bHkgYWN0aXZlIGFuaW1hdGlvbi5cbiAgICAgICAqL1xuICAgICAgY29uc3QgaGFuZGxlQW5pbWF0aW9uRW5kID0gKGV2ZW50OiBBbmltYXRpb25FdmVudCkgPT4ge1xuICAgICAgICBjb25zdCBjdXJyZW50QW5pbWF0aW9uTmFtZSA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgICAgICBjb25zdCBpc0N1cnJlbnRBbmltYXRpb24gPSBjdXJyZW50QW5pbWF0aW9uTmFtZS5pbmNsdWRlcyhldmVudC5hbmltYXRpb25OYW1lKTtcbiAgICAgICAgaWYgKGV2ZW50LnRhcmdldCA9PT0gbm9kZSAmJiBpc0N1cnJlbnRBbmltYXRpb24pIHtcbiAgICAgICAgICAvLyBXaXRoIFJlYWN0IDE4IGNvbmN1cnJlbmN5IHRoaXMgdXBkYXRlIGlzIGFwcGxpZWRcbiAgICAgICAgICAvLyBhIGZyYW1lIGFmdGVyIHRoZSBhbmltYXRpb24gZW5kcywgY3JlYXRpbmcgYSBmbGFzaCBvZiB2aXNpYmxlIGNvbnRlbnQuXG4gICAgICAgICAgLy8gQnkgbWFudWFsbHkgZmx1c2hpbmcgd2UgZW5zdXJlIHRoZXkgc3luYyB3aXRoaW4gYSBmcmFtZSwgcmVtb3ZpbmcgdGhlIGZsYXNoLlxuICAgICAgICAgIFJlYWN0RE9NLmZsdXNoU3luYygoKSA9PiBzZW5kKCdBTklNQVRJT05fRU5EJykpO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgY29uc3QgaGFuZGxlQW5pbWF0aW9uU3RhcnQgPSAoZXZlbnQ6IEFuaW1hdGlvbkV2ZW50KSA9PiB7XG4gICAgICAgIGlmIChldmVudC50YXJnZXQgPT09IG5vZGUpIHtcbiAgICAgICAgICAvLyBpZiBhbmltYXRpb24gb2NjdXJyZWQsIHN0b3JlIGl0cyBuYW1lIGFzIHRoZSBwcmV2aW91cyBhbmltYXRpb24uXG4gICAgICAgICAgcHJldkFuaW1hdGlvbk5hbWVSZWYuY3VycmVudCA9IGdldEFuaW1hdGlvbk5hbWUoc3R5bGVzUmVmLmN1cnJlbnQpO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgbm9kZS5hZGRFdmVudExpc3RlbmVyKCdhbmltYXRpb25zdGFydCcsIGhhbmRsZUFuaW1hdGlvblN0YXJ0KTtcbiAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uY2FuY2VsJywgaGFuZGxlQW5pbWF0aW9uRW5kKTtcbiAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uZW5kJywgaGFuZGxlQW5pbWF0aW9uRW5kKTtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIG5vZGUucmVtb3ZlRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uc3RhcnQnLCBoYW5kbGVBbmltYXRpb25TdGFydCk7XG4gICAgICAgIG5vZGUucmVtb3ZlRXZlbnRMaXN0ZW5lcignYW5pbWF0aW9uY2FuY2VsJywgaGFuZGxlQW5pbWF0aW9uRW5kKTtcbiAgICAgICAgbm9kZS5yZW1vdmVFdmVudExpc3RlbmVyKCdhbmltYXRpb25lbmQnLCBoYW5kbGVBbmltYXRpb25FbmQpO1xuICAgICAgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gVHJhbnNpdGlvbiB0byB0aGUgdW5tb3VudGVkIHN0YXRlIGlmIHRoZSBub2RlIGlzIHJlbW92ZWQgcHJlbWF0dXJlbHkuXG4gICAgICAvLyBXZSBhdm9pZCBkb2luZyBzbyBkdXJpbmcgY2xlYW51cCBhcyB0aGUgbm9kZSBtYXkgY2hhbmdlIGJ1dCBzdGlsbCBleGlzdC5cbiAgICAgIHNlbmQoJ0FOSU1BVElPTl9FTkQnKTtcbiAgICB9XG4gIH0sIFtub2RlLCBzZW5kXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBpc1ByZXNlbnQ6IFsnbW91bnRlZCcsICd1bm1vdW50U3VzcGVuZGVkJ10uaW5jbHVkZXMoc3RhdGUpLFxuICAgIHJlZjogUmVhY3QudXNlQ2FsbGJhY2soKG5vZGU6IEhUTUxFbGVtZW50KSA9PiB7XG4gICAgICBpZiAobm9kZSkgc3R5bGVzUmVmLmN1cnJlbnQgPSBnZXRDb21wdXRlZFN0eWxlKG5vZGUpO1xuICAgICAgc2V0Tm9kZShub2RlKTtcbiAgICB9LCBbXSksXG4gIH07XG59XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuZnVuY3Rpb24gZ2V0QW5pbWF0aW9uTmFtZShzdHlsZXM/OiBDU1NTdHlsZURlY2xhcmF0aW9uKSB7XG4gIHJldHVybiBzdHlsZXM/LmFuaW1hdGlvbk5hbWUgfHwgJ25vbmUnO1xufVxuXG4vLyBCZWZvcmUgUmVhY3QgMTkgYWNjZXNzaW5nIGBlbGVtZW50LnByb3BzLnJlZmAgd2lsbCB0aHJvdyBhIHdhcm5pbmcgYW5kIHN1Z2dlc3QgdXNpbmcgYGVsZW1lbnQucmVmYFxuLy8gQWZ0ZXIgUmVhY3QgMTkgYWNjZXNzaW5nIGBlbGVtZW50LnJlZmAgZG9lcyB0aGUgb3Bwb3NpdGUuXG4vLyBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvcHVsbC8yODM0OFxuLy9cbi8vIEFjY2VzcyB0aGUgcmVmIHVzaW5nIHRoZSBtZXRob2QgdGhhdCBkb2Vzbid0IHlpZWxkIGEgd2FybmluZy5cbmZ1bmN0aW9uIGdldEVsZW1lbnRSZWYoZWxlbWVudDogUmVhY3QuUmVhY3RFbGVtZW50KSB7XG4gIC8vIFJlYWN0IDw9MTggaW4gREVWXG4gIGxldCBnZXR0ZXIgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGVsZW1lbnQucHJvcHMsICdyZWYnKT8uZ2V0O1xuICBsZXQgbWF5V2FybiA9IGdldHRlciAmJiAnaXNSZWFjdFdhcm5pbmcnIGluIGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmc7XG4gIGlmIChtYXlXYXJuKSB7XG4gICAgcmV0dXJuIChlbGVtZW50IGFzIGFueSkucmVmO1xuICB9XG5cbiAgLy8gUmVhY3QgMTkgaW4gREVWXG4gIGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoZWxlbWVudCwgJ3JlZicpPy5nZXQ7XG4gIG1heVdhcm4gPSBnZXR0ZXIgJiYgJ2lzUmVhY3RXYXJuaW5nJyBpbiBnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nO1xuICBpZiAobWF5V2Fybikge1xuICAgIHJldHVybiBlbGVtZW50LnByb3BzLnJlZjtcbiAgfVxuXG4gIC8vIE5vdCBERVZcbiAgcmV0dXJuIGVsZW1lbnQucHJvcHMucmVmIHx8IChlbGVtZW50IGFzIGFueSkucmVmO1xufVxuXG5leHBvcnQgeyBQcmVzZW5jZSB9O1xuZXhwb3J0IHR5cGUgeyBQcmVzZW5jZVByb3BzIH07XG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbnR5cGUgTWFjaGluZTxTPiA9IHsgW2s6IHN0cmluZ106IHsgW2s6IHN0cmluZ106IFMgfSB9O1xudHlwZSBNYWNoaW5lU3RhdGU8VD4gPSBrZXlvZiBUO1xudHlwZSBNYWNoaW5lRXZlbnQ8VD4gPSBrZXlvZiBVbmlvblRvSW50ZXJzZWN0aW9uPFRba2V5b2YgVF0+O1xuXG4vLyDwn6SvIGh0dHBzOi8vZmV0dGJsb2cuZXUvdHlwZXNjcmlwdC11bmlvbi10by1pbnRlcnNlY3Rpb24vXG50eXBlIFVuaW9uVG9JbnRlcnNlY3Rpb248VD4gPSAoVCBleHRlbmRzIGFueSA/ICh4OiBUKSA9PiBhbnkgOiBuZXZlcikgZXh0ZW5kcyAoeDogaW5mZXIgUikgPT4gYW55XG4gID8gUlxuICA6IG5ldmVyO1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlU3RhdGVNYWNoaW5lPE0+KFxuICBpbml0aWFsU3RhdGU6IE1hY2hpbmVTdGF0ZTxNPixcbiAgbWFjaGluZTogTSAmIE1hY2hpbmU8TWFjaGluZVN0YXRlPE0+PlxuKSB7XG4gIHJldHVybiBSZWFjdC51c2VSZWR1Y2VyKChzdGF0ZTogTWFjaGluZVN0YXRlPE0+LCBldmVudDogTWFjaGluZUV2ZW50PE0+KTogTWFjaGluZVN0YXRlPE0+ID0+IHtcbiAgICBjb25zdCBuZXh0U3RhdGUgPSAobWFjaGluZVtzdGF0ZV0gYXMgYW55KVtldmVudF07XG4gICAgcmV0dXJuIG5leHRTdGF0ZSA/PyBzdGF0ZTtcbiAgfSwgaW5pdGlhbFN0YXRlKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlJlYWN0RE9NIiwidXNlQ29tcG9zZWRSZWZzIiwidXNlTGF5b3V0RWZmZWN0IiwidXNlU3RhdGVNYWNoaW5lIiwiaW5pdGlhbFN0YXRlIiwibWFjaGluZSIsInVzZVJlZHVjZXIiLCJzdGF0ZSIsImV2ZW50IiwibmV4dFN0YXRlIiwiUHJlc2VuY2UiLCJwcm9wcyIsInByZXNlbnQiLCJjaGlsZHJlbiIsInByZXNlbmNlIiwidXNlUHJlc2VuY2UiLCJjaGlsZCIsImlzUHJlc2VudCIsIlJlYWN0MiIsIkNoaWxkcmVuIiwib25seSIsInJlZiIsImdldEVsZW1lbnRSZWYiLCJmb3JjZU1vdW50IiwiY2xvbmVFbGVtZW50IiwiZGlzcGxheU5hbWUiLCJub2RlIiwic2V0Tm9kZSIsInVzZVN0YXRlIiwic3R5bGVzUmVmIiwidXNlUmVmIiwicHJldlByZXNlbnRSZWYiLCJwcmV2QW5pbWF0aW9uTmFtZVJlZiIsInNlbmQiLCJtb3VudGVkIiwiVU5NT1VOVCIsIkFOSU1BVElPTl9PVVQiLCJ1bm1vdW50U3VzcGVuZGVkIiwiTU9VTlQiLCJBTklNQVRJT05fRU5EIiwidW5tb3VudGVkIiwidXNlRWZmZWN0IiwiY3VycmVudEFuaW1hdGlvbk5hbWUiLCJnZXRBbmltYXRpb25OYW1lIiwiY3VycmVudCIsInN0eWxlcyIsIndhc1ByZXNlbnQiLCJoYXNQcmVzZW50Q2hhbmdlZCIsInByZXZBbmltYXRpb25OYW1lIiwiZGlzcGxheSIsImlzQW5pbWF0aW5nIiwiaGFuZGxlQW5pbWF0aW9uRW5kIiwiaXNDdXJyZW50QW5pbWF0aW9uIiwiaW5jbHVkZXMiLCJhbmltYXRpb25OYW1lIiwidGFyZ2V0IiwiZmx1c2hTeW5jIiwiaGFuZGxlQW5pbWF0aW9uU3RhcnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInVzZUNhbGxiYWNrIiwiZ2V0Q29tcHV0ZWRTdHlsZSIsImVsZW1lbnQiLCJnZXR0ZXIiLCJPYmplY3QiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJnZXQiLCJtYXlXYXJuIiwiaXNSZWFjdFdhcm5pbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/primitive/src/Primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/useCallbackRef.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGFuZGluZ19wYWdlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzPzQ3MWMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlQ2FsbGJhY2tSZWYudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUNhbGxiYWNrUmVmKGNhbGxiYWNrKSB7XG4gIGNvbnN0IGNhbGxiYWNrUmVmID0gUmVhY3QudXNlUmVmKGNhbGxiYWNrKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjYWxsYmFja1JlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gY2FsbGJhY2tSZWYuY3VycmVudD8uKC4uLmFyZ3MpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VDYWxsYmFja1JlZlxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-controllable-state/src/useControllableState.tsx\n\n\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGFuZGluZ19wYWdlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZXNjYXBlLWtleWRvd24vZGlzdC9pbmRleC5tanM/NTIxYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtZXNjYXBlLWtleWRvd24vc3JjL3VzZUVzY2FwZUtleWRvd24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrUmVmIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmXCI7XG5mdW5jdGlvbiB1c2VFc2NhcGVLZXlkb3duKG9uRXNjYXBlS2V5RG93blByb3AsIG93bmVyRG9jdW1lbnQgPSBnbG9iYWxUaGlzPy5kb2N1bWVudCkge1xuICBjb25zdCBvbkVzY2FwZUtleURvd24gPSB1c2VDYWxsYmFja1JlZihvbkVzY2FwZUtleURvd25Qcm9wKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50KSA9PiB7XG4gICAgICBpZiAoZXZlbnQua2V5ID09PSBcIkVzY2FwZVwiKSB7XG4gICAgICAgIG9uRXNjYXBlS2V5RG93bihldmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgICBvd25lckRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24sIHsgY2FwdHVyZTogdHJ1ZSB9KTtcbiAgICByZXR1cm4gKCkgPT4gb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gIH0sIFtvbkVzY2FwZUtleURvd24sIG93bmVyRG9jdW1lbnRdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUVzY2FwZUtleWRvd25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/useLayoutEffect.tsx\n\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsdURBQXVELGtEQUFxQjtBQUM1RTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYW5kaW5nX3BhZ2UvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzPzE2NTQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZUxheW91dEVmZmVjdC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZUxheW91dEVmZmVjdDIgPSBCb29sZWFuKGdsb2JhbFRoaXM/LmRvY3VtZW50KSA/IFJlYWN0LnVzZUxheW91dEVmZmVjdCA6ICgpID0+IHtcbn07XG5leHBvcnQge1xuICB1c2VMYXlvdXRFZmZlY3QyIGFzIHVzZUxheW91dEVmZmVjdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/usePrevious.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0EsY0FBYyx5Q0FBWSxHQUFHLHdCQUF3QjtBQUNyRCxTQUFTLDBDQUFhO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGFuZGluZ19wYWdlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtcHJldmlvdXMvZGlzdC9pbmRleC5tanM/ODcyNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtcHJldmlvdXMvc3JjL3VzZVByZXZpb3VzLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VQcmV2aW91cyh2YWx1ZSkge1xuICBjb25zdCByZWYgPSBSZWFjdC51c2VSZWYoeyB2YWx1ZSwgcHJldmlvdXM6IHZhbHVlIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiB7XG4gICAgaWYgKHJlZi5jdXJyZW50LnZhbHVlICE9PSB2YWx1ZSkge1xuICAgICAgcmVmLmN1cnJlbnQucHJldmlvdXMgPSByZWYuY3VycmVudC52YWx1ZTtcbiAgICAgIHJlZi5jdXJyZW50LnZhbHVlID0gdmFsdWU7XG4gICAgfVxuICAgIHJldHVybiByZWYuY3VycmVudC5wcmV2aW91cztcbiAgfSwgW3ZhbHVlXSk7XG59XG5leHBvcnQge1xuICB1c2VQcmV2aW91c1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/visually-hidden/src/VisuallyHidden.tsx\n\n\n\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: {\n          // See: https://github.com/twbs/bootstrap/blob/master/scss/mixins/_screen-reader.scss\n          position: \"absolute\",\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: \"hidden\",\n          clip: \"rect(0, 0, 0, 0)\",\n          whiteSpace: \"nowrap\",\n          wordWrap: \"normal\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ3VCO0FBQ2Q7QUFDeEM7QUFDQSxxQkFBcUIsNkNBQWdCO0FBQ3JDO0FBQ0EsMkJBQTJCLHNEQUFHO0FBQzlCLE1BQU0sZ0VBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBSUU7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2xhbmRpbmdfcGFnZS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdmlzdWFsbHktaGlkZGVuL2Rpc3QvaW5kZXgubWpzPzUyYzgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdmlzdWFsbHktaGlkZGVuL3NyYy9WaXN1YWxseUhpZGRlbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1wcmltaXRpdmVcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIE5BTUUgPSBcIlZpc3VhbGx5SGlkZGVuXCI7XG52YXIgVmlzdWFsbHlIaWRkZW4gPSBSZWFjdC5mb3J3YXJkUmVmKFxuICAocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgUHJpbWl0aXZlLnNwYW4sXG4gICAgICB7XG4gICAgICAgIC4uLnByb3BzLFxuICAgICAgICByZWY6IGZvcndhcmRlZFJlZixcbiAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS90d2JzL2Jvb3RzdHJhcC9ibG9iL21hc3Rlci9zY3NzL21peGlucy9fc2NyZWVuLXJlYWRlci5zY3NzXG4gICAgICAgICAgcG9zaXRpb246IFwiYWJzb2x1dGVcIixcbiAgICAgICAgICBib3JkZXI6IDAsXG4gICAgICAgICAgd2lkdGg6IDEsXG4gICAgICAgICAgaGVpZ2h0OiAxLFxuICAgICAgICAgIHBhZGRpbmc6IDAsXG4gICAgICAgICAgbWFyZ2luOiAtMSxcbiAgICAgICAgICBvdmVyZmxvdzogXCJoaWRkZW5cIixcbiAgICAgICAgICBjbGlwOiBcInJlY3QoMCwgMCwgMCwgMClcIixcbiAgICAgICAgICB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiLFxuICAgICAgICAgIHdvcmRXcmFwOiBcIm5vcm1hbFwiLFxuICAgICAgICAgIC4uLnByb3BzLnN0eWxlXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICApO1xuICB9XG4pO1xuVmlzdWFsbHlIaWRkZW4uZGlzcGxheU5hbWUgPSBOQU1FO1xudmFyIFJvb3QgPSBWaXN1YWxseUhpZGRlbjtcbmV4cG9ydCB7XG4gIFJvb3QsXG4gIFZpc3VhbGx5SGlkZGVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Content: () => (/* binding */ e0),
/* harmony export */   Indicator: () => (/* binding */ e1),
/* harmony export */   Item: () => (/* binding */ e2),
/* harmony export */   Link: () => (/* binding */ e3),
/* harmony export */   List: () => (/* binding */ e4),
/* harmony export */   NavigationMenu: () => (/* binding */ e5),
/* harmony export */   NavigationMenuContent: () => (/* binding */ e6),
/* harmony export */   NavigationMenuIndicator: () => (/* binding */ e7),
/* harmony export */   NavigationMenuItem: () => (/* binding */ e8),
/* harmony export */   NavigationMenuLink: () => (/* binding */ e9),
/* harmony export */   NavigationMenuList: () => (/* binding */ e10),
/* harmony export */   NavigationMenuSub: () => (/* binding */ e11),
/* harmony export */   NavigationMenuTrigger: () => (/* binding */ e12),
/* harmony export */   NavigationMenuViewport: () => (/* binding */ e13),
/* harmony export */   Root: () => (/* binding */ e14),
/* harmony export */   Sub: () => (/* binding */ e15),
/* harmony export */   Trigger: () => (/* binding */ e16),
/* harmony export */   Viewport: () => (/* binding */ e17),
/* harmony export */   createNavigationMenuScope: () => (/* binding */ e18)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Content`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Indicator`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Item`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Link`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#List`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenu`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuContent`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuIndicator`);

const e8 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuItem`);

const e9 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuLink`);

const e10 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuList`);

const e11 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuSub`);

const e12 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuTrigger`);

const e13 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#NavigationMenuViewport`);

const e14 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Root`);

const e15 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Sub`);

const e16 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Trigger`);

const e17 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#Viewport`);

const e18 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs#createNavigationMenuScope`);


/***/ })

};
;