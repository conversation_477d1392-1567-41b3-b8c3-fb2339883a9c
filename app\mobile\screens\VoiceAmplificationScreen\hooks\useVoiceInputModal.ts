import Voice, {
  SpeechErrorEvent,
  SpeechRecognizedEvent,
  SpeechResultsEvent,
} from '@react-native-voice/voice';
import {Dispatch, SetStateAction, useEffect, useState} from 'react';
import {Alert, Platform} from 'react-native';
import {
  androidPermissionChecking,
  checkMicrophonePermission,
} from '../../../utils/checkMicPermission';
import {useTheme} from 'react-native-paper';
import SQLite from 'react-native-sqlite-storage';
import {insertPhrase} from '../../../services/phraseManageService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';

SQLite.DEBUG(false);
SQLite.enablePromise(true);

export const useVoiceInputModal = (
  setShowModal: Dispatch<SetStateAction<boolean>>,
  setIsListening?: Dispatch<SetStateAction<boolean>>,
  isListening?: boolean,
) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isDisabled, setIsDisabled] = useState(true);
  const [message, setMessage] = useState('');
  const [isSpeaking, setIsSpeaking] = useState(false);
  const theme = useTheme();
  const {setPhrases, sortOption, language} = useSorting();
  useEffect(() => {
    Voice.onSpeechStart = onSpeechStart;
    Voice.onSpeechRecognized = onSpeechRecognized;
    Voice.onSpeechEnd = onSpeechEnd;
    Voice.onSpeechError = onSpeechError;
    Voice.onSpeechResults = onSpeechResults;
    Voice.onSpeechPartialResults = onSpeechPartialResults;
    Voice.onSpeechVolumeChanged = onSpeechVolumeChange;

    if (Platform.OS === 'android') {
      androidPermissionChecking();
    }

    if (isListening) {
      _startRecognizing();
    }

    checkMicrophonePermission(setIsListening);

    return () => {
      Voice.destroy().then(Voice.removeAllListeners);
    };
  }, []);

  const onSpeechStart = (e: any) => {
    console.log('onSpeechStart', e);
  };

  const onSpeechRecognized = (e: SpeechRecognizedEvent) => {
    console.log('onSpeechRecognized: ', e);
  };

  const onSpeechEnd = (e: any) => {
    console.log('onSpeechEnd: ', e);
    Voice.removeAllListeners();
    setIsListening && setIsListening(false);
  };

  const onSpeechError = (e: SpeechErrorEvent) => {
    console.log('onSpeechError: ', e);
    Voice.removeAllListeners();
    setIsListening && setIsListening(false);
    setIsDisabled(true);

    if (e.error?.message === 'User denied access to speech recognition') {
      // Prompt the user to grant permission again
      Alert.alert(
        'Permission required',
        'You need to grant permission to use speech recognition.',
        [{text: 'OK'}],
        {cancelable: false},
      );
    }
  };

  const onSpeechVolumeChange = (e: any) => {
    // console.log('onSpeechVolumeChange: ', e);
  };

  const onSpeechResults = (e: SpeechResultsEvent) => {
    console.log('onSpeechResults: ', e);
    setMessage(e.value?.[0] || ''); //Use the first guess instead of joining them
  };

  const onSpeechPartialResults = (e: SpeechResultsEvent) => {
    console.log('onSpeechPartialResults: ', e);
  };

  const _startRecognizing = async () => {
    if (isListening) {
      await _stopRecognizing();
    }
    setMessage('');
    setIsListening && setIsListening(true);
    console.log('Listening set to true');
    setIsDisabled(true);
    setIsFocused(true);
    try {
      await Voice.start(language.voiceCode);
    } catch (e) {
      console.error('error', e);
      setIsListening && setIsListening(false);
      setIsDisabled(true);
    }
  };

  const _stopRecognizing = async () => {
    try {
      await Voice.stop();
      setIsListening && setIsListening(false);
      console.log('Listening set to false');
      setIsDisabled(false);
    } catch (e) {
      console.error('error', e);
      setIsListening && setIsListening(false);
      setIsDisabled(true);
    }
  };

  const handleSaveSentence = async () => {
    if (message.trim() !== '' && message !== null) {
      const db = await SQLite.openDatabase({
        name: 'phrases.db',
        location: 'default',
      });
      if (setPhrases) {
        await insertPhrase(db, message, setPhrases, sortOption, language.code);
        setTimeout(() => {
          setShowModal(false);
        }, 100);
        // await _destroyRecognizer();
      }
    } else {
      Alert.alert(
        'Message is empty',
        'Please provide message before saving.',
        [{text: 'OK'}],
        {cancelable: false},
      );
      await _stopRecognizing();
    }
  };

  const handleIsValidToSpeak = isDisabled || message.trim() === '';

  return {
    isSpeaking,
    setIsSpeaking,
    message,
    setMessage,
    isFocused,
    _startRecognizing,
    _stopRecognizing,
    handleSaveSentence,
    theme,
  };
};
