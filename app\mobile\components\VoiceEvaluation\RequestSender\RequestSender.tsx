import { Check, Pause, Play, RotateCcw } from 'lucide-react-native';
import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import { Platform, View } from 'react-native';
import { <PERSON><PERSON>, Card, IconButton, Text, useTheme } from 'react-native-paper';
import SQLite from 'react-native-sqlite-storage';
import LinearGradient from 'react-native-linear-gradient';
import ShareFileButton from '../ShareFile/ShareFileButton';

import { StaticWaveForm } from '../AudioRecorder/AudioRecorder';
import useRequestSenderStyle from './RequestSenderStyle';
import audioPlayerManager from '../../../services/audioPlayerManager';
SQLite.DEBUG(false);
SQLite.enablePromise(true);

type RequestSenderProps = {
  filePath: string;
  filePathTemp: string;
  setCurrentPosition?: Dispatch<SetStateAction<number>>;
  title: string;
  children?: React.ReactNode;
};
const RequestSender: React.FC<RequestSenderProps> = ({
  filePath,
  filePathTemp,
  setCurrentPosition,
  title,
  children,
}) => {
  const style = useRequestSenderStyle();
  const theme = useTheme();

  // Generate a unique ID for this component instance
  const componentId = `${title}-${filePathTemp}`;

  // Track playback state
  const [isPlaying, setIsplaying] = useState(false);
  const [playbackTime, setPlaybackTime] = useState('0:00');
  const [currentTime, setCurrentTime] = useState(0);

  // Format time for display (MM:SS format)
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${String(secs).padStart(2, '0')}`;
  };

  // Subscribe to audio player state changes
  useEffect(() => {
    setIsplaying(audioPlayerManager.isPlaying(componentId));

    // Subscribe to play state changes
    const unsubscribe = audioPlayerManager.onPlayStateChanged(({ id, isPlaying }) => {
      if (id === componentId) {
        setIsplaying(isPlaying);
      } else if (isPlaying) {
        setIsplaying(false);
      }
    });

    // Clean up subscription on unmount
    return () => {
      unsubscribe();
    };
  }, [componentId]);

  // Set up playback time tracking
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isPlaying) {
      interval = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = prev + 0.1;
          setPlaybackTime(formatTime(newTime));
          return newTime;
        });
      }, 100);
    } else if (interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isPlaying]);

  const onStartPlay = async () => {
    try {
      if (!filePathTemp) {
        console.error('RequestSender: Cannot play - file path is empty');
        return;
      }

      console.log(`RequestSender (${title}): Starting playback of ${filePathTemp}`);

      // Reset playback time when starting
      setCurrentTime(0);
      setPlaybackTime('0:00');

      if (Platform.OS === 'ios') {
        const cleanPath = filePathTemp.replace('file://', '');
        console.log(`RequestSender (${title}): Cleaned path for iOS: ${cleanPath}`);

        await new Promise(resolve => setTimeout(resolve, 100));
      }

      await audioPlayerManager.playAudio(filePathTemp, componentId);
    } catch (error) {
      console.error(`RequestSender (${title}): Error in onStartPlay:`, error);
      setIsplaying(false);
    }
  };

  const onStopPlay = async () => {
    try {
      console.log(`RequestSender (${title}): Stopping playback`);

      if (audioPlayerManager.isPlaying(componentId)) {
        await audioPlayerManager.stopAudio();
      }
    } catch (error) {
      console.error(`RequestSender (${title}): Error in onStopPlay:`, error);
      setIsplaying(false);
    }
  };

  const onRecordAgain = () => {
    if (title === 'Sustained Vowel') {
      setCurrentPosition && setCurrentPosition(1);
    } else {
      setCurrentPosition && setCurrentPosition(0);
    }
  };

  return (
    <View style={style.mainContainer}>
      <LinearGradient
        colors={['#e5f5f6', '#e2ecfc']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={style.CardStyle}>
        <View style={style.titleStyle}>
          <Check size={25} color={theme.colors.primary} style={{ marginRight: 10 }} />
          <Text style={style.textStyle}>{title}</Text>
        </View>

        <LinearGradient
          colors={['#3C80F3', '#4EC3C7']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={style.waveFormContainer}>
          {/* Inner white container for waveform and play button */}
          <View style={style.innerWaveFormContainer}>
            <View style={style.playButtonContainer}>
              <View style={style.playButton}>
                <IconButton
                  style={{ margin: 0 }}
                  icon={() => isPlaying ?
                    <Pause size={20} color={'white'} fill={'white'} /> :
                    <Play size={20} color={'white'} fill={'white'} />
                  }
                  size={20}
                  onPress={() => (isPlaying ? onStopPlay() : onStartPlay())}
                />
              </View>
              <View style={style.waveFormAndTimeContainer}>
                <View style={{ flex: 1, height: 40, justifyContent: 'center' }}>
                  <StaticWaveForm path={filePath} />
                </View>
                <Text style={style.timeText}>{playbackTime}</Text>
              </View>
            </View>
          </View>

          {/* Bottom buttons */}
          <View style={style.bottomButtonsContainer}>
            {children ? (
              children
            ) : (
              <View style={{ flexDirection: 'row', justifyContent: 'space-around', width: '50%' }}>
                <ShareFileButton filePath={filePathTemp} iconColor="white" />
                <RotateCcw
                  size={25}
                  color="white"
                  onPress={onRecordAgain}
                />
              </View>
            )}
          </View>
        </LinearGradient>
      </LinearGradient>
    </View >
  );
};

export default RequestSender;