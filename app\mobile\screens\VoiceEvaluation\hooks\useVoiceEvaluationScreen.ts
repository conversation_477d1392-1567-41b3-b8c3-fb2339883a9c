import {Dispatch, SetStateAction, useEffect} from 'react';
import {AUTH_PASSWORD, AUTH_USERNAME, DB_VERSION} from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AudioFile,
  createNewAudioTable,
  createTable,
  fetchAudioFiles,
} from '../../../services/audioFileManageService';
import AuthService from '../../../services/authService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import SQLite from 'react-native-sqlite-storage';
import {useCameraPermission} from 'react-native-vision-camera';
import {useTheme} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {VoiceEvaluationStackParamList} from '../../Navigation/VoiceEvaluationStack';

const initialData = async (
  setAudioFiles: Dispatch<SetStateAction<AudioFile[]>>,
) => {
  const db = await SQLite.openDatabase({
    name: 'audioFiles.db',
    location: 'default',
  });

  const isInserted = await AsyncStorage.getItem('@isInserted');

  if (isInserted === 'true') {
    const versions = await db.executeSql('PRAGMA user_version;');
    const currentVersion = versions[0].rows.item(0).user_version;
    if (currentVersion < DB_VERSION) {
      await createNewAudioTable(db);
    }
    await db.executeSql(`PRAGMA user_version = ${DB_VERSION};`);
  }

  await createTable(db);
  await fetchAudioFiles(db, setAudioFiles);
};

export const useVoiceEvaluationScreen = () => {
  const theme = useTheme();

  const {hasPermission, requestPermission} = useCameraPermission();

  const {audioFiles, setAudioFiles, setToken, token} = useSorting();

  useEffect(() => {
    (async () => {
      await initialData(setAudioFiles);
      await AuthService().getAccessToken(setToken);
      console.log('[Token]', Date.now(), token, AUTH_PASSWORD, AUTH_USERNAME);
    })();
  }, []);

  const navigation = useNavigation<NativeStackNavigationProp<VoiceEvaluationStackParamList>>();

  const handleNavigateToRecordAudio = async () => {
    if (hasPermission) {
      navigation.navigate('RecordAudio');
    } else {
      await requestPermission();
      navigation.navigate('RecordAudio');
    }
  };

  return {
    audioFiles,
    theme,
    handleNavigateToRecordAudio,
  };
};
