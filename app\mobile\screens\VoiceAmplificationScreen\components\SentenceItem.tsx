import {<PERSON>, <PERSON>Off, Trash} from 'lucide-react-native';
import React, {Dispatch, SetStateAction} from 'react';
import {Image, View} from 'react-native';
import {IconButton, Text} from 'react-native-paper';
import {Phrase} from '../../../services/phraseManageService';
import SpeakButton from '../../../components/SpeakButton/SpeakButton';
import {GestureDetector} from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';

import useSentenceItem from '../hooks/useSentenceItem';
import SentenceItemStyle from '../styles/SentenceItemStyle';
import LanguageSelector from '../../../components/LanguageSelector/LanguageSelector';

type SentenceItemProps = {
  phrase: Phrase;
  currentTime: string;
  setPlayingPreview: Dispatch<SetStateAction<boolean>>;
  setHighlightWord: Dispatch<SetStateAction<number>>;
  setHighlightWordIndex: Dispatch<SetStateAction<number>>;
  isSpeaking: boolean;
  setIsSpeaking: Dispatch<SetStateAction<boolean>>;
  setCurrentPhrase: () => void;
};

const SentenceItem: React.FC<SentenceItemProps> = ({
  phrase,
  currentTime,
  setPlayingPreview,
  setHighlightWord,
  setHighlightWordIndex,
  isSpeaking,
  setIsSpeaking,
  setCurrentPhrase,
}) => {
  const {
    handleDelete,
    handleUpdate,
    handleChangeLanguage,
    handleUpdateLastUsed,
    text,
    lastUsed,
    iconImages,
    languageCode,
    changeLanguage,
    setChangeLanguage,
  } = useSentenceItem(phrase, currentTime, setPlayingPreview, isSpeaking);

  const {
    rTaskContainerStyle,
    panGesture,
    bgAnimatedStyle,
    rFavoriteIconStyle,
    rDeleteIconStyle,
    rStyle,
    styles,
  } = SentenceItemStyle({
    handleDelete,
    handleUpdate,
  });

  return (
    <>
      <Animated.View style={rTaskContainerStyle}>
        <View
          style={{
            display: 'flex',
            width: '100%',
            height: '100%',
            position: 'absolute',
          }}>
          <Animated.View
            style={[styles.swipeBg, bgAnimatedStyle]}></Animated.View>
        </View>

        <Animated.View style={[styles.favoriteIcon, rFavoriteIconStyle]}>
          {!phrase.isLiked ? (
            <Heart size={24} color={'white'} />
          ) : (
            <HeartOff size={24} color={'white'} />
          )}
        </Animated.View>
        <Animated.View style={[styles.deleteIcon, rDeleteIconStyle]}>
          <Trash size={24} color={'white'} />
        </Animated.View>

        <GestureDetector gesture={panGesture}>
          <Animated.View style={[styles.mainContainer, rStyle]}>
            <View style={styles.textContainer}>
              <View>
                <Text variant="bodyLarge">{text}</Text>
                <Text>
                  {lastUsed !== 'Invalid date' ? lastUsed : 'Never used'}
                </Text>
              </View>
            </View>
            <View style={styles.actionContainer}>
              <IconButton
                icon={() => (
                  <Image
                    source={iconImages[languageCode as keyof typeof iconImages]}
                    style={{width: 28, height: 28}}
                  />
                )}
                size={15}
                style={{borderWidth: 0, backgroundColor: 'transparent'}}
                onPress={() => setChangeLanguage(!changeLanguage)}
              />

              <View style={styles.divider} />

              <SpeakButton
                text={text}
                isSpeaking={isSpeaking}
                setIsSpeaking={setIsSpeaking}
                languageCode={languageCode}
                updateLastUsed={handleUpdateLastUsed}
                setHighlightWord={setHighlightWord}
                setHighlightWordIndex={setHighlightWordIndex}
                size={30}
                setCurrentPhrase={setCurrentPhrase}
              />
            </View>
          </Animated.View>
        </GestureDetector>
      </Animated.View>

      <LanguageSelector
        languageCode={languageCode}
        handleChangeLanguage={handleChangeLanguage}
        showLanguage={changeLanguage}
        setShowLanguage={setChangeLanguage}
      />
    </>
  );
};

export default SentenceItem;
