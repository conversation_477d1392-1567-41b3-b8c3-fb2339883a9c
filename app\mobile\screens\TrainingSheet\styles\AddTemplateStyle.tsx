import {StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';

export const AddTemplateStyle = () => {
  const theme = useTheme();

  return StyleSheet.create({
    modalContainer: {
      width: '100%',
      margin: 0,
      backgroundColor: 'white',
    },
    scrollView: {
      backgroundColor: 'white',
      height: '100%',
      width: '100%',
      padding: 10,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      flexWrap: 'wrap',
    },

    card: {
      width: 180,
      height: 120,
      display: 'flex',
      justifyContent: 'center',
      marginBottom: 20,
      backgroundColor: theme.colors.background,
    },
    cardContent: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 10,
    },
    button: {
      flex: 1,

      alignItems: 'flex-start',
    },
    buttonText: {
      fontWeight: 'bold',
      fontSize: 16,
      color: theme.colors.primary,
    },
    bottomSheet: {
      width: '100%',
      rowGap: 10,
      padding: 10,
      position: 'relative',
      paddingBottom: 130,
    },
    buttonSheetContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
      position: 'absolute',
      bottom: 150,
      left: '32%',
    },
    input: {
      textAlignVertical: 'top',
      fontSize: 14,
      width: '90%',
      height: 45,
      left: 15,
    },
    backButton: {
      position: 'absolute',
      top: -10,
      left: -5,
      zIndex: 1,
    },
  });
};
