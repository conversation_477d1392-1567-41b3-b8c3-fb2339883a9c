import { StyleSheet, Dimensions } from 'react-native';
import { useTheme } from 'react-native-paper';
import {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';

type VoiceAnalyzeItemStyleProps = {
  handleDelete: () => Promise<void>;
};

const useVoiceAnalyzeItemStyle = ({ handleDelete }: VoiceAnalyzeItemStyleProps) => {
  const theme = useTheme();
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const backgroundColor = useSharedValue(theme.colors.background);

  const { width: SCREEN_WIDTH } = Dimensions.get('window');
  const TRANSLATE_X_THRESHOLD = SCREEN_WIDTH / 3;
  const ICON_SHOW_TRESHOLD = SCREEN_WIDTH / 5;

  const panGesture = Gesture.Pan()
    .activeOffsetX([-20, 20])
    .failOffsetY([-10, 10])
    .onUpdate(e => {
      translateX.value = e.translationX;

      if (translateX.value < -TRANSLATE_X_THRESHOLD) {
        translateX.value = -TRANSLATE_X_THRESHOLD - 1;
      } else if (translateX.value > 0) {
        translateX.value = 0;
      }

      if (translateX.value < -(SCREEN_WIDTH / 100)) {
        backgroundColor.value = '#FF7870';
      } else {
        backgroundColor.value = theme.colors.background;
      }
    })
    .onEnd(e => {
      const shouldDelete = translateX.value < -TRANSLATE_X_THRESHOLD;
      if (shouldDelete) {
        translateX.value = withTiming(-SCREEN_WIDTH);
        opacity.value = withTiming(0, undefined, () => {
          runOnJS(handleDelete)();
        });
      } else {
        translateX.value = withTiming(0);
      }
    });

  const rDeleteIconStyle = useAnimatedStyle(() => {
    const opacity = withTiming(translateX.value < -ICON_SHOW_TRESHOLD ? 1 : 0);
    return { opacity };
  });

  const rStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: translateX.value,
      },
    ],
  }));

  const bgAnimatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: backgroundColor.value,
    };
  });

  const rTaskContainerStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  const styles = StyleSheet.create({
    container: {
      width: '100%',
      backgroundColor: theme.colors.background,
      paddingVertical: 10,
      paddingHorizontal: 25,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    leftContent: {
      flex: 1,
    },
    dateText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#333333',
      marginBottom: 2,
    },
    timeText: {
      fontSize: 14,
      color: '#666666',
    },
    rightContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusBadge: {
      paddingHorizontal: 14,
      paddingVertical: 8,
      borderRadius: 16,
      marginRight: 8,
      minHeight: 32,
      justifyContent: 'center',
      alignItems: 'center',
    },
    statusText: {
      fontSize: 13,
      fontWeight: 'bold',
    },
    deleteButton: {
      marginRight: 0,
    },
    textContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      padding: 10,
      justifyContent: 'space-between',
    },
    actionContainer: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
    },

    gramContainer: {
      width: '100%',
      display: 'flex',
      height: 220,

      // alignItems: 'center',
      overflow: 'hidden',
      justifyContent: 'center',
    },
    image: {
      width: '100%',
      aspectRatio: 1,
    },

    imageTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: 10,
      color: '#333',
    },

    mainContainer: {
      // height: '100%',
      width: '100%',
      display: 'flex',
      gap: 30,
      paddingVertical: 12,
      paddingHorizontal: 25,
      alignItems: 'center',
    },

    buttonContainer: {
      display: 'flex',
      flexDirection: 'row',
    },

    waveFormContainer: {
      paddingHorizontal: 25,
      display: 'flex',
      justifyContent: 'space-between',
      flexDirection: 'row',

      borderRadius: 50,
      width: '100%',
      backgroundColor: theme.colors.primary,
    },

    CardStyle: {
      // width: '100%',
      // display: 'flex',
      // // gap: 16,
      // paddingHorizontal: 12,
      // paddingVertical: 12,
      display: 'flex',
      paddingTop: 8,
      paddingHorizontal: 10,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
    },

    titleStyle: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      gap: 10,
      height: 50,
    },

    textStyle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    iconContainer: {
      height: '100%',
      width: 50,
      position: 'absolute',
      right: 10,
      justifyContent: 'center',
      alignItems: 'center',
    },
    taskContainer: {
      width: '100%',
      backgroundColor: theme.colors.background,
      paddingVertical: 10,
      paddingHorizontal: 25,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
  });

  return {
    styles,
    panGesture,
    rDeleteIconStyle,
    rStyle,
    bgAnimatedStyle,
    rTaskContainerStyle,
  };
};

export default useVoiceAnalyzeItemStyle;