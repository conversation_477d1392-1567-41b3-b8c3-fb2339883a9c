import {CirclePlay, Pencil, Play, Trash} from 'lucide-react-native';
import React from 'react';
import {Text, View, TouchableOpacity} from 'react-native';
import {Card, IconButton, useTheme} from 'react-native-paper';
import {ExerciseCardStyle} from '../styles/ExerciseCardStyle';
import Animated from 'react-native-reanimated';
import {GestureDetector} from 'react-native-gesture-handler';

type ExerciseCardProps = {
  text?: string;
  content?: string[];
  buttonOne?: () => void;
  buttonTwo?: () => void;
  buttonThree?: () => void;
};

const ExerciseCard: React.FC<ExerciseCardProps> = ({
  text,
  content,
  buttonOne,
  buttonTwo,
  buttonThree,
}) => {
  const theme = useTheme();

  const {
    style,
    panGesture,
    rDeleteIconStyle,
    rStyle,
    rTaskContainerStyle,
    bgAnimatedStyle,
  } = ExerciseCardStyle(buttonOne);

  return (
    <Animated.View style={rTaskContainerStyle}>
      <View
        style={{
          display: 'flex',
          width: '100%',
          height: '100%',
          position: 'absolute',
        }}>
        <Animated.View style={[style.swipeBg, bgAnimatedStyle]}></Animated.View>
      </View>

      <Animated.View style={[style.deleteIcon, rDeleteIconStyle]}>
        <Trash size={24} color={'white'} />
      </Animated.View>

      <GestureDetector gesture={panGesture}>
        <Animated.View style={[style.card, rStyle]}>
          <TouchableOpacity style={style.card} onPress={buttonTwo}>
            <View>
              <Text style={style.textContainer}>{text}</Text>
              <View
                style={[
                  style.textContainer,
                  {display: 'flex', flexDirection: 'row'},
                ]}>
                <Text
                  style={style.descText}
                  numberOfLines={1}
                  ellipsizeMode="tail">
                  {content?.join(' ')}
                </Text>
              </View>
            </View>

            <Card.Actions>
              <IconButton
                style={{borderWidth: 0}}
                mode="outlined"
                icon={() => (
                  <CirclePlay size={30} color={theme.colors.primary} />
                )}
                size={30}
                onPress={buttonThree}
              />
            </Card.Actions>
          </TouchableOpacity>
        </Animated.View>
      </GestureDetector>
    </Animated.View>
  );
};

export default ExerciseCard;
