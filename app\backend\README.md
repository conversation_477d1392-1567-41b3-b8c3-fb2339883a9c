# VoiceBack - Backend local development setting up

---

## 🧰 Prerequisites

- Python 3.11+
- `venv` + `pip` installed

---

## 🚀 Local Development Setup

### 1. Go into the correct backend dir

```bash
cd voiceback/app/backend
```

### 2. First time setup

```bash
# Create venv
python3 -m venv venv # Do `python -m venv venv` if you don't have python3

# Activate venv (for Powershell)
.\venv\Scripts\activate.ps1 

# Activate venv (for Bash)
source venv/bin/activate

pip install -r requirements.txt

cd src

uvicorn app.main:app --reload
```

### 3. After first-time setup

```bash
.\start-dev.ps1
```

### 4. To deactivate venv

```bash
deactivate
```

# VoiceBack - Backend local development setting up (with <PERSON><PERSON>)

## 1. Prepare your `.env` file

Make sure you have a valid `.env` file in root before starting

## 2. Run Docker Compose

```bash
docker compose down -v
docker compose up -d --build
```

## 3. Check for Containers' status

```bash
docker stats
```

## 4. Check for test cases

```bash
docker logs -f voiceback-test-1
```

If the log reaches ```✔ ALL TESTS PASSED```, which means:

1. Praat native is working
2. API is ready with JSON data
3. Audio files can be uploaded at /app/uploads
4. Praat output files (abi.txt and avqui.txt) are generated at /app/src/outputs
5. Python can process the output files to create 4 images of each task ID
    - ABI_plot.png
    - AVQI_plot.png
    - Spectrogram.png
    - Waveform.png

## Troubleshooting

### 1. `.env` stuff

Make sure you have `.env` file before starting Docker
If you changed the `STAGE` value or other variables, restart Docker Compose

```bash
docker compose down -v
docker compose up -d --build
```

### 2. ✖ FASTAPI STARTUP TIMEOUT

This means something is wrong with the server startup

1. Check server container logs:

```bash
docker logs -f voiceback-server-1
```

Verify the FastAPI server is correctly running on port 8000.

2. Check the HTTPS portal:

```bash
docker logs -f voiceback-https-portal-1
```

Confirm the domain is correctly configured and verified

3. No audio files uploaded

- Make sure the uploaded files have the .wav extension.

- Audio files must be uploaded in pairs: `cs - sv`

4. Files uploaded but no output
This means something is wrong with the Praat software

1. Test if Praat is working
Copy the Praat binary into /usr/local/bin manually:

```bash
sudo cp ~/app/voiceback/tools/praat /usr/local/bin/
sudo chmod +x /usr/local/bin/praat
source ~/.bashrc
```

Test with terminal:

```bash
praat --version
```

You should see sth like:

```bash
✔ Praat version: Praat 6.4.27 (January 27 2025)
```

If not, this means the Praat binary might not be compatible with your machine's architecture (x86 vs ARM), try the correct one.
