import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useEffect, useState } from 'react';
import { Animated, Dimensions, View, Platform } from 'react-native';
import { RotateCcw } from 'lucide-react-native';
import AudioRecord from 'react-native-audio-record';
import {
  ActivityIndicator,
  Button,
  Modal,
  Portal,
  Text,
  useTheme,
} from 'react-native-paper';
import SQLite from 'react-native-sqlite-storage';
import StepIndicator from 'react-native-step-indicator';
import { useNavigation } from '@react-navigation/native';
import { useSorting } from '../../SortingContextProvider/SortingContextProvider';
import useBottomSheet from '../../BottomSheetComponent/useBottomSheet';
import AudioRecordService, { resetAudioRecordGlobal } from '../AudioRecordService';
import AudioRecorder from '../AudioRecorder/AudioRecorder';
import RecordAudio from '../RecordAudio';
import AudioPlayerCard from '../AudioPlayerCard/AudioPlayerCard';
import RequestService from '../RequestService';
import useStepperStyle, { useStepIndicatorStyles } from './StepperStyle';
import { getLanguagePrompt } from '../../../utils/languagePrompts';
import audioSessionManager from '../../../services/audioSessionManager';

SQLite.DEBUG(false);
SQLite.enablePromise(true);

type StepperCompProps = {
  isActive: boolean;
  index: number;
  bottomSheetModalRef: React.RefObject<BottomSheetModal>;
  isScreen?: boolean; // Add this prop to indicate if it's being used in a screen
};

const labels = ['Connected Speech', 'Sustained Vowel', 'Send request'];

const StepperComp: React.FC<StepperCompProps> = ({
  isActive,
  index,
  bottomSheetModalRef,
  isScreen,
}) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const styles = useStepperStyle();
  const customStyles = useStepIndicatorStyles(theme);
  const [fileCs, setFileCs] = useState<string>('');
  const [fileCsTemp, setFileCsTemp] = useState<string>('');
  const [textCs, setTextCs] = useState<string>('');
  const [fileSv, setFileSv] = useState<string>('');
  const [fileSvTemp, setFileSvTemp] = useState<string>('');
  const [textSv, setTextSv] = useState<string>('');
  const [instructionText, setInstructionText] = useState<string>('');
  const [isRecording, setIsRecording] = useState<boolean>(false);

  const [isRequesting, setIsRequesting] = useState<boolean>(false);

  const [isFinalStep, setIsFinalStep] = useState<boolean>(false);
  const [isFailed, setIsFailed] = useState<boolean>(false);

  const { dismissBottomSheet } = useBottomSheet(bottomSheetModalRef);

  const [currentPosition, setCurrentPosition] = useState(0);

  const { setAudioFiles, token, language } = useSorting();

  const service = RequestService(setAudioFiles, isRequesting, setIsRequesting);

  // Create services for CS and SV recordings
  const CsService = AudioRecordService(
    setFileCs,
    setFileCsTemp,
    setIsRecording,
    isRecording,
    'cs'
  );

  const SvService = AudioRecordService(
    setFileSv,
    setFileSvTemp,
    setIsRecording,
    isRecording,
    'sv'
  );

  useEffect(() => {
    if (index !== 1) {
      // Use an async IIFE to handle the async resetState
      (async () => {
        await resetState();
      })();
    }
  }, [isActive]);

  useEffect(() => {
    if (currentPosition === 2) {
      setIsFinalStep(true);
    }
  }, [currentPosition]);

  useEffect(() => {
    AudioRecord.on('data', (_data) => {
      console.log('[isRecording stepperComp]', isRecording);
    });
  }, [isRecording]);

  // Update text prompts when language changes
  useEffect(() => {
    if (language) {
      const prompts = getLanguagePrompt(language.code);
      setTextCs(prompts.connectedSpeech);
      setTextSv(prompts.sustainedVowel);
      setInstructionText(prompts.instruction);
      console.log(`Language changed to ${language.name}, updated text prompts`);
    }
  }, [language]);

  // Handle step changes
  useEffect(() => {
    // When changing steps, ensure recording is stopped
    const handleStepChange = async () => {
      if (isRecording) {
        console.log('[StepperComp] Step changed while recording, stopping recording');

        // Stop recording
        if (currentPosition === 0) {
          await CsService.onStopRecord();
        } else if (currentPosition === 1) {
          await SvService.onStopRecord();
        }

        setIsRecording(false);

        // On iOS, reset audio session when changing steps
        if (Platform.OS === 'ios') {
          console.log('[StepperComp] Resetting iOS audio session after step change');
          await audioSessionManager.reset();
        }
      }
    };

    handleStepChange();
  }, [currentPosition]);

  const resetState = async () => {
    console.log('[StepperComp] Resetting state');

    // Reset global AudioRecord state first
    await resetAudioRecordGlobal();

    // Deactivate audio session on iOS
    if (Platform.OS === 'ios') {
      console.log('[StepperComp] Deactivating iOS audio session');
      await audioSessionManager.deactivate();
    }

    // Then reset component state
    setFileCs('');
    setFileCsTemp('');
    setFileSv('');
    setFileSvTemp('');
    setIsRecording(false);
    setCurrentPosition(0);
    setIsFinalStep(false);

    console.log('[StepperComp] State reset complete');
  };

  const next = () => {
    setCurrentPosition(prev => {
      const nextPosition = Math.min(prev + 1, labels.length - 1);
      animateStepChange('left');
      return nextPosition;
    });
    if (isFinalStep) {
      setCurrentPosition(2);
    }
  };

  const prev = () => {
    setCurrentPosition(prev => {
      const prevPosition = Math.max(prev - 1, 0);
      animateStepChange('right');
      return prevPosition;
    });

    if (isFinalStep) {
      setCurrentPosition(2);
    }
  };

  const screenWidth = Dimensions.get('window').width;

  const [fadeAnim] = useState(new Animated.Value(1));
  const [slideAnim] = useState(new Animated.Value(0));
  const [textModalAnim] = useState(new Animated.Value(screenWidth));

  const [instructModalAnim] = useState(new Animated.Value(0));

  const animateStepChange = (direction: 'left' | 'right') => {
    fadeAnim.setValue(0);
    slideAnim.setValue(direction === 'left' ? 380 : -380);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animatedTextModal = () => {
    Animated.parallel([
      Animated.timing(textModalAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(instructModalAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleClickFunction = async (_recordingState: boolean) => {
    await resetAudioRecordGlobal();

    setIsRecording(false);

    Animated.parallel([
      Animated.timing(textModalAnim, {
        toValue: screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(instructModalAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      next();
    });
  };

  const handleClick = async () => {
    console.log(`[handleClick] Current position: ${currentPosition}, isRecording: ${isRecording}`);

    if (isRecording) {
      if (currentPosition === 0) {
        console.log('[handleClick] Stopping CS recording');
        try {
          await CsService.onStopRecord();

          await resetAudioRecordGlobal();

          handleClickFunction(isRecording);
        } catch (error) {
          console.error('[handleClick] Error stopping CS recording:', error);
          await resetAudioRecordGlobal();
          setIsRecording(false);
        }
      }
      if (currentPosition === 1) {
        console.log('[handleClick] Stopping SV recording');
        try {
          await SvService.onStopRecord();

          await resetAudioRecordGlobal();

          handleClickFunction(!isRecording);
        } catch (error) {
          console.error('[handleClick] Error stopping SV recording:', error);
          await resetAudioRecordGlobal();
          setIsRecording(false);
        }
      }
    } else {
      if (currentPosition === 0) {
        console.log('[handleClick] Starting CS recording');
        try {
          animatedTextModal();

          await resetAudioRecordGlobal();

          await CsService.onStartRecord();
        } catch (error) {
          console.error('[handleClick] Error starting CS recording:', error);
          await resetAudioRecordGlobal();
        }
      }
      if (currentPosition === 1) {
        console.log('[handleClick] Starting SV recording');
        try {
          animatedTextModal();

          await resetAudioRecordGlobal();

          await SvService.onStartRecord();
        } catch (error) {
          console.error('[handleClick] Error starting SV recording:', error);
          await resetAudioRecordGlobal();
        }
      }
    }
  };

  const handleAnalysing = async () => {
    const status = await service.handleAnalysing(
      fileCs,
      fileCsTemp,
      fileSv,
      fileSvTemp,
      token,
    );
    if (status == 'done') {
      if (isScreen) {
        // If it's a screen, navigate back
        navigation.goBack();
      } else {
        // If it's a bottom sheet, dismiss it
        dismissBottomSheet();
      }
    } else if (status == 'failed') {
      setIsFailed(true);
    }
  };

  const renderStepComp = () => {
    switch (currentPosition) {
      case 0:
        return (
          <View key={0} style={{ paddingHorizontal: 40, height: 60, marginBottom: 20, marginTop: 20 }}>
            <AudioRecorder
              filePath={fileCs}
              isRecordingState={isRecording}></AudioRecorder>
          </View>
        );

      case 1:
        return (
          <View key={1} style={{ paddingHorizontal: 40, height: 60, marginBottom: 20, marginTop: 20 }}>
            <AudioRecorder
              filePath={fileSv}
              isRecordingState={isRecording}></AudioRecorder>
          </View>
        );
      case 2:
        return (
          <View
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: 10,
              height: '100%',
            }}>
            <AudioPlayerCard
              filePath={fileCs}
              filePathTemp={fileCsTemp}
              title="Connected Speech"
              setCurrentPosition={setCurrentPosition}>
              <RotateCcw
                size={25}
                color="white"
                onPress={() => setCurrentPosition(0)}
              />
            </AudioPlayerCard>
            <AudioPlayerCard
              filePath={fileSv}
              filePathTemp={fileSvTemp}
              title="Sustained Vowel"
              setCurrentPosition={setCurrentPosition}>
              <RotateCcw
                size={25}
                color="white"
                onPress={() => setCurrentPosition(1)}
              />
            </AudioPlayerCard>
          </View>
        );
      default:
        return null;
    }
  };

  const isDisable = (): { next: boolean; back: boolean } => {
    if (isRecording) {
      return { next: true, back: true };
    }
    if (currentPosition === 0) {
      if (fileCs === '') {
        return { next: true, back: true };
      }
      if (isFinalStep) {
        return { next: true, back: false };
      }
    }
    if (currentPosition === 1) {
      if (fileSv === '') {
        return { next: true, back: false };
      }

      if (isFinalStep) {
        return { next: true, back: false };
      }
    }

    if (isFinalStep) {
      return { next: false, back: false };
    }

    return { next: false, back: false };
  };

  return (
    <View style={styles.container}>
      <StepIndicator
        customStyles={customStyles}
        currentPosition={currentPosition}
        labels={labels}
        stepCount={labels.length}
      />

      <View style={styles.stepContent}>
        {currentPosition !== 2 ? (
          <RecordAudio key={1} isActive={isActive}></RecordAudio>
        ) : null}

        <Animated.View
          style={{
            width: '100%',
            opacity: fadeAnim,
            transform: [{ translateX: slideAnim }],
          }}>
          {renderStepComp()}
        </Animated.View>
        {/* <View style={{width: '100%'}}>{renderStepComp()}</View> */}
      </View>

      <View style={styles.buttonContainer}>
        {currentPosition === labels.length - 1 ? null : (
          <Button
            mode="contained"
            buttonColor={
              isDisable().back
                ? theme.colors.onSurfaceDisabled
                : theme.colors.primary
            }
            textColor="white"
            style={{
              flex: 1,
            }}
            contentStyle={styles.buttonContentStyle}
            onPress={prev}
            disabled={isDisable().back}>
            <Text style={{ fontSize: 16, color: 'white' }}>Back</Text>
          </Button>
        )}

        {currentPosition !== labels.length - 1 ? (
          <Button
            mode="contained"
            buttonColor={theme.colors.primary}
            textColor="white"
            style={{ flex: 1 }}
            contentStyle={styles.buttonContentStyle}
            onPress={handleClick}>
            <Text style={{ fontSize: 16, color: 'white' }}>
              {isRecording ? 'Stop' : 'Record'}
            </Text>
          </Button>
        ) : (
          <Button
            mode="contained"
            buttonColor={theme.colors.primary}
            textColor="white"
            style={{ flex: 1 }}
            contentStyle={styles.buttonContentStyle}
            onPress={handleAnalysing}>
            <Text style={{ fontSize: 16, color: 'white' }}>Send</Text>
          </Button>
        )}
      </View>
      <Portal>
        <Modal visible={isRequesting} onDismiss={() => !isRequesting}>
          <ActivityIndicator
            animating={true}
            color={theme.colors.primary}
            size={80}
          />
        </Modal>
        <Modal
          visible={isFailed}
          onDismiss={() => !isRequesting}
          contentContainerStyle={{
            backgroundColor: 'white',
            borderRadius: 20,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.25,
            shadowRadius: 3.84,
            elevation: 5, // for Android
            padding: 20,
            marginHorizontal: 40,
            alignItems: 'center',
            justifyContent: 'center',
            gap: 20,
          }}>
          <View>
            <Text
              style={{
                fontSize: 20,
                fontWeight: 'bold',
                marginBottom: 5,
                textAlign: 'center',
              }}>
              Failed to analyze, please try again
            </Text>
          </View>
          <Button mode="contained" onPress={() => setIsFailed(false)}>
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: 'white' }}>
              Confirm
            </Text>
          </Button>
        </Modal>
      </Portal>
      {currentPosition !== labels.length - 1 ? (
        <Animated.View
          style={[
            styles.textModalContainer,

            {
              transform: [{ translateX: instructModalAnim }],
              paddingHorizontal: 20,
            },
          ]}>
          <Text style={[styles.textStyle, { fontSize: 20 }]}>
            {instructionText}
          </Text>
        </Animated.View>
      ) : null}

      <Animated.View
        style={[
          styles.textModalContainer,
          {
            transform: [{ translateX: textModalAnim }],
          },
        ]}>
        {currentPosition === 0 ? (
          <Text style={styles.textStyle}>{textCs}</Text>
        ) : (
          <Text style={styles.textStyle}>{textSv}</Text>
        )}
      </Animated.View>
    </View>
  );
};

export default StepperComp;