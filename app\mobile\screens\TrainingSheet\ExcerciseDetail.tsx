import {ArrowR<PERSON>, Check, X} from 'lucide-react-native';
import React from 'react';
import {Image, Text, View} from 'react-native';
import {Appbar, IconButton} from 'react-native-paper';
import {ExerciseItem} from './components/ExerciseItem';
import {useExerciseDetail} from './hooks/useExerciseDetail';
import {ExerciseDetailStyle} from './styles/ExerciseDetailStyle';
import {TopNav} from '../../components/TopNav/TopNav';

type ExerciseDetailProps = {
  isDirect?: boolean;
};

const ExerciseDetail: React.FC<ExerciseDetailProps> = ({isDirect}) => {
  const {
    theme,
    navigation,
    currentExercise,
    currentLesson,
    handleNextExercise,
    buttonDisabled,
  } = useExerciseDetail();

  const styles = ExerciseDetailStyle(isDirect);

  return (
    <View style={styles.container}>
      <TopNav
        title={currentExercise.name}
        backFunction={() => navigation.navigate('LessonPage')}></TopNav>

      <View style={styles.textContainer}>
        {currentExercise.content.map((item, index) => (
          <ExerciseItem key={index} item={item} index={index}></ExerciseItem>
        ))}
      </View>

      <View style={styles.buttons}>
        <IconButton
          style={styles.leftButtons}
          icon={() => (
            <Image
              source={require('../../assets/exerciseIcons/playMelody.png')}
              style={{width: 50, height: 50}}
            />
          )}
          onPress={() => console.log('Played melody')}
          size={40}
          disabled={buttonDisabled}
        />
        <View style={styles.rightButtons}>
          <IconButton
            style={styles.leftButtons}
            icon={() => (
              <Image
                source={require('../../assets/exerciseIcons/playSample.png')}
                style={{width: 50, height: 50}}
              />
            )}
            onPress={() => console.log('Played sample')}
            size={40}
            disabled={buttonDisabled}
          />
          <IconButton
            style={styles.checkButton}
            icon={() => <ArrowRight size={40} color="white" />}
            mode="contained-tonal"
            size={40}
            onPress={handleNextExercise}
            disabled={buttonDisabled}
          />
        </View>
      </View>
    </View>
  );
};

export default ExerciseDetail;
