import { createNativeStackNavigator } from '@react-navigation/native-stack';
import * as React from 'react';
import VoiceEvaluationScreen from '../VoiceEvaluation/VoiceEvaluationScreen';
import VoiceAnalysisScreen from '../VoiceAnalysis/VoiceAnalysisScreen';
import RecordAudioScreen from '../VoiceEvaluation/RecordAudioScreen';
import { AudioFile } from '../../services/audioFileManageService';

export type VoiceEvaluationStackParamList = {
  VoiceEvaluation: undefined;
  VoiceAnalysis: {
    audioFile: AudioFile;
    fileCs: string;
    fileSv: string;
  };
  RecordAudio: undefined;
};

const Stack = createNativeStackNavigator<VoiceEvaluationStackParamList>();

export const VoiceEvaluationNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="VoiceEvaluation">
      <Stack.Screen
        name="VoiceEvaluation"
        component={VoiceEvaluationScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="VoiceAnalysis"
        component={VoiceAnalysisScreen as React.ComponentType<any>}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="RecordAudio"
        component={RecordAudioScreen}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};
