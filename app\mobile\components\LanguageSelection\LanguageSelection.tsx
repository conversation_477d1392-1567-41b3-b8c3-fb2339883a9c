// LanguageSelection.js
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import {StyleSheet} from 'nativewind';
import React, {useEffect, useRef, useState} from 'react';
import {Image, ScrollView, View} from 'react-native';
import {Appbar, Card, Text, useTheme} from 'react-native-paper';
import {useSorting} from '../SortingContextProvider/SortingContextProvider';
import BottomSheetWrapper from '../BottomSheetComponent/BottomSheetWrapper';
import useBottomSheet from '../BottomSheetComponent/useBottomSheet';
import countryFlags from '../../utils/language';
import LanguageSelector from '../LanguageSelector/LanguageSelector';

export type LanguageItem = {
  name: string;
  code: string;
  voiceCode: string;
  isSelected: boolean;
};

const {images} = countryFlags();

const LanguageSelection = () => {
  const theme = useTheme();
  const styles = StyleSheet.create({
    modalContainer: {
      width: '100%',
      margin: 0,
      backgroundColor: 'white',
    },
    scrollView: {
      backgroundColor: 'white',
      height: '100%',
      width: '100%',
      padding: 10,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      flexWrap: 'wrap',
    },
    card: {
      width: 180,
      height: 120,
      display: 'flex',
      justifyContent: 'center',
      marginBottom: 20,
      backgroundColor: theme.colors.background,
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    cardContent: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 10,
    },
  });

  const {language, setLanguage} = useSorting();
  const [selectedIcon, setSelectIcon] = useState<string>(language.code);
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const {showBottomSheet, dismissBottomSheet, handleSheetChanges} =
    useBottomSheet(bottomSheetModalRef);

  const [index, setIndex] = useState(handleSheetChanges(1));

  const [showLanguageSelector, setShowLanguageSelector] =
    useState<boolean>(false);

  const [languages, setLanguages] = useState<LanguageItem[]>([
    {name: 'English', code: 'en-US', voiceCode: 'en_US', isSelected: false},
    {name: 'Vietnamese', code: 'vi-VN', voiceCode: 'vi-VN', isSelected: false},
    {name: 'Spanish', code: 'es-ES', voiceCode: 'es_CL', isSelected: false},
    {name: 'German', code: 'de-DE', voiceCode: 'de_DE', isSelected: false},
    {name: 'French', code: 'fr-FR', voiceCode: 'fr_FR', isSelected: false},
    {name: 'Italian', code: 'it-IT', voiceCode: 'it_IT', isSelected: false},
    {name: 'Japanese', code: 'ja-JP', voiceCode: 'ja_JP', isSelected: false},
    {name: 'Korean', code: 'ko-KR', voiceCode: 'ko_KR', isSelected: false},
    {name: 'Russian', code: 'ru-RU', voiceCode: 'ru_RU', isSelected: false},
    {
      name: 'Chinese',
      code: 'zh-CN',
      voiceCode: 'zh_Hans_SG',
      isSelected: false,
    },
  ]);

  useEffect(() => {
    if (language) {
      setLanguages(
        languages.map(item => ({
          ...item,
          isSelected: item.code === language.code,
        })),
      );
      setSelectIcon(language.code);
    }
  }, [language]);

  const handleChangeLanguage = (language: LanguageItem) => {
    setLanguages(
      languages.map(item => ({
        ...item,
        isSelected: item.code === language.code,
      })),
    );
    setLanguage(language);
    setSelectIcon(language.code);
  };

  return (
    <View>
      <Appbar.Action
        icon={() => (
          <Image
            source={images[selectedIcon as keyof typeof images]}
            style={{width: 30, height: 30}}
          />
        )}
        animated={false}
        color="white"
        onPress={() => setShowLanguageSelector(true)}
      />
      <LanguageSelector
        languageCode={language.code}
        showLanguage={showLanguageSelector}
        setShowLanguage={setShowLanguageSelector}
        handleChangeLanguageGlobal={handleChangeLanguage}
      />
    </View>
  );
};

export default LanguageSelection;
