import React from 'react';
import { View, Text } from 'react-native';

// We're no longer using value comparison for color coding

export const RenderAnalysisData = ({ data, theme }: { data: string, theme: any }) => {
  let keyValuePairs: { key: string; value: number }[] = [{ key: '', value: 0 }];
  if (data !== '') {
    try {
      // Try to parse the data as JSON
      let convertData;
      try {
        convertData = JSON.parse(data);
        console.log('Successfully parsed AVQI data:', convertData);
      } catch (parseError) {
        console.error('Error parsing result data:', parseError);
        console.log('Raw data:', data);
        return (
          <View>
            <Text>Error parsing analysis data. Please try again.</Text>
          </View>
        );
      }

      // Check if convertData is an object
      if (typeof convertData !== 'object' || convertData === null) {
        console.error('Result data is not an object:', convertData);
        return (
          <View>
            <Text>Invalid analysis data format. Please try again.</Text>
          </View>
        );
      }

      // Determine if we're dealing with AVQI or ABI data
      const isAVQI = convertData.hasOwnProperty('value') ||
        convertData.hasOwnProperty('cpps') ||
        convertData.hasOwnProperty('hnr');

      const isABI = convertData.hasOwnProperty('abi') ||
        convertData.hasOwnProperty('gne') ||
        convertData.hasOwnProperty('hfno');

      console.log(`Analysis data appears to be: ${isAVQI ? 'AVQI' : ''} ${isABI ? 'ABI' : ''}`);

      // Keys to exclude from display (encoded data)
      const keysToRemove = ['encoded_plot', 'encoded_spectrogram', 'encoded_waveform'];

      // Create key-value pairs from the data
      let allPairs: { key: string; value: number }[] = [];

      // Process the data
      Object.entries(convertData)
        .filter(([key, _]) => !keysToRemove.includes(key))
        .forEach(([key, value]) => {
          // Skip nested objects (we'll process them separately)
          if (typeof value !== 'object' || value === null) {
            // Format the key for display
            let displayKey = key.replace(/_/g, ' ');

            // Make sure ABI metrics are properly prefixed for filtering
            if (key.toLowerCase() === 'abi') {
              displayKey = 'abi';
            } else if (['gne', 'hfno', 'hnrd', 'h1h2', 'jitter', 'period_sd'].includes(key.toLowerCase())) {
              // Add ABI prefix to known ABI metrics if they don't already have it
              if (!key.toLowerCase().startsWith('abi')) {
                displayKey = 'abi ' + displayKey;
              }
            }

            allPairs.push({
              key: displayKey,
              value: typeof value === 'number' ? parseFloat(value as any) :
                typeof value === 'string' ? parseFloat(value) : 0,
            });
          }
        });

      // Sort the pairs for better display
      allPairs.sort((a, b) => {
        // Put main metrics (AVQI, ABI) first
        if (a.key.toLowerCase() === 'value' || a.key.toLowerCase() === 'avqi' || a.key.toLowerCase() === 'abi') return -1;
        if (b.key.toLowerCase() === 'value' || b.key.toLowerCase() === 'avqi' || b.key.toLowerCase() === 'abi') return 1;

        // Then sort alphabetically
        return a.key.localeCompare(b.key);
      });

      keyValuePairs = allPairs;

      console.log('Extracted key-value pairs:', keyValuePairs);
    } catch (error) {
      console.error('Error processing analysis data:', error);
      return (
        <View>
          <Text>Error processing analysis data. Please try again.</Text>
        </View>
      );
    }
  }

  // We're no longer using color coding for values

  // Group the data by type (AVQI vs ABI)
  const avqiData = keyValuePairs.filter(({ key }) => {
    const keyLower = key.toLowerCase();
    // Include only AVQI metrics (exclude ABI metrics and encoded data)
    return !keyLower.includes('abi') &&
      !keyLower.includes('encoded') &&
      !['gne', 'hfno', 'hnrd', 'h1h2', 'jitter', 'period_sd'].includes(keyLower);
  });

  const abiData = keyValuePairs.filter(({ key }) => {
    const keyLower = key.toLowerCase();
    // Include only ABI metrics (exclude encoded data)
    return (keyLower.includes('abi') ||
      ['gne', 'hfno', 'hnrd', 'h1h2', 'jitter', 'period_sd'].includes(keyLower)) &&
      !keyLower.includes('encoded');
  });

  console.log('AVQI data:', avqiData);
  console.log('ABI data:', abiData);

  // Function to clean up display names
  const getDisplayName = (key: string, isAbiSection: boolean) => {
    let displayKey = key.toUpperCase();

    if (!isAbiSection && displayKey === 'VALUE') {
      return 'AVQI';
    }

    // Replace "percent" with "%"
    displayKey = displayKey.replace('PERCENT', '%');

    // Remove "ABI_" or "ABI " prefix if in ABI section
    if (isAbiSection && (displayKey.startsWith('ABI_') || displayKey.startsWith('ABI '))) {
      displayKey = displayKey.replace(/^ABI_|^ABI /, '');
    }

    return displayKey;
  };

  // Render function for a list of key-value pairs
  const renderDataList = (dataList: { key: string; value: number }[], title: string) => {
    const isAbiSection = title.includes('ABI');

    return (
      <View style={{ marginBottom: 10, paddingHorizontal: 15 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: theme.colors.primary, marginBottom: 10 }}>
          {title}
        </Text>
        {dataList.map(({ key, value }) => (
          <View
            key={key}
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingVertical: 8
            }}
          >
            <Text style={{ fontSize: 16, fontWeight: '500' }}>
              {getDisplayName(key, isAbiSection)}
            </Text>
            <Text style={{
              fontSize: 16,
              textAlign: 'right',
              color: 'black', // Use black color for all values
              fontWeight: '400'
            }}>
              {value !== null && value !== undefined ? value.toString() : 'N/A'}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <View>
      {avqiData.length > 0 && renderDataList(avqiData, 'AVQI Metrics')}
      {abiData.length > 0 && renderDataList(abiData, 'ABI Metrics')}
    </View>
  );
};