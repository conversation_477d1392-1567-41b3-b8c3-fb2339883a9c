import { StyleSheet } from 'react-native';
import { useTheme } from 'react-native-paper';

export const LessonPageStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    safeViewStyle: {
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: theme.colors.background,
    },
    scrollViewContainer: {
      flexGrow: 1,
      padding: 10,
      rowGap: 10,
      paddingBottom: 90,
      marginTop: 20,
    },
    buttonContainer: {
      flexDirection: 'row',
      display: 'flex',
      justifyContent: 'space-between',
      gap: 20,
      marginTop: 25,
    },
    addButton: {
      width: 200,
      left: 0,
    },
    sectionDivider: {
      width: '120%',
      height: 1,
      backgroundColor: '#ccc',
      marginHorizontal: 10,
      marginLeft: '-5%',
      marginTop: -5,
    },
    modalContainer: {
      backgroundColor: 'white',
      borderRadius: 20,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5, // for Android
      padding: 20,
      marginHorizontal: 30,
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 5,
    },
    modalMessage: {
      fontSize: 18,
    },
    modalContent: {
      borderRadius: 20,
      backgroundColor: 'white',
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      marginHorizontal: 30,
      padding: 30,
    },
    deleteButton: {
      width: 120,
      height: 50,
    },
    textStyle: {
      fontWeight: 'bold',
      fontSize: 18,
    },
    confirmButton: {
      backgroundColor: 'red',
    },
    bottomSheet: {
      width: '100%',
      rowGap: 10,
      padding: 10,
      position: 'relative',
      paddingBottom: 130,
    },
    buttonSheetContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      position: 'absolute',
      bottom: 170,
      left: '42%',
      borderRadius: 50,
    },
    input: {
      textAlignVertical: 'top',
      fontSize: 14,
      width: '100%',
      height: 45,
    },
    button: {
      marginTop: 10,
      borderWidth: 0,
    },

    playAllButton: {
      height: '12%',
      display: 'flex',
      alignItems: 'center',
      backgroundColor: 'transparent',
      position: 'absolute',
      bottom: 0,
      right: '6%',
    },
  });
};
