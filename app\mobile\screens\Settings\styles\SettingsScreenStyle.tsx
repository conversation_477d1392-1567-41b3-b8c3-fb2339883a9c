import { StyleSheet } from 'nativewind';
import { useTheme } from 'react-native-paper';

const useSettingsScreenStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    segmentedButtonContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 24,
      width: 280,
      alignSelf: 'center',
    },
    segmentButton: {
      paddingVertical: 8,
      paddingHorizontal: 0,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      marginHorizontal: 5,
      width: 100,
    },
    segmentButtonSelected: {
      backgroundColor: '#3C80F3',
    },
    segmentButtonText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#3C80F3',
    },
    segmentButtonTextSelected: {
      color: '#FFFFFF',
    },
    settingRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      paddingVertical: 5,
      paddingHorizontal: 5,
      height: 50,
    },
    settingLabel: {
      fontSize: 16,
      fontWeight: 'bold',
      marginRight: 20,
      marginTop: 5,
    },
    languageButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#3C80F3',
      borderRadius: 25,
      paddingHorizontal: 20,
      paddingVertical: 10,
    },
    languageText: {
      color: 'white',
      marginRight: 8,
      fontSize: 16,
      fontWeight: 'bold',
    },
    flagImage: {
      width: 24,
      height: 24,
      borderRadius: 12,
    },
    sliderContainer: {
      flex: 1,
      position: 'relative',
      alignItems: 'flex-end',
      justifyContent: 'flex-end',
      flexDirection: 'column',
      marginTop: -5,
    },
    sliderValue: {
      fontSize: 16,
      fontWeight: 'bold',
      alignSelf: 'flex-end',
      marginTop: -5,
    },
    divider: {
      height: 1,
      backgroundColor: '#E0E0E0',
      marginVertical: 16,
    },
    scrollView: {
      backgroundColor: 'white',
      height: '100%',
      width: '100%',
      padding: 10,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      flexWrap: 'wrap',
    },
    languageCard: {
      width: 180,
      height: 120,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
      backgroundColor: 'white',
      borderColor: 'rgba(0, 0, 0, 0.1)',
      borderWidth: 1,
      borderRadius: 8,
      padding: 10,
      gap: 10,
    },
    profileContainer: {
      alignItems: 'center',
      paddingTop: 10,
      paddingBottom: 20,
    },
    profilePicture: {
      width: 120,
      height: 120,
      borderRadius: 60,
      marginBottom: 20,
      backgroundColor: '#E0E0E0',
    },
    profileInfoContainer: {
      width: '100%',
      marginTop: 10,
    },
    profileInfoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    profileLabel: {
      fontSize: 16,
      fontWeight: 'bold',
    },
    profileValue: {
      fontSize: 16,
      color: '#000',
    },
    profilePlaceholder: {
      fontSize: 16,
      color: '#AAAAAA',
    },
    profileValueContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      minWidth: 120,
    },
    editableInput: {
      fontSize: 16,
      color: '#000',
      borderBottomWidth: 1,
      borderBottomColor: '#3C80F3',
      padding: 0,
      minWidth: 120,
      textAlign: 'right',
    },
    arrowIcon: {
      marginLeft: 8,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent',
    },
    optionsContainer: {
      position: 'absolute',
      right: 0,
      top: 50,
      backgroundColor: 'white',
      borderRadius: 8,
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      zIndex: 1000,
    },
    optionItem: {
      paddingVertical: 12,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: '#E0E0E0',
    },
    optionText: {
      fontSize: 16,
    },
    saveButton: {
      backgroundColor: '#3C80F3',
      borderRadius: 25,
      paddingVertical: 15,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 30,
      width: '100%',
    },
    saveButtonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: 'bold',
    },
    dropdownContainer: {
      position: 'absolute',
      top: 40,
      right: 0,
      width: 150,
      backgroundColor: 'white',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#E0E0E0',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 10,
      zIndex: 9999,
    },
    dropdownBackdrop: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'transparent',
      zIndex: 9000,
    },
    dropdownItem: {
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderBottomWidth: 1,
      borderBottomColor: '#F0F0F0',
    },
    dropdownItemSelected: {
      backgroundColor: '#F0F8FF',
    },
    dropdownItemText: {
      fontSize: 16,
      color: '#000',
    },
    dropdownItemTextSelected: {
      color: '#3C80F3',
      fontWeight: 'bold',
    },
  });
};

export default useSettingsScreenStyle;
