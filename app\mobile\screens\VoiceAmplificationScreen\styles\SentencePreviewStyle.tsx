import {StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';

export const SentencePreviewStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: theme.colors.background,
      zIndex: 5,
    },
    text: {
      fontSize: 90,
      textAlign: 'center',
      fontWeight: 'bold',
      height: '100%',
      paddingHorizontal: 15,
      paddingTop: 10,
      paddingBottom: 70,
    },
    word: {
      color: '#9C9BA6',
    },
    highlighted: {
      color: '#000000',
      borderRadius: 4,
      paddingHorizontal: 4,
    },
    optionsBar: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      width: '100%',
      justifyContent: 'flex-end',
    },
  });
};
