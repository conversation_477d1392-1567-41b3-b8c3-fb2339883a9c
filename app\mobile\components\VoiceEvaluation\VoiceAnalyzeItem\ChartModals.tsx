import React, { useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { Button } from 'react-native-paper';
import { DetailedMetric } from './Chart';

interface ChartModalProps {
  visible: boolean;
  onClose: () => void;
  selectedMetric: DetailedMetric | null;
  metrics: DetailedMetric[];
  onSelectMetric: (metric: DetailedMetric) => void;
  chartTitle: string;
}

// Base Modal Component
const BaseChartModal: React.FC<ChartModalProps> = ({
  visible,
  onClose,
  selectedMetric,
  metrics,
  onSelectMetric,
  chartTitle,
}) => {
  // Auto-select the first metric when the modal becomes visible
  useEffect(() => {
    if (visible && metrics.length > 0 && !selectedMetric) {
      onSelectMetric(metrics[0]);
    }
  }, [visible, metrics, selectedMetric, onSelectMetric]);
  // Function to render a detailed metric card
  const renderDetailedMetricCard = () => {
    if (!selectedMetric) return null;

    const {
      name,
      value,
      normalizedValue,
      threshold,
      thresholdContinuousSpeech,
      isNormal,
      higherIsBetter,
      description,
      min,
      max,
      chartType
    } = selectedMetric;

    const normalizedPercent = (normalizedValue * 100).toFixed(1);

    // Determine status text and color based on metric type and value
    let status;
    if (isNormal) {
      status = 'Normal';
    } else {
      // Special cases for metrics with specific interpretations
      switch (name) {
        case 'Slope':
          // For Slope: -6 to -12 dB/octave is normal
          status = value < -12 ? 'Steep (Breathy)' : 'Shallow (Pressed)';
          break;
        case 'Tilt':
          // For Tilt: ~10-20 dB is normal
          status = value < 10 ? 'Low (Pressed Voice)' : 'High (Breathy Voice)';
          break;
        case 'H1-H2':
          // For H1-H2: ~2-6 dB is normal
          status = value < 2 ? 'Pressed Voice' : 'Breathy Voice';
          break;
        default:
          // Standard higher/lower is better logic
          status = higherIsBetter ? 'Reduced' : 'Elevated';
      }
    }
    const statusColor = isNormal ? '#4CAF50' : '#F44336';

    return (
      <View style={styles.detailCard}>
        <Text style={styles.detailTitle}>{name}</Text>
        <Text style={styles.detailDescription}>{description}</Text>

        <View style={styles.metricsContainer}>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Raw Value:</Text>
            <Text style={styles.metricValue}>{value.toFixed(2)}</Text>
          </View>

          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Normal Range:</Text>
            <Text style={styles.metricValue}>
              {renderNormalRange(name, threshold, thresholdContinuousSpeech, higherIsBetter, chartType)}
            </Text>
          </View>

          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Reference Min/Max:</Text>
            <Text style={styles.metricValue}>{min} / {max}</Text>
          </View>

          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Normalized Score:</Text>
            <Text style={styles.metricValue}>{normalizedPercent}%</Text>
          </View>

          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Status:</Text>
            <Text style={[styles.metricValue, { color: statusColor, fontWeight: 'bold' }]}>
              {status}
            </Text>
          </View>
        </View>

        <Text style={styles.interpretationTitle}>Clinical Interpretation:</Text>
        <Text style={styles.interpretationText}>
          {isNormal
            ? `Your ${name} value is within the normal clinical range.`
            : (() => {
              // Special cases for metrics with specific interpretations
              switch (name) {
                case 'AVQI':
                  return `Your AVQI score is elevated, suggesting the presence of dysphonia (voice disorder).`;
                case 'ABI':
                  return `Your ABI score is elevated, indicating significant breathiness in your voice.`;
                case 'CPPS':
                  return `Your CPPS value is below the recommended threshold, suggesting reduced voice periodicity and potential dysphonia.`;
                case 'HNR':
                case 'HNRD':
                  return `Your ${name} value is below the recommended threshold, indicating increased noise in your voice signal.`;
                case 'Jitter':
                  return `Your Jitter value is elevated, indicating increased cycle-to-cycle frequency variation in your voice.`;
                case 'Shimmer %':
                case 'Shimmer dB':
                  return `Your ${name} value is elevated, indicating increased cycle-to-cycle amplitude variation in your voice.`;
                case 'Slope':
                  return value < -12
                    ? `Your Spectral Slope is steeper than normal, which may indicate a breathy or weak voice with reduced high-frequency energy.`
                    : `Your Spectral Slope is shallower than normal, which may indicate a pressed or strained voice with excessive high-frequency energy.`;
                case 'Tilt':
                  return value < 10
                    ? `Your Spectral Tilt is lower than normal, suggesting a pressed or tense voice quality with relatively strong high-frequency components.`
                    : `Your Spectral Tilt is higher than normal, suggesting a breathy voice quality with weak high-frequency components.`;
                case 'GNE':
                  return `Your GNE value is below the recommended threshold, indicating increased noise in your voice signal, often associated with breathiness.`;
                case 'H1-H2':
                  return value < 2
                    ? `Your H1-H2 value is lower than normal, suggesting a pressed voice quality with tight vocal fold adduction.`
                    : `Your H1-H2 value is higher than normal, suggesting a breathy voice quality with incomplete vocal fold closure.`;
                default:
                  return higherIsBetter
                    ? `Your ${name} value is below the recommended threshold, which may indicate reduced vocal function.`
                    : `Your ${name} value is above the recommended threshold, which may indicate vocal dysfunction.`;
              }
            })()
          }
        </Text>

        <Text style={styles.interpretationText}>
          {renderClinicalInterpretation(name, chartType)}
        </Text>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <ScrollView style={styles.modalScrollView}>
            {renderDetailedMetricCard()}

            {/* All Metrics Section */}
            <View style={styles.allMetricsContainer}>
              <Text style={styles.allMetricsTitle}>
                {chartTitle}
              </Text>

              {/* Show metrics relevant to the current chart type */}
              {metrics
                .filter(metric => !metric.key.startsWith('placeholder'))
                .map((metric) => (
                  <TouchableOpacity
                    key={metric.key}
                    style={[
                      styles.metricButton,
                      selectedMetric?.key === metric.key && styles.selectedMetricButton
                    ]}
                    onPress={() => onSelectMetric(metric)}
                  >
                    <Text style={[
                      styles.metricButtonText,
                      selectedMetric?.key === metric.key && styles.selectedMetricButtonText
                    ]}>
                      {metric.name}
                    </Text>
                    <View style={[
                      styles.statusIndicator,
                      { backgroundColor: metric.isNormal ? '#4CAF50' : '#F44336' }
                    ]} />
                  </TouchableOpacity>
                ))}
            </View>
          </ScrollView>

          <Button
            mode="contained"
            onPress={onClose}
            style={styles.closeButton}
          >
            Close
          </Button>
        </View>
      </View>
    </Modal>
  );
};

// Helper function to render normal range based on metric and chart type
const renderNormalRange = (
  name: string,
  threshold: number,
  _thresholdContinuousSpeech: number | undefined, // Prefixed with underscore to indicate it's intentionally unused
  higherIsBetter: boolean,
  chartType: 'AVQI' | 'ABI'
) => {
  if (name === 'CPPS') {
    return chartType === 'AVQI' ? '≥ 9.3 dB (Connected Speech)' : '≥ 14.5 dB (Sustained Vowel)';
  }

  // Special cases for specific metrics
  switch (name) {
    case 'AVQI':
      return '< 3.0 (language-specific)';
    case 'ABI':
      return '< 3.0';
    case 'HNR':
    case 'HNRD':
      return '≥ 20.0 dB';
    case 'Slope':
      return '-6 to -12 dB/octave';
    case 'Tilt':
      return '~10-20 dB difference';
    case 'H1-H2':
      return '~2-6 dB';
    default:
      return higherIsBetter ? `≥ ${threshold}` : `< ${threshold}`;
  }
};

// Helper function to render clinical interpretation based on metric
const renderClinicalInterpretation = (name: string, chartType: 'AVQI' | 'ABI') => {
  // Use chart type to provide context-specific interpretations
  const interpretations: Record<string, string> = {
    // AVQI Chart Metrics
    'AVQI': 'AVQI is a composite dysphonia index. Lower values (near 0) correspond to normal voice quality, while higher values toward 10 reflect increasing overall dysphonia severity. Scores above the validated cut-off (which varies by language, typically around 3.0) indicate the presence of a voice disorder.',

    // Different CPPS interpretations based on chart type
    'CPPS': chartType === 'AVQI'
      ? 'For connected speech, CPPS reflects voice periodicity. Higher CPPS values indicate a stronger harmonic structure (healthy voice). A CPPS below ~9.3 dB is typically associated with dysphonia or reduced voice quality in running speech.'
      : 'For sustained phonation, CPPS values are generally higher than in running speech. A CPPS around 14 dB or above on a sustained vowel is expected in healthy adult voices. Lower CPPS indicates weak harmonic structure and is associated with dysphonia.',

    'HNR': 'HNR measures the ratio of periodic (harmonic) voice energy to noise energy. Healthy voices have high HNR (≥15 dB, indicating little noise). A significantly lower HNR indicates a noisy, breathy or hoarse voice.',

    'Jitter': 'Jitter quantifies cycle-to-cycle frequency variation. Normal voices show minimal jitter (usually well under 0.5%). Elevated jitter indicates irregular vocal fold vibration frequency; this is often associated with roughness or hoarseness.',

    'Shimmer %': 'Shimmer% quantifies cycle-to-cycle amplitude variation. Normal voices show minimal shimmer (usually well under 5%). Elevated shimmer indicates irregular vocal fold vibration amplitude; this is often associated with breathiness or hoarseness.',

    'Shimmer dB': 'Shimmer in dB is another expression of amplitude perturbation. Normal sustained voices have very small shimmer in dB (on the order of a few tenths of a dB). A shimmer above ~0.5 dB reflects significant cycle-to-cycle loudness fluctuations.',

    'Slope': 'Spectral slope describes how rapidly harmonic energy declines at higher frequencies. A steep, highly negative slope means high-frequency harmonics drop off sharply (often in breathy voices). A shallow slope indicates relatively strong high-frequency energy (as in pressed voice).',

    'Tilt': 'Spectral tilt represents the balance between low- and high-frequency spectral energy. A high tilt (large difference) indicates a breathy voice where upper harmonics are weak. A low tilt means high-frequency energy is relatively strong, often reflecting a tense voice.',

    // ABI Chart Metrics
    'ABI': 'ABI is a composite index (0–10) quantifying breathy voice quality. Higher scores denote more severe breathiness. In adults, an ABI above the established cutoff (~3.0) suggests abnormal, breathy dysphonia, whereas an ABI below the cutoff reflects essentially normal voice quality.',

    'GNE': 'GNE quantifies how much of the voice signal\'s excitation is due to clean glottal vibration versus noise. Normal voices produce a high GNE (near 1.0). Dysphonic, breathy voices yield a much lower GNE (closer to 0), indicating that turbulent noise is a large component.',

    'H1-H2': 'H1–H2 is an acoustic correlate of glottal closure and open quotient. A high H1–H2 (large amplitude difference favoring H1) signifies a more open glottal posture – typically a breathy voice. A very low or negative H1–H2 points to a tightly adducted, pressed voice quality.',

    'HNRD': 'HNR-D is a harmonic-to-noise ratio typically measured during sustained phonation. Normal sustained voices have a high HNR (around 20 dB or more). A reduced HNR in sustained phonation reflects significant noise in the voice signal – suggesting a hoarse or breathy voice.'
  };

  return interpretations[name] || '';
};

// AVQI Chart Modal
export const AVQIChartModal: React.FC<Omit<ChartModalProps, 'chartTitle'>> = (props) => {
  return (
    <BaseChartModal
      {...props}
      chartTitle="AVQI Voice Metrics"
    />
  );
};

// ABI Chart Modal
export const ABIChartModal: React.FC<Omit<ChartModalProps, 'chartTitle'>> = (props) => {
  return (
    <BaseChartModal
      {...props}
      chartTitle="ABI Voice Metrics"
    />
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '90%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalScrollView: {
    maxHeight: '90%',
  },
  detailCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
  },
  detailTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#3C80F3',
    marginBottom: 5,
  },
  detailDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    fontStyle: 'italic',
  },
  metricsContainer: {
    marginBottom: 15,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  metricLabel: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  metricValue: {
    fontSize: 14,
    color: '#333',
  },
  interpretationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  interpretationText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
    lineHeight: 20,
  },
  allMetricsContainer: {
    marginTop: 10,
  },
  allMetricsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  metricButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedMetricButton: {
    backgroundColor: '#e0f0ff',
    borderColor: '#3C80F3',
    borderWidth: 1,
  },
  metricButtonText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  selectedMetricButtonText: {
    color: '#3C80F3',
    fontWeight: 'bold',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  closeButton: {
    marginTop: 15,
    backgroundColor: '#3C80F3',
  },
});