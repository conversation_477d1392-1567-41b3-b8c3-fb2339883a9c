import React from 'react';
import { TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { useTabButtonStyle } from './TabButtonStyle';

export type TabButtonProps = {
  title: string;
  isActive: boolean;
  onPress: () => void;
  variant?: 'default' | 'chart';
  disabled?: boolean;
  testID?: string;
};

const TabButton: React.FC<TabButtonProps> = ({
  title,
  isActive,
  onPress,
  variant = 'default',
  disabled = false,
  testID,
}) => {
  const theme = useTheme();
  const styles = useTabButtonStyle(variant);

  // Use TouchableWithoutFeedback for 'default' variant (Voice Analysis style)
  // Use TouchableOpacity for 'chart' variant (Chart tabs style)
  const TouchableComponent = variant === 'default' ? TouchableWithoutFeedback : TouchableOpacity;

  const buttonStyle = [
    styles.button,
    isActive && {
      ...styles.activeButton,
      backgroundColor: theme.colors.primary,
    },
    disabled && styles.disabledButton,
  ];

  const textStyle = [
    styles.text,
    isActive && styles.activeText,
    disabled && styles.disabledText,
  ];

  return (
    <TouchableComponent onPress={onPress} disabled={disabled} testID={testID}>
      <View style={buttonStyle}>
        <Text style={textStyle}>{title}</Text>
      </View>
    </TouchableComponent>
  );
};

export default TabButton;
