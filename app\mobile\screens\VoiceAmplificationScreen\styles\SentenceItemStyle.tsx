import {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {Gesture} from 'react-native-gesture-handler';
import {Dimensions, StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';

type SentenceItemStyleProps = {
  handleDelete: () => Promise<void>;
  handleUpdate: () => Promise<void>;
};

const SentenceItemStyle = ({
  handleDelete,
  handleUpdate,
}: SentenceItemStyleProps) => {
  const theme = useTheme();
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const backgroundColor = useSharedValue('#FFFFFF');

  const {width: SCREEN_WIDTH} = Dimensions.get('window');
  const TRANSLATE_X_THRESHOLD = SCREEN_WIDTH / 3;
  const ICON_SHOW_TRESHOLD = SCREEN_WIDTH / 5;

  const panGesture = Gesture.Pan()
    .activeOffsetX([-20, 20]) // Must swipe more than 10px left or right
    .failOffsetY([-10, 10])
    .onUpdate(e => {
      translateX.value = e.translationX;

      if (translateX.value < -TRANSLATE_X_THRESHOLD) {
        translateX.value = -TRANSLATE_X_THRESHOLD - 1;
      } else if (translateX.value > TRANSLATE_X_THRESHOLD) {
        translateX.value = TRANSLATE_X_THRESHOLD + 1;
      }

      if (translateX.value < -(SCREEN_WIDTH / 100)) {
        backgroundColor.value = '#FF7870';
      } else if (translateX.value > SCREEN_WIDTH / 100) {
        backgroundColor.value = '#3C80F3';
      } else {
        backgroundColor.value = '#FFFFFF';
      }
    })
    .onEnd(e => {
      const shouldDelete = translateX.value < -TRANSLATE_X_THRESHOLD;
      const shouldFavorite = translateX.value > TRANSLATE_X_THRESHOLD;
      if (shouldDelete) {
        translateX.value = withTiming(-SCREEN_WIDTH);
        opacity.value = withTiming(0, undefined, () => {
          runOnJS(handleDelete)();
        });
      } else if (shouldFavorite) {
        translateX.value = withTiming(SCREEN_WIDTH);
        opacity.value = withTiming(0, undefined, () => {
          runOnJS(handleUpdate)();
        });
      } else {
        translateX.value = withTiming(0);
      }
    });

  const rDeleteIconStyle = useAnimatedStyle(() => {
    const opacity = withTiming(translateX.value < -ICON_SHOW_TRESHOLD ? 1 : 0);
    return {opacity};
  });

  const rFavoriteIconStyle = useAnimatedStyle(() => {
    const opacity = withTiming(translateX.value > ICON_SHOW_TRESHOLD ? 1 : 0);
    return {opacity};
  });

  const rStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: translateX.value,
      },
    ],
  }));

  const bgAnimatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: backgroundColor.value,
    };
  });

  const rTaskContainerStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  const styles = StyleSheet.create({
    mainContainer: {
      width: '100%',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.background,
      paddingVertical: 10,
      paddingHorizontal: 25,
    },
    textContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionContainer: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
    },
    fab: {
      backgroundColor: theme.colors.primary,
    },
    modalContainer: {
      width: '100%',
      margin: 0,
      backgroundColor: 'white',
    },
    scrollView: {
      backgroundColor: 'white',
      height: '100%',
      width: '100%',
      padding: 10,
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      flexWrap: 'wrap',
    },
    card: {
      width: 180,
      height: 120,
      display: 'flex',
      justifyContent: 'center',
      marginBottom: 20,
      backgroundColor: theme.colors.background,
      borderColor: 'rgba(0, 0, 0, 0.1)',
    },
    cardContent: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 10,
    },
    deleteIcon: {
      position: 'absolute',
      right: '5%',
      display: 'flex',
      top: '50%',
      transform: [{translateY: -12}],
    },
    favoriteIcon: {
      position: 'absolute',
      left: '5%',
      display: 'flex',
      top: '50%',
      transform: [{translateY: -12}],
    },
    swipeBg: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      width: '100%',
    },
    divider: {
      width: 1,
      height: 30, // or a fixed height like 24
      backgroundColor: '#ccc',
      marginHorizontal: 10,
    },
  });

  return {
    rTaskContainerStyle,
    panGesture,
    bgAnimatedStyle,
    rFavoriteIconStyle,
    rDeleteIconStyle,
    rStyle,
    styles,
  };
};

export default SentenceItemStyle;
