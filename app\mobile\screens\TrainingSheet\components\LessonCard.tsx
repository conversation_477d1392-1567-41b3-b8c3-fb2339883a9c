import {CirclePlay, Pencil, Play, Trash} from 'lucide-react-native';
import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native';
import {Card, IconButton, useTheme} from 'react-native-paper';
import {LessonCardStyle} from '../styles/LessonCardStyle';
import Animated from 'react-native-reanimated';
import {GestureDetector} from 'react-native-gesture-handler';

type LessonCardProps = {
  text?: string;
  target?: number;
  count?: number;
  buttonOne?: () => void;
  buttonTwo?: () => void;
  buttonThree?: () => void;
};

const LessonCard: React.FC<LessonCardProps> = ({
  text,
  target,
  count,
  buttonOne,
  buttonTwo,
  buttonThree,
}) => {
  const theme = useTheme();

  const {
    styles,
    rTaskContainerStyle,
    rStyle,
    bgAnimatedStyle,
    panGesture,
    rDeleteIconStyle,
  } = LessonCardStyle(buttonOne);

  return (
    <Animated.View style={rTaskContainerStyle}>
      <View
        style={{
          display: 'flex',
          width: '100%',
          height: '100%',
          position: 'absolute',
        }}>
        <Animated.View
          style={[styles.swipeBg, bgAnimatedStyle]}></Animated.View>
      </View>

      <Animated.View style={[styles.deleteIcon, rDeleteIconStyle]}>
        <Trash size={24} color={'white'} />
      </Animated.View>

      <GestureDetector gesture={panGesture}>
        <Animated.View style={[styles.card, rStyle]}>
          <TouchableOpacity style={styles.lessonInfo} onPress={buttonTwo}>
            <Text style={styles.textContainer}>{text}</Text>

            <View style={{flex: 1, flexDirection: 'row'}}>
              <Text style={styles.subTitle}>Daily Target: </Text>
              <Text
                style={[
                  styles.subTitle,
                  {
                    color:
                      (count ?? 0) >= (target ?? 0)
                        ? theme.colors.primary
                        : '#FF7870',
                  },
                ]}>
                {count}/{target}
              </Text>
            </View>
          </TouchableOpacity>

          <Card.Actions>
            <IconButton
              style={{borderWidth: 0}}
              mode="outlined"
              icon={() => <CirclePlay size={30} color={theme.colors.primary} />}
              size={30}
              onPress={buttonThree}
            />
          </Card.Actions>
        </Animated.View>
      </GestureDetector>
    </Animated.View>
  );
};

export default LessonCard;
