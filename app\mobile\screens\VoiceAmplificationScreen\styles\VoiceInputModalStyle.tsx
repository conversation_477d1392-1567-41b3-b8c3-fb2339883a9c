import {StyleSheet} from 'react-native';

export const VoiceInputModalStyle = () => {
  return StyleSheet.create({
    button: {
      borderWidth: 0,
    },
    container: {
      backgroundColor: '#FEFFFF',
      display: 'flex',
      paddingTop: 20,
      paddingHorizontal: 15,
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 20,
    },
    welcome: {
      fontSize: 20,
      textAlign: 'center',
      margin: 10,
    },
    action: {
      textAlign: 'center',
      color: '#0000FF',
      marginVertical: 5,
      fontWeight: 'bold',
    },

    buttonContainer: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      gap: 10,
    },

    separator: {
      width: 1,
      height: '60%',
      backgroundColor: '#C2C2C2',
      alignSelf: 'center',
    },
  });
};
