import AsyncStorage from '@react-native-async-storage/async-storage';
import React, {
  createContext,
  Dispatch,
  SetStateAction,
  useContext,
  useEffect,
} from 'react';
import {LanguageItem} from '../../utils/language';
import {Phrase} from '../../services/phraseManageService';
import {Lesson} from '../../services/lessonManageService';
import {Exercise} from '../../services/exerciseManageService';
import {AudioFile} from '../../services/audioFileManageService';
import {Graph, LessonCount} from '../../services/graphManageService';
import {SortingProviderService} from '../../services/sortingContextService';

type SortingState = {
  sortOption: string;
  phrases: Phrase[];
  language: LanguageItem;
  lessons: Lesson[];
  exercises: Exercise[];
  currentLesson: Lesson;
  currentExercise: Exercise;
  nextExercise: Exercise;
  audioFiles: AudioFile[];
  lessonCount: LessonCount[];
  graph: Graph[];
  token: string;
  autoEndPreview: boolean;
  isPlayAllMode: boolean;
  playbackSpeed: number;
};

type SortingContextType = SortingState & {
  setSortOption: Dispatch<SetStateAction<string>>;
  setPhrases: Dispatch<SetStateAction<Phrase[]>>;
  setLanguage: Dispatch<SetStateAction<LanguageItem>>;
  setLessons: Dispatch<SetStateAction<Lesson[]>>;
  setExercises: Dispatch<SetStateAction<Exercise[]>>;
  setCurrentLesson: Dispatch<SetStateAction<Lesson>>;
  setCurrentExercise: Dispatch<SetStateAction<Exercise>>;
  setNextExercise: Dispatch<SetStateAction<Exercise>>;
  setAudioFiles: Dispatch<SetStateAction<AudioFile[]>>;
  setLessonCount: Dispatch<SetStateAction<LessonCount[]>>;
  setGraph: Dispatch<SetStateAction<Graph[]>>;
  startAutoRequest: (audioFile: AudioFile, intervalInSeconds: number) => void;
  stopAutoRequest: () => void;
  setToken: Dispatch<SetStateAction<string>>;
  setPreviewEnd: Dispatch<SetStateAction<boolean>>;
  setPlayAllLesson: Dispatch<SetStateAction<boolean>>;
  setPlaybackSpeed: Dispatch<SetStateAction<number>>;
};

const SortingContext = createContext<SortingContextType>(
  {} as SortingContextType,
);

const SortingContextProvider = ({children}: {children: React.ReactNode}) => {
  const {
    setSortOption,
    setPhrases,
    setLanguage,
    setLessons,
    setExercises,
    setCurrentExercise,
    setCurrentLesson,
    setNextExercise,
    setAudioFiles,
    setLessonCount,
    setGraph,
    setToken,
    setPreviewEnd,
    startAutoRequest,
    stopAutoRequest,
    setPlayAllLesson,
    setPlaybackSpeed,
    state,
    dispatch,
  } = SortingProviderService();
  const loadSettings = async () => {
    try {
      const [
        savedSortOption,
        savedLanguage,
        savedToken,
        savedPreviewOption,
        savedPlaybackSpeed,
      ] = await Promise.all([
        AsyncStorage.getItem('@sortOption'),
        AsyncStorage.getItem('@language'),
        AsyncStorage.getItem('@token'),
        AsyncStorage.getItem('@autoEndPreview'),
        AsyncStorage.getItem('@playbackSpeed'),
      ]);

      if (savedSortOption !== null) {
        dispatch({type: 'SET_SORT_OPTION', payload: savedSortOption});
      }

      if (savedLanguage !== null) {
        const languageConverted: LanguageItem = JSON.parse(savedLanguage);
        dispatch({type: 'SET_LANGUAGE', payload: languageConverted});
      }

      if (savedToken !== null) {
        dispatch({type: 'SET_TOKEN', payload: savedToken});
      }

      if (savedPreviewOption !== null) {
        const autoEndPreview: LanguageItem = JSON.parse(savedPreviewOption);
        dispatch({type: 'SET_PREVIEW_END', payload: autoEndPreview});
      } else {
        AsyncStorage.setItem('@autoEndPreview', JSON.stringify(true));
      }

      if (savedPlaybackSpeed !== null) {
        const playbackSpeed: number = +savedPlaybackSpeed;
        dispatch({type: 'SET_PLAYBACK_SPEED', payload: playbackSpeed});
      } else {
        AsyncStorage.setItem('@playbackSpeed', '0.5');
      }
    } catch (error) {
      console.error('Error loading settings', error);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  useEffect(() => {
    startAutoRequest(state.audioFiles); // Start auto request with a 5-second interval
    return () => stopAutoRequest(); // Clean up the interval on unmount
  }, [state.audioFiles, startAutoRequest, stopAutoRequest]);

  return (
    <SortingContext.Provider
      value={{
        ...state,
        setSortOption,
        setPhrases,
        setLanguage,
        setLessons,
        setExercises,
        setCurrentExercise,
        setCurrentLesson,
        setNextExercise,
        setAudioFiles,
        setLessonCount,
        setGraph,
        setToken,
        setPreviewEnd,
        startAutoRequest,
        stopAutoRequest,
        setPlayAllLesson,
        setPlaybackSpeed,
      }}>
      {children}
    </SortingContext.Provider>
  );
};

const useSorting = () => {
  const context = useContext(SortingContext);
  if (context === undefined) {
    throw new Error('useSorting must be used within a SortingProvider');
  }
  return context;
};

export {SortingContextProvider, useSorting};
