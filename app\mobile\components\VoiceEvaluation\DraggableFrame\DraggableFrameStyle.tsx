import {StyleSheet} from 'nativewind';
import {useTheme} from 'react-native-paper';

const useDraggableStyles = () => {
  const theme = useTheme();
  return StyleSheet.create({
    selectionFrame: {
      position: 'absolute',
      height: '110%',
      backgroundColor: 'rgba(217, 217, 217, 0.3)', // Semi-transparent blue
      borderColor: theme.colors.surfaceDisabled,
      borderWidth: 2,
      borderRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
      top: '-5%',
    },
    textStyle: {
      color: 'white',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      padding: 2,
      borderRadius: 3,
    },
  });
};

export default useDraggableStyles;
