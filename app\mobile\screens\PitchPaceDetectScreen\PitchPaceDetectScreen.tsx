import React from 'react';
import { View, Pressable } from 'react-native';
import { Text } from 'react-native-paper';
import RealTimeChart from './components/LineChart';
import { TopNav } from '../../components/TopNav/TopNav';
import { usePitchPaceDetect } from './hooks/usePitchPaceDetect';
import { PitchPaceDetectStyle } from './styles/PitchPaceDetectStyle';

const PitchPaceDetectScreen = () => {
  const {
    showPitch,
    setShowPitch,
    pitch,
    showVolume,
    setShowVolume,
    volume,
    isSpeaking,
    rawPitchRef,
    rawVolumeRef,
  } = usePitchPaceDetect();

  const styles = PitchPaceDetectStyle();

  return (
    <View style={styles.container}>
      <TopNav title="Real-Time Voice" />

      <View style={styles.content}>
        <View style={styles.chartContainer}>
          <RealTimeChart
            volumeData={volume}
            pitchData={pitch}
            isSpeaking={isSpeaking}
            showVolume={showVolume}
            showPitch={showPitch}
          />
        </View>

        <View style={styles.toggleRow}>
          <View style={styles.pillContainer}>
            <Pressable
              style={[
                styles.pill,
                showPitch ? styles.pillActiveRed : styles.pillInactiveRed,
              ]}
              onPress={() => setShowPitch(!showPitch)}>
              <View
                style={[
                  styles.circleBase,
                  showPitch ? styles.circleActiveRed : styles.circleInactiveRed,
                ]}>
                {showPitch && <View style={styles.dotInnerRed} />}
              </View>
              <Text
                style={[
                  styles.pillText,
                  showPitch
                    ? styles.pillTextActiveRed
                    : styles.pillTextInactiveRed,
                ]}>
                Pitch
              </Text>
            </Pressable>
            <Text style={[styles.valueUnderPill, { color: '#FF7870' }]}>
              {rawPitchRef.current?.toFixed(2) || 0} Hz
            </Text>
          </View>

          <View style={styles.pillContainer}>
            <Pressable
              style={[
                styles.pill,
                showVolume ? styles.pillActiveBlue : styles.pillInactiveBlue,
              ]}
              onPress={() => setShowVolume(!showVolume)}>
              <View
                style={[
                  styles.circleBase,
                  showVolume
                    ? styles.circleActiveBlue
                    : styles.circleInactiveBlue,
                ]}>
                {showVolume && <View style={styles.dotInnerBlue} />}
              </View>
              <Text
                style={[
                  styles.pillText,
                  showVolume
                    ? styles.pillTextActiveBlue
                    : styles.pillTextInactiveBlue,
                ]}>
                Magnitude
              </Text>
            </Pressable>
            <Text style={[styles.valueUnderPill, { color: '#3C80F3' }]}>
              {volume.toFixed(2)} dB
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default PitchPaceDetectScreen;
