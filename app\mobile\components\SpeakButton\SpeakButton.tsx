import {CirclePause, CirclePlay} from 'lucide-react-native';
import React, {Dispatch, SetStateAction, useEffect, useRef} from 'react';
import {NativeEventEmitter, NativeModules, Platform} from 'react-native';
import {IconButton, useTheme} from 'react-native-paper';

// import Tts from 'react-native-tts';
import {useSorting} from '../SortingContextProvider/SortingContextProvider';
import {play, stop, setLanguage, revertVolume} from '../../services/ttsService';

interface props {
  text: string;
  onPress?: () => void;
  isDisable?: boolean;
  isSpeaking: boolean;
  setIsSpeaking: Dispatch<SetStateAction<boolean>>;
  languageCode?: string;
  updateLastUsed?: () => void;
  setHighlightWord?: Dispatch<SetStateAction<number>>;
  setHighlightWordIndex?: Dispatch<SetStateAction<number>>;
  size?: number;
  buttonSize?: number;
  buttonMode?: string;
  buttonColor?: string;
  setCurrentPhrase?: () => void;
}

const SpeakButton: React.FC<props> = ({
  text,
  onPress,
  isSpeaking,
  setIsSpeaking,
  languageCode,
  updateLastUsed,
  setHighlightWord,
  setHighlightWordIndex,
  size,
  buttonSize,
  buttonMode,
  buttonColor,
  setCurrentPhrase,
}) => {
  const {language} = useSorting();

  const ttsStart = (event: any) => {
    // console.log('TTS started', event);
    setHighlightWordIndex && setHighlightWordIndex(0);
  };

  const ttsProgress = (event: any) => {
    console.log(event);
    if (Platform.OS === 'ios') {
      setHighlightWord && setHighlightWord(event.location);
    } else {
      setHighlightWord && setHighlightWord(event.start);
    } // Update word highlight for preview
  };

  const ttsFinish = (event: any) => {
    // console.log('TTS finished', event);
    setIsSpeaking(false);
    revertVolume();
    setHighlightWordIndex && setHighlightWordIndex(100);
  };

  useEffect(() => {
    const ttsEmitter = new NativeEventEmitter(NativeModules.TextToSpeech);

    const startListener = ttsEmitter.addListener('tts-start', ttsStart);
    const progressListener = ttsEmitter.addListener(
      'tts-progress',
      ttsProgress,
    );
    const finishListener = ttsEmitter.addListener('tts-finish', ttsFinish);

    return () => {
      startListener.remove();
      progressListener.remove();
      finishListener.remove();
    };
  }, []);

  const theme = useTheme();

  const handlePress = async () => {
    setCurrentPhrase && setCurrentPhrase();
    setHighlightWordIndex && setHighlightWordIndex(-1);
    console.log();

    if (updateLastUsed) {
      updateLastUsed();
    }
    if (languageCode) {
      setLanguage(languageCode);
    } else {
      setLanguage(language.code);
    }

    if (isSpeaking) {
      stop();
      setIsSpeaking(false);
    }
    if (onPress) {
      play(text);
      setIsSpeaking(true);
      onPress();
    }
    if (!isSpeaking && !onPress) {
      play(text);
      setIsSpeaking(true);
    }
  };

  const renderIcon = () => {
    if (isSpeaking) {
      return (
        <CirclePause
          size={size || 24}
          color={buttonColor ? 'white' : '#FF7870'}
        />
      );
    } else {
      return (
        <CirclePlay
          size={size || 24}
          color={buttonColor ? 'white' : theme.colors.primary}
        />
      );
    }
  };

  return (
    <IconButton
      icon={renderIcon}
      size={buttonSize || 18}
      mode={buttonMode}
      containerColor={buttonColor}
      onPress={handlePress}
    />
  );
};

export default SpeakButton;
