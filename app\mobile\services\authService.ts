import { AUTH_PASSWORD, AUTH_USERNAME } from '@env';
import axios from 'axios';
import { Dispatch, SetStateAction } from 'react';
import { SERVER_API_URL } from '../services/config';

const AuthService = () => {
    const getAccessToken = async (setToken: Dispatch<SetStateAction<string>>) => {
        try {
            const config = {
                method: 'POST',
                url: `${SERVER_API_URL}/auth/token`,
                auth: {
                    username: AUTH_USERNAME,
                    password: AUTH_PASSWORD,
                },
            };
            const response = await axios.request(config);

            if (response.data) {
                setToken(response.data.access_token);
            }
        } catch (error) {
            console.error('[Error getAccessToken]', error);
        }
    };
    return { getAccessToken };
};

export default AuthService;