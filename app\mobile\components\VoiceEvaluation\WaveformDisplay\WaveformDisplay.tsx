import React, { useRef } from 'react';
import { View } from 'react-native';
import { useTheme } from 'react-native-paper';
import { Waveform, IWaveformRef } from '@simform_solutions/react-native-audio-waveform';

type WaveformDisplayProps = {
  path: string;
  height?: number;
  waveColor?: string;
  candleWidth?: number;
  candleSpace?: number;
  candleHeightScale?: number;
};

const WaveformDisplay: React.FC<WaveformDisplayProps> = ({
  path,
  height = 40,
  waveColor = '#B5E8FF',
  candleWidth = 3,
  candleSpace = 2,
  candleHeightScale = 2,
}) => {
  const waveFormRef = useRef<IWaveformRef>(null);
  const theme = useTheme();

  return (
    <View style={{ height, width: '100%' }}>
      {path ? (
        <Waveform
          containerStyle={{ height, width: '100%' }}
          mode="static"
          ref={waveFormRef}
          path={path}
          candleSpace={candleSpace}
          candleWidth={candleWidth}
          scrubColor={theme.colors.secondary}
          waveColor={waveColor}
          candleHeightScale={candleHeightScale}
          onPlayerStateChange={state => {
            if (__DEV__) {
              console.log(`WaveformDisplay: Player state changed to: ${state}`);
            }
          }}
          onPanStateChange={state => {
            if (__DEV__) {
              console.log(`WaveformDisplay: Pan state: ${state}`);
            }
          }}
          onError={error => {
            console.error(`WaveformDisplay: Error with waveform: ${error}`);
          }}
          onCurrentProgressChange={(currentProgress, songDuration) => {
            if (__DEV__) {
              console.log(
                `WaveformDisplay: Progress: ${currentProgress}/${songDuration}`
              );
            }
          }}
          onChangeWaveformLoadState={state => {
            if (__DEV__) {
              console.log(`WaveformDisplay: Load state changed: ${state}`);
            }
          }}
        />
      ) : (
        <View style={{ height, width: '100%' }} />
      )}
    </View>
  );
};

export default WaveformDisplay;
