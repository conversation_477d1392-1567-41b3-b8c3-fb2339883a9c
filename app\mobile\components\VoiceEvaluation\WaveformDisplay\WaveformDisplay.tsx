import React, { useEffect, useRef, useState } from 'react';
import { View } from 'react-native';
import { useTheme } from 'react-native-paper';
import { Waveform, IWaveformRef, PlayerState } from '@simform_solutions/react-native-audio-waveform';

type WaveformDisplayProps = {
  path: string;
  height?: number;
  waveColor?: string;
  candleWidth?: number;
  candleSpace?: number;
  candleHeightScale?: number;
};

const WaveformDisplay: React.FC<WaveformDisplayProps> = ({
  path,
  height = 40,
  waveColor = '#B5E8FF',
  candleWidth = 3,
  candleSpace = 2,
  candleHeightScale = 2,
}) => {
  const waveFormRef = useRef<IWaveformRef>(null);
  const [playerState, setPlayerState] = useState(PlayerState.stopped);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPlaying, setCurrentPlaying] = useState('');
  const [error, setError] = useState<string | null>(null);
  const theme = useTheme();
  const [file, setFile] = useState<string>('');

  useEffect(() => {
    if (path !== '') {
      setFile(path);
    }
  }, [path]);

  return (
    <View style={{ height, width: '100%' }}>
      {file ? (
        <Waveform
          containerStyle={{ height, width: '100%' }}
          mode="static"
          ref={waveFormRef}
          path={file}
          candleSpace={candleSpace}
          candleWidth={candleWidth}
          scrubColor={theme.colors.secondary}
          waveColor={waveColor}
          candleHeightScale={candleHeightScale}
          onPlayerStateChange={state => {
            console.log(`WaveformDisplay: Player state changed to: ${state}`);
            setPlayerState(state);
            if (state === PlayerState.stopped && currentPlaying === path) {
              setCurrentPlaying('');
            }
          }}
          onPanStateChange={state => {
            if (__DEV__) {
              console.log(`WaveformDisplay: Pan state: ${state}`);
            }
          }}
          onError={error => {
            console.error(`WaveformDisplay: Error with waveform: ${error}`);
            setError(`Waveform error: ${error}`);
          }}
          onCurrentProgressChange={(currentProgress, songDuration) => {
            if (__DEV__) {
              console.log(
                `WaveformDisplay: Progress: ${currentProgress}/${songDuration}`
              );
            }
          }}
          onChangeWaveformLoadState={state => {
            console.log(`WaveformDisplay: Load state changed: ${state}`);
            setIsLoading(state);
          }}
        />
      ) : (
        <View style={{ height, width: '100%' }} />
      )}
    </View>
  );
};

export default WaveformDisplay;
