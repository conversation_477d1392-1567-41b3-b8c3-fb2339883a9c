import {selectExercise} from '../../../services/exerciseManageService';
import {countLesson} from '../../../services/graphManageService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useLessonNavigation} from '../../../utils/useAppNavigation';
import {useTheme} from 'react-native-paper';
import {useState} from 'react';

export const useExerciseDetail = () => {
  const theme = useTheme();
  const navigation = useLessonNavigation();

  const [buttonDisabled, setButtonDisabled] = useState(false);

  const {
    lessons,
    setCurrentLesson,
    exercises,
    currentExercise,
    setExercises,
    nextExercise,
    currentLesson,
    setCurrentExercise,
    setNextExercise,
    setLessonCount,
    isPlayAllMode,
  } = useSorting();

  const enableButton = () => {
    setTimeout(() => setButtonDisabled(false), 100);
  };

  const handleNextExercise = async () => {
    if (buttonDisabled) return;

    setButtonDisabled(true);

    const isLastExerciseInLesson =
      exercises.indexOf(currentExercise) === exercises.length - 1;

    if (isLastExerciseInLesson) {
      await countLesson(currentLesson, setLessonCount);

      if (isPlayAllMode) {
        const currentLessonIndex = lessons.findIndex(
          lesson => lesson.id === currentLesson.id,
        );
        const nextLesson = lessons[currentLessonIndex + 1];

        if (nextLesson && nextLesson.exercises.length > 0) {
          setCurrentLesson(nextLesson);
          setExercises(nextLesson.exercises);

          selectExercise(
            nextLesson.exercises[0],
            nextLesson.exercises,
            setCurrentExercise,
            setNextExercise,
          );
          enableButton();
          return;
        }
      }

      // Finish flow
      navigation.navigate('LessonPage');
    } else {
      // Just move to next exercise in current lesson
      selectExercise(
        nextExercise,
        exercises,
        setCurrentExercise,
        setNextExercise,
      );

      enableButton();
    }
  };

  return {
    theme,
    navigation,
    currentExercise,
    currentLesson,
    handleNextExercise,
    buttonDisabled,
  };
};
