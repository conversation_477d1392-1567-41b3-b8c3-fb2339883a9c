{"name": "FrontEnd", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build:ios": "react-native bundle --entry-file='index.js' --bundle-output='./ios/main.jsbundle' --dev=false --platform='ios'"}, "dependencies": {"@gorhom/bottom-sheet": "^4.6.3", "@picovoice/react-native-voice-processor": "^1.2.3", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/hooks": "^3.0.0", "@react-native-community/slider": "^4.5.6", "@react-native-voice/voice": "^3.2.4", "@react-navigation/material-bottom-tabs": "^6.2.28", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@react-navigation/stack": "^6.3.29", "@simform_solutions/react-native-audio-waveform": "^2.0.0", "@wuba/react-native-echarts": "^1.3.1", "axios": "^1.7.2", "babel-plugin-optional-require": "^0.3.1", "buffer": "^6.0.3", "echarts": "^5.5.1", "lightningcss": "^1.29.3", "lucide-react-native": "^0.378.0", "moment": "^2.30.1", "nativewind": "^4.0.36", "pitchfinder": "^2.3.2", "react": "18.2.0", "react-native": "0.74.0", "react-native-audio-record": "^0.2.2", "react-native-audio-recorder-player": "^3.6.10", "react-native-audio-session": "^0.0.6", "react-native-chart-kit": "^6.12.0", "react-native-check-permissions": "^0.1.6", "react-native-date-picker": "^5.0.12", "react-native-document-picker": "^9.3.0", "react-native-dotenv": "^3.4.11", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.17.0", "react-native-gifted-charts": "^1.4.14", "react-native-html-to-pdf": "^0.12.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.12.3", "react-native-permissions": "^3.10.1", "react-native-pitch-detector": "^0.1.6", "react-native-reanimated": "^3.14.0", "react-native-safe-area-context": "^4.10.1", "react-native-screens": "^3.31.1", "react-native-share": "^11.0.2", "react-native-sound-level": "^1.3.0", "react-native-splash-screen": "^3.3.0", "react-native-sqlite-storage": "^6.0.1", "react-native-step-indicator": "^1.0.3", "react-native-svg": "^12.5.1", "react-native-tts": "^4.1.0", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.1.0", "react-native-view-shot": "^3.8.0", "react-native-vision-camera": "^4.3.2", "react-native-volume-manager": "^2.0.8", "react-native-webview": "^13.6.4", "rxjs": "^7.8.2", "sqlite": "^5.1.1", "tailwindcss": "^3.4.3", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.83", "@react-native/eslint-config": "0.74.83", "@react-native/metro-config": "0.74.83", "@react-native/typescript-config": "0.74.83", "@types/react": "^18.2.6", "@types/react-native-html-to-pdf": "^0.8.3", "@types/react-native-sqlite-storage": "^6.0.5", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "reactNativePermissionsIOS": ["Microphone"]}