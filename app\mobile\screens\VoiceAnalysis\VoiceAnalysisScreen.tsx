import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  Image,
  Modal,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  Text,
  useTheme,
} from 'react-native-paper';
import ImageViewer from 'react-native-image-zoom-viewer';
import { TopNav } from '../../components/TopNav/TopNav';
import ViewShot from 'react-native-view-shot';
import { AudioFile } from '../../services/audioFileManageService';
import ChartTabs from '../../components/VoiceEvaluation/VoiceAnalyzeItem/ChartTabs';
import AudioPlayerCard from '../../components/VoiceEvaluation/AudioPlayerCard/AudioPlayerCard';
import ShareFileButton from '../../components/VoiceEvaluation/ShareFile/ShareFileButton';
import TabButton from '../../components/VoiceEvaluation/TabButton/TabButton';

import { RenderAnalysisData } from '../../components/VoiceEvaluation/VoiceAnalyzeItem/RenderAnalysisData';
import useVoiceAnalysisStyle from './styles/VoiceAnalysisStyle';

type VoiceAnalysisScreenProps = {
  route: {
    params: {
      audioFile: AudioFile;
      fileCs: string;
      fileSv: string;
    };
  };
  navigation: any;
};

const VoiceAnalysisScreen: React.FC<VoiceAnalysisScreenProps> = ({ route, navigation }) => {
  const { audioFile, fileCs, fileSv } = route.params;
  const style = useVoiceAnalysisStyle();
  const theme = useTheme();

  const [audio] = useState<AudioFile>(audioFile);
  const [value, setValue] = useState('data');

  const [imageList, setImageList] = useState<string[]>([]);
  const [isImageViewerVisible, setIsImageViewerVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const viewShotRef = useRef(null);
  const [fadeAnim] = useState(new Animated.Value(1));
  const [slideAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Prepare image list for sharing and viewing
    const images = [];
    if (audioFile.spectrogramPath) {
      images.push(audioFile.spectrogramPath);
    }
    if (audioFile.waveFormPath) {
      images.push(audioFile.waveFormPath);
    }
    if (audioFile.avqiGram) {
      images.push(audioFile.avqiGram);
    }
    if (audioFile.abiGram) {
      images.push(audioFile.abiGram);
    }
    setImageList(images);
  }, [audioFile]);

  useEffect(() => {
    animateStepChange(value);
  }, [value]);

  const animateStepChange = (direction: string) => {
    fadeAnim.setValue(0);
    slideAnim.setValue(direction === 'data' ? -380 : 380);

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Function to handle opening the image viewer
  const handleImagePress = (index: number) => {
    setCurrentImageIndex(index);
    setIsImageViewerVisible(true);
  };



  const renderSegment = () => {
    switch (value) {
      case 'data':
        return (
          <ScrollView
            contentContainerStyle={{
              width: '100%',
              rowGap: 10,
              paddingHorizontal: 10,
              position: 'relative',
              paddingBottom: 60,
            }}
            showsVerticalScrollIndicator={true} // Show scroll indicator
          >
            <ViewShot ref={viewShotRef} options={{ format: 'jpg', quality: 0.9 }}>
              <RenderAnalysisData data={audioFile.result} theme={theme} />
            </ViewShot>

            <View style={{ alignItems: 'center', width: '100%', paddingVertical: 15 }}>
              {audio.result ? (
                <ChartTabs data={audio.result} />
              ) : (
                <Text style={{ textAlign: 'center', padding: 20, color: '#666' }}>
                  No chart data available
                </Text>
              )}
            </View>
          </ScrollView>
        );
      case 'image':
        return (
          <ScrollView
            contentContainerStyle={{
              width: '100%',
              position: 'relative',
              padding: 0,
              margin: 0,
              gap: 10,
              alignItems: 'center',
            }}
            showsVerticalScrollIndicator={false}
          >
            {/* Display Spectrogram if available */}
            {audio.spectrogramPath && (
              <TouchableOpacity
                style={[style.gramContainer, { margin: 0, padding: 0, width: '100%' }]}
                onPress={() => handleImagePress(0)}
              >
                <Image
                  style={style.image}
                  resizeMode="contain"
                  source={{
                    uri: `data:image/png;base64,${audio.spectrogramPath}`,
                  }}
                />
              </TouchableOpacity>
            )}

            {/* Display Waveform if available */}
            {audio.waveFormPath && (
              <TouchableOpacity
                style={[style.gramContainer, { margin: 0, padding: 0, width: '100%' }]}
                onPress={() => handleImagePress(1)}
              >
                <Image
                  style={style.image}
                  resizeMode="contain"
                  source={{
                    uri: `data:image/png;base64,${audio.waveFormPath}`,
                  }}
                />
              </TouchableOpacity>
            )}

            {/* Display AVQI Plot if available */}
            {audio.avqiGram && (
              <TouchableOpacity
                style={[style.gramContainer, { margin: 0, padding: 0, width: '100%' }]}
                onPress={() => handleImagePress(2)}
              >
                <Image
                  style={style.visualizationImage}
                  resizeMode="contain"
                  source={{
                    uri: `data:image/png;base64,${audio.avqiGram}`,
                  }}
                />
              </TouchableOpacity>
            )}

            {/* Display ABI Plot if available */}
            {audio.abiGram && (
              <TouchableOpacity
                style={[style.gramContainer, { margin: 0, padding: 0, width: '100%' }]}
                onPress={() => handleImagePress(3)}
              >
                <Image
                  style={style.visualizationImage}
                  resizeMode="contain"
                  source={{
                    uri: `data:image/png;base64,${audio.abiGram}`,
                  }}
                />
              </TouchableOpacity>
            )}
          </ScrollView>
        );
      case 'audio':
        return (
          <ScrollView
            contentContainerStyle={{
              display: 'flex',
              flexDirection: 'column',
              gap: 10,
              height: '100%',
              paddingBottom: 60,
            }}>
            <AudioPlayerCard
              filePath={fileCs}
              filePathTemp={fileCs}
              title="Connected Speech">
              <ShareFileButton filePath={fileCs}></ShareFileButton>
            </AudioPlayerCard>
            <AudioPlayerCard
              filePath={fileSv}
              filePathTemp={fileSv}
              title="Sustained Vowel">
              <ShareFileButton filePath={fileSv}></ShareFileButton>
            </AudioPlayerCard>
          </ScrollView>
        );
      default:
        return (
          <ScrollView
            contentContainerStyle={{
              width: '100%',
              rowGap: 10,
              paddingHorizontal: 10,
              position: 'relative',
              paddingBottom: 60,
            }}>
            <ViewShot ref={viewShotRef} options={{ format: 'jpg', quality: 0.9 }}>
              <RenderAnalysisData data={audioFile.result} theme={theme} />
            </ViewShot>
            <View style={{ alignItems: 'center', width: '100%', paddingVertical: 15 }}>
              {audio.result ? (
                <ChartTabs data={audio.result} />
              ) : (
                <Text style={{ textAlign: 'center', padding: 20, color: '#666' }}>
                  No chart data available
                </Text>
              )}
            </View>
          </ScrollView>
        );
    }
  };

  return (
    <View style={style.mainContainer}>
      {/* Header */}
      <TopNav
        title="Voice Analysis"
        backFunction={() => navigation.goBack()}
      >
        <View style={{ paddingRight: 15 }}>
          <ShareFileButton
            viewShotRef={viewShotRef}
            image2={imageList[0]}
            image3={imageList[1]}
            image4={imageList[2]}
            image5={imageList[3]}
            iconColor="white"
          />
        </View>
      </TopNav>

      {/* Content */}
      <View style={[style.contentContainer, { padding: 0 }]}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-around',
          marginTop: 10,
          marginBottom: 10,
          width: '100%',
        }}>
          <TabButton
            title="Analysis"
            isActive={value === 'data'}
            onPress={() => setValue('data')}
            variant="default"
          />
          <TabButton
            title="Images"
            isActive={value === 'image'}
            onPress={() => setValue('image')}
            variant="default"
          />
          <TabButton
            title="Audio"
            isActive={value === 'audio'}
            onPress={() => setValue('audio')}
            variant="default"
          />
        </View>

        <Animated.View
          style={{
            width: '100%',
            flex: 1,
            opacity: fadeAnim,
            transform: [{ translateX: slideAnim }],
            padding: 0,
            margin: 0,
          }}>
          {renderSegment()}
        </Animated.View>
      </View>

      {/* Image Viewer Modal */}
      <Modal visible={isImageViewerVisible} transparent={true}>
        <View style={{ flex: 1, backgroundColor: 'black' }}>
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
              zIndex: 1000,
              backgroundColor: 'rgba(0,0,0,0.5)',
              borderRadius: 20,
              padding: 10
            }}
            onPress={() => setIsImageViewerVisible(false)}
          >
            <Text style={{ color: 'white', fontSize: 16 }}>Close</Text>
          </TouchableOpacity>

          <ImageViewer
            imageUrls={imageList.map(img => ({
              url: `data:image/png;base64,${img}`,
              props: {}
            }))}
            index={currentImageIndex}
            enableSwipeDown={true}
            onSwipeDown={() => setIsImageViewerVisible(false)}
            onClick={() => { }} // Removed auto-close on click to allow for better zooming
            saveToLocalByLongPress={false}
          />
        </View>
      </Modal>
    </View>
  );
};

export default VoiceAnalysisScreen;
