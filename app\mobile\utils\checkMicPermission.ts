import {checkMicrophone} from 'react-native-check-permissions';
import {Alert, PermissionsAndroid, Platform} from 'react-native';
import {Dispatch, SetStateAction} from 'react';

const androidPermissionChecking = async () => {
  if (Platform.OS === 'android') {
    const hasPermission = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
    );
  }
};

const checkMicrophonePermission = async (
  setIsListening: Dispatch<SetStateAction<boolean>> | undefined,
) => {
  checkMicrophone().then((result: any) => {
    if (result?.status === 3) {
      Alert.alert(
        'Permission required',
        'You need to grant permission for microphone to use.',
        [{text: 'OK'}],
        {cancelable: false},
      );
      setIsListening && setIsListening(false);
    }
  });
};

export {checkMicrophonePermission, androidPermissionChecking};
