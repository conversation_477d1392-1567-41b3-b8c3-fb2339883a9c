from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from app.utils.token import generate_token
from app.config import Configs
import time

router = APIRouter(tags=["Auth"])
config = Configs()

# In-memory storage for tokens
tokens = {}

@router.get("/health")
async def auth_health_check():
    return {"status": "ok", "message": "Auth route is reachable"}

@router.post('/token')
async def issue_token(request: Request):
    auth = request.headers.get("authorization")

    if not auth or not auth.lower().startswith("basic "):
        return JSONResponse(status_code=401, content={"error": "Unauthorized"})

    import base64
    try:
        decoded = base64.b64decode(auth.split(" ")[1]).decode("utf-8")
        client_id, client_secret = decoded.split(":")
    except Exception:
        return JSONResponse(status_code=401, content={"error": "Invalid basic auth format"})

    if client_id == config.CLIENT_ID and client_secret == config.CLIENT_SECRET:
        token = generate_token()
        expires_in = 30 * 24 * 60 * 60  # 30 days

        tokens[token] = {
            'client_id': client_id,
            'expires_at': time.time() + expires_in
        }

        return JSONResponse(content={
            'access_token': token,
            'token_type': 'Bearer',
            'expires_at': expires_in
        })

    return JSONResponse(status_code=401, content={'error': 'Invalid client credentials'})

def validate_token(token):
    token_data = tokens.get(token)
    if not token_data:
        return None
    if time.time() > token_data['expires_at']:
        return None
    return token_data

