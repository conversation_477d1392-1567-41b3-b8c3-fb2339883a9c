import {useRef, useState} from 'react';
import {Animated} from 'react-native';
import {PanGestureHandler, State} from 'react-native-gesture-handler';
import useDraggableStyles from './DraggableFrameStyle';

const DraggableFrame = ({onTrimChange}) => {
  const [position, setPosition] = useState(0);
  const translateX = useRef(new Animated.Value(0)).current;
  const fixedFrameWidth = 100; // Set the fixed width of the frame

  const handleGestureEvent = Animated.event(
    [{nativeEvent: {translationX: translateX}}],
    {useNativeDriver: false},
  );

  const styles = useDraggableStyles();

  const handleStateChange = event => {
    if (event.nativeEvent.state === State.END) {
      const newX = position + event.nativeEvent.translationX;

      // Ensure the frame does not go out of bounds
      const clampedX = Math.max(0, Math.min(newX, 290 - fixedFrameWidth));

      setPosition(clampedX);

      onTrimChange({
        start: clampedX,
        end: clampedX + fixedFrameWidth,
      });

      translateX.setValue(0); // Reset the translation
    }
  };

  return (
    <PanGestureHandler
      onGestureEvent={handleGestureEvent}
      onHandlerStateChange={handleStateChange}>
      <Animated.View
        style={[
          styles.selectionFrame,
          {
            transform: [{translateX}],
            width: fixedFrameWidth,
            left: position, // Set the initial position of the frame
          },
        ]}
      />
    </PanGestureHandler>
  );
};

export default DraggableFrame;
