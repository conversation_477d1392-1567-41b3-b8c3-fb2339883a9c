import { ShareIcon } from 'lucide-react-native';
import { Platform } from 'react-native';

import RNHTMLtoPDF from 'react-native-html-to-pdf';
import { IconButton, useTheme } from 'react-native-paper';
import Share from 'react-native-share';

type ShareFileButtonProps = {
  viewShotRef?: React.RefObject<any>;
  image2?: string;
  image3?: string;
  image4?: string;
  image5?: string;
  filePath?: string;
  iconColor?: string;
};

const ShareFileButton: React.FC<ShareFileButtonProps> = ({
  viewShotRef,
  image2,
  image3,
  image4,
  image5,
  filePath,
  iconColor = 'white',
}) => {
  const theme = useTheme();

  const captureAndConvert = async () => {
    try {
      const imageUri = await viewShotRef?.current.capture();

      const image2Path = `data:image/png;base64,${image2}`;
      const image3Path = `data:image/png;base64,${image3}`;
      const image4Path = `data:image/png;base64,${image4}`;
      const image5Path = `data:image/png;base64,${image5}`;

      const filePathFinal = await createPdfFile(
        imageUri,
        image2Path,
        image3Path,
        image4Path,
        image5Path,
        `file_${Date.now()}`,
      );

      await Share.open({
        url: `file://${filePathFinal}`,
        type: 'application/pdf',
      });
    } catch (error) {
      console.error('[Error captureAndConvert]', Date.now(), error);
    }
  };

  const shareAudio = async () => {
    try {
      await Share.open({
        url: `${filePath}`,
        type: 'audio/wav',
      });
    } catch (error) {
      console.error('[Error shareAudio]', error);
    }
  };

  return (
    <ShareIcon
      size={25}
      color={iconColor}
      onPress={viewShotRef ? captureAndConvert : shareAudio}
    />
  );
};

const createPdfFile = async (
  img1: string,
  image2: string,
  image3: string,
  image4: string,
  image5: string,
  fileName: string,
): Promise<string> => {
  const htmlContent = `
  <!DOCTYPE html>
    <html lang="en">
    <head>
          <style>
            body {
              font-family: 'Helvetica';
              margin: 0 12px;
            }
            .title-container {
              width: 100%;
              height: 100px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .container {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: space-around;
            }
            .analysis-container {
              width: 50%;
              height: auto;
              display: flex;
              flex-direction: column;
            }
            .gram-container {
              width: 50%;
              height: auto;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              align-items: center;
            }
            .image {
              width: 100%;
              height: auto;
              max-width: 595px; /* A4 width in px */
              margin-bottom: 20px;
            }
            .image-eval {
              width: 100%;
              height: auto;
              max-width: 595px; /* A4 width in px */
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <div class="title-container">
            <h2>Voice Evaluation</h2>
          </div>
          <div class="container">
            <div class="analysis-container">
              <h4>Analysis aspect</h4>
              <img class="image-eval" src="${img1}" />
            </div>
            <div class="gram-container">
              <h4>Analysis diagram</h4>
              <img class="image" src="${image2}" />
              <img class="image" src="${image3}" />
              <img class="image" src="${image4}" />
              <img class="image" src="${image5}" />
            </div>
          </div>
        </body>
      </html>
    </html>
`;

  try {
    let PDFOptions = {
      html: htmlContent,
      fileName: fileName,
      directory: Platform.OS === 'android' ? 'Downloads' : 'Documents',
    };

    let file = await RNHTMLtoPDF.convert(PDFOptions);
    if (!file.filePath) return '';
    console.log(`[PDFFile path]`, file.filePath);

    return file.filePath;
  } catch (error) {
    console.error('[Error convert html to pdf]', error);
    return '';
  }
};

export default ShareFileButton;