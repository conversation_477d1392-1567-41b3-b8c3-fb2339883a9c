import {createNativeStackNavigator} from '@react-navigation/native-stack';
import * as React from 'react';
import VoiceAmplification from '../VoiceAmplificationScreen/VoiceAmplification';

export type VoiceAmplificationStackParamList = {
  VoiceAmplification: undefined;
  LikeList: undefined;
};

const Stack = createNativeStackNavigator<VoiceAmplificationStackParamList>();

export const VoiceAmplificationNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="VoiceAmplification">
      <Stack.Screen
        name="VoiceAmplification"
        component={VoiceAmplification}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};
