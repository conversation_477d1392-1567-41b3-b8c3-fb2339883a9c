globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/Header2.png":{"*":{"id":"(ssr)/./src/assets/Header2.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/Icon1.png":{"*":{"id":"(ssr)/./src/assets/Icon1.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/Icon2.png":{"*":{"id":"(ssr)/./src/assets/Icon2.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/Icon3.png":{"*":{"id":"(ssr)/./src/assets/Icon3.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/assets/Logo.png":{"*":{"id":"(ssr)/./src/assets/Logo.png","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-navigation-menu/dist/index.mjs","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/app/page.module.css":{"id":"(app-pages-browser)/./src/app/page.module.css","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/assets/Header2.png":{"id":"(app-pages-browser)/./src/assets/Header2.png","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/assets/Icon1.png":{"id":"(app-pages-browser)/./src/assets/Icon1.png","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/assets/Icon2.png":{"id":"(app-pages-browser)/./src/assets/Icon2.png","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/assets/Icon3.png":{"id":"(app-pages-browser)/./src/assets/Icon3.png","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/assets/Logo.png":{"id":"(app-pages-browser)/./src/assets/Logo.png","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/":[],"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/app/page":["static/css/app/page.css"],"/Users/<USER>/Documents/GitHub/voiceback/LandingPage/landing_page/src/app/layout":["static/css/app/layout.css"]}}