import {Info} from 'lucide-react-native';
import React from 'react';
import {ScrollView, View} from 'react-native';
import {BarChart} from 'react-native-gifted-charts';
import {screenWidth} from 'react-native-gifted-charts/src/utils';
import {
  Button,
  Card,
  IconButton,
  Modal,
  Portal,
  Text,
} from 'react-native-paper';
import {TopNav} from '../../components/TopNav/TopNav';
import {useAnalysisScreen} from './hooks/useAnalysisScreen';
import {AnalysisScreenStyle} from './styles/AnalysisScreenStyle';

const AnalysisScreen = () => {
  const {
    lessons,
    barColor,
    navigation,
    handleClick,
    isSelected,
    theme,
    visible,
    hideModal,
    showModal,
    totalActivities,
    completeTimeEachLesson,
    barData7,
  } = useAnalysisScreen();

  const styles = AnalysisScreenStyle();

  const renderTitle = () => {
    return (
      <View style={styles.renderTitle}>
        {lessons.map((item, index) => (
          <View
            key={index}
            style={{flexDirection: 'row', alignItems: 'center'}}>
            <View
              style={{
                height: 12,
                width: 12,
                borderRadius: 6,
                backgroundColor: barColor[item.id - 1],
                marginRight: 8,
              }}
            />
            <Text style={styles.titleContent}>{item.name}</Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <View>
      <TopNav
        title="Analysis"
        backFunction={() => navigation.navigate('LessonPage')}></TopNav>

      <View style={styles.buttonContainer}>
        <Button
          onPress={() => handleClick('7 days')}
          style={
            isSelected === '7 days' ? styles.selectedButton : styles.notSelected
          }
          labelStyle={{
            color: isSelected === '7 days' ? 'white' : theme.colors.primary,
          }}>
          7 days
        </Button>
        <Button
          onPress={() => handleClick('1 month')}
          style={
            isSelected === '1 month'
              ? styles.selectedButton
              : styles.notSelected
          }
          labelStyle={{
            color: isSelected === '1 month' ? 'white' : theme.colors.primary,
          }}>
          1 month
        </Button>
        <Button
          onPress={() => handleClick('3 months')}
          style={
            isSelected === '3 months'
              ? styles.selectedButton
              : styles.notSelected
          }
          labelStyle={{
            color: isSelected === '3 months' ? 'white' : theme.colors.primary,
          }}>
          3 months
        </Button>
      </View>

      <ScrollView>
        <View style={styles.infoView}>
          <Portal>
            <Modal
              visible={visible}
              onDismiss={hideModal}
              contentContainerStyle={styles.modalContainer}>
              {renderTitle()}
            </Modal>
          </Portal>

          <IconButton
            icon={() => <Info size={28} color={theme.colors.primary} />}
            onPress={() => {
              showModal();
            }}
            style={{position: 'absolute', right: 0}}
          />
        </View>
      </ScrollView>

      <ScrollView contentContainerStyle={{paddingHorizontal: 5}}>
        <View style={{flexDirection: 'row', display: 'flex', gap: 10}}>
          <Card style={styles.cardStyle} contentStyle={styles.cardStyle}>
            <Card.Title
              title="Total Activities"
              titleStyle={styles.titleStyle}
            />
            <Card.Content style={styles.contentContainer}>
              <Text style={styles.dataStyle}>{totalActivities}</Text>
            </Card.Content>
          </Card>

          <Card style={{flex: 1, width: screenWidth, height: 110}}>
            <Card.Title title="Top 3" titleStyle={styles.titleStyle} />
            <Card.Content style={{marginTop: -15}}>
              {completeTimeEachLesson
                .sort((a, b) => b.count - a.count)
                .slice(0, 3)
                .map((item, index) =>
                  item.name ? ( // Check if item.name is not null or empty
                    <Text key={index}>
                      {item.name} : {item.count}
                    </Text>
                  ) : null,
                )}
            </Card.Content>
          </Card>
        </View>

        <BarChart
          width={screenWidth}
          barWidth={isSelected === '7 days' ? 10 : 7}
          spacing={
            isSelected === '7 days' ? 33 : isSelected === '1 month' ? 4 : 17
          }
          noOfSections={4}
          stackBorderRadius={2}
          yAxisThickness={0}
          xAxisLabelTextStyle={{
            color: theme.colors.primary,
            width: 80,
          }}
          stackData={barData7}
        />
      </ScrollView>
    </View>
  );
};

export default AnalysisScreen;
