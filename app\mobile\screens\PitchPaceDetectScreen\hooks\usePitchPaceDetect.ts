import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useEffect, useState, useRef, useCallback} from 'react';
import {VoiceProcessor} from '@picovoice/react-native-voice-processor';
import {Alert, PermissionsAndroid, Platform} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import PitchFinder from 'pitchfinder';

export const usePitchPaceDetect = () => {
  const [showVolume, setShowVolume] = useState<boolean>(true);
  const [showPitch, setShowPitch] = useState<boolean>(true);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [pitch, setPitch] = useState<number | null>(0);
  const [volume, setVolume] = useState<number>(0);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);

  const rawPitchRef = useRef<number | null>(0);
  const rawVolumeRef = useRef<number>(0);
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const speakingRef = useRef<boolean>(false);
  const showPitchRef = useRef<boolean>(showPitch);
  const showVolumeRef = useRef<boolean>(showVolume);
  const float32FrameRef = useRef<Float32Array | null>(null);

  const pitchBufferRef = useRef<number[]>([]);
  const volumeBufferRef = useRef<number[]>([]);
  const windowSize = 5;

  let voiceProcessor = VoiceProcessor.instance;

  const pitchDetector = useRef(
    PitchFinder.YIN({ sampleRate: 16000 })
  ).current;

  useSorting();

  // Update refs when state changes
  useEffect(() => {
    showPitchRef.current = showPitch;
  }, [showPitch]);

  useEffect(() => {
    showVolumeRef.current = showVolume;
  }, [showVolume]);

  const getAverage = (arr: number[]) =>
    arr.length === 0 ? 0 : arr.reduce((a, b) => a + b, 0) / arr.length;

  const hasChanged = (a: number | null, b: number | null, threshold = 0.5) =>
    Math.abs(((a ?? 0) - (b ?? 0))) > threshold;

  const calculateDbFS = (
    samples: number[],
    maxAmplitude = 32768,
    minDb = -65,
  ) => {
    if (!samples.length) return -Infinity;
    const sumSquares = samples.reduce((sum, s) => sum + s * s, 0);
    const rms = Math.sqrt(sumSquares / samples.length);
    const dbfs = 20 * Math.log10(rms / maxAmplitude);
    const clamped = Math.max(dbfs, minDb);
    const normalized = (clamped - minDb) / -minDb;
    return Math.round(Math.pow(normalized, 0.5) * 100);
  };

  const handlePitchData = ({frequency}: {frequency: number}) => {
    if (isNaN(frequency) || frequency <= 0 || frequency >= 2000) {
      if (speakingRef.current) {
        speakingRef.current = false;
        setIsSpeaking(false);
      }
      return;
    }
    pitchBufferRef.current.push(frequency);
    if (pitchBufferRef.current.length > windowSize) pitchBufferRef.current.shift();
    rawPitchRef.current = getAverage(pitchBufferRef.current);
    
    if (!speakingRef.current) {
      speakingRef.current = true;
      setIsSpeaking(true);
    }
  };

  const startRecording = async () => {
    const frameLength = 1024;
    const sampleRate = 16000;
    if (await voiceProcessor.hasRecordAudioPermission()) {
      await voiceProcessor.start(frameLength, sampleRate);
    }
  };

  const stopRecording = async () => {
    try {
      await voiceProcessor.stop();
    } catch (e) {
      // handle stop error
      console.log("Error stopping recording: ", e)
    }
  };

  useEffect(() => {
    if (Platform.OS !== 'android') return;
    PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO, {
      title: 'Microphone Permission',
      message: 'App needs access to your microphone',
      buttonNeutral: 'Ask Me Later',
      buttonNegative: 'Cancel',
      buttonPositive: 'OK',
    })
      .then(granted => {
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Alert.alert('Permission Denied', 'Microphone permission is required');
        }
      })
      .catch(err => console.warn('Permission error:', err));
  }, []);

  const setupAudioProcessing = useCallback(() => {
    voiceProcessor.addFrameListener((frame: number[]) => {
      // Process volume data regardless of showVolume state since it's cheap
      const db = calculateDbFS(frame);
      const volBuf = volumeBufferRef.current;
      volBuf.push(db);
      if (volBuf.length > windowSize) volBuf.shift();
      rawVolumeRef.current = getAverage(volBuf);

      // Only process pitch if showPitch is true
      if (showPitchRef.current) {
        // Reuse Float32Array if possible to reduce memory allocation
        if (!float32FrameRef.current || float32FrameRef.current.length !== frame.length) {
          float32FrameRef.current = new Float32Array(frame.length);
        }
        
        // Convert the frame to a Float32Array (assuming frame is in PCM 16-bit format)
        for (let i = 0; i < frame.length; i++) {
          float32FrameRef.current[i] = frame[i] / 32768;  // Normalize to [-1, 1]
        }

        // Run pitch detection using YIN
        const detectedPitch = pitchDetector(float32FrameRef.current);

        if (detectedPitch && detectedPitch >= 50 && detectedPitch <= 1000) {
          const buf = pitchBufferRef.current;
          buf.push(detectedPitch);
          if (buf.length > windowSize) buf.shift();
          rawPitchRef.current = getAverage(buf);
          
          if (!speakingRef.current) {
            speakingRef.current = true;
            setIsSpeaking(true);
          }
        } else if (speakingRef.current) {
          speakingRef.current = false;
          setIsSpeaking(false);
        }
      }
    });

    // Update UI state only when values have meaningfully changed
    updateIntervalRef.current = setInterval(() => {
      const currentPitch = rawPitchRef.current;
      const currentVolume = rawVolumeRef.current;

      setPitch(prev => hasChanged(prev, currentPitch) ? currentPitch : prev);
      setVolume(prev => hasChanged(prev, currentVolume) ? currentVolume : prev);
    }, 100);
  }, []);

  // Cleanup audio processing
  const cleanupAudioProcessing = useCallback(() => {
    stopRecording();
    voiceProcessor.clearFrameListeners();

    // Clear the update interval
    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
      updateIntervalRef.current = null;
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      // When screen comes into focus
      console.log('PitchPaceDetect screen focused - starting microphone');
      setupAudioProcessing();
      startRecording();
      setIsRecording(true);

      // Cleanup when screen loses focus
      return () => {
        console.log('PitchPaceDetect screen unfocused - stopping microphone');
        cleanupAudioProcessing();
        setIsRecording(false);
      };
    }, [setupAudioProcessing, cleanupAudioProcessing]),
  );

  return {
    showPitch,
    setShowPitch,
    pitch,
    showVolume,
    setShowVolume,
    volume,
    isRecording,
    isSpeaking,
    rawPitchRef,
    rawVolumeRef,
  };
};
