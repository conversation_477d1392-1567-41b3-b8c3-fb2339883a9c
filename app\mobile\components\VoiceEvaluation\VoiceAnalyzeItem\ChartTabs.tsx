import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import Chart from './Chart';
import TabButton from '../TabButton/TabButton';

type ChartTabsProps = {
  data: string;
};

const ChartTabs: React.FC<ChartTabsProps> = ({ data }) => {
  const [activeTab, setActiveTab] = useState<'avqi' | 'abi'>('avqi');
  const [filteredAvqiData, setFilteredAvqiData] = useState('{}');
  const [filteredAbiData, setFilteredAbiData] = useState('{}');

  // Parse and filter data once on component mount or when data changes
  useEffect(() => {
    if (!data) return;

    try {
      const parsedData = JSON.parse(data);

      // Process AVQI metrics
      const avqiData: Record<string, any> = {};
      const avqiKeys = [
        'value', 'avqi', 'cpps', 'hnr',
        'shimmer_percent', 'shimmer_db',
        'slope', 'tilt'
      ];

      // Ensure AVQI is always included if available
      if (parsedData['avqi'] !== undefined || parsedData['value'] !== undefined) {
        avqiData['avqi'] = parsedData['avqi'] !== undefined ? parsedData['avqi'] : parsedData['value'];
      }

      // Add other AVQI metrics
      avqiKeys.forEach(key => {
        if (parsedData[key] !== undefined) {
          avqiData[key] = parsedData[key];
        }
      });

      // Filter for ABI metrics
      const abiData: Record<string, any> = {};
      const abiKeys = [
        'abi',
        'abi_cpps', 'cpps',
        'abi_gne', 'gne',
        'h1h2',
        'hnrd',
        'abi_jitter', 'jitter',
        'abi_shimmer_db', 'shimmer_db',
        'abi_shimmer_percent', 'shimmer_percent',
      ];

      // Check if this is ABI data based on available keys
      const isAbiData =
        parsedData.hasOwnProperty('abi') ||
        parsedData.hasOwnProperty('abi_cpps') ||
        parsedData.hasOwnProperty('abi_gne') ||
        parsedData.hasOwnProperty('h1h2') ||
        parsedData.hasOwnProperty('hnrd');

      // Process ABI value
      if (parsedData['abi'] !== undefined) {
        abiData['abi'] = parsedData['abi'];
      } else if (parsedData.abi && parsedData.abi.value !== undefined) {
        abiData['abi'] = parsedData.abi.value;
      } else if (parsedData['value'] !== undefined && isAbiData) {
        abiData['abi'] = parsedData['value'];
      }

      // Process key metrics
      if (parsedData['h1h2'] !== undefined) {
        abiData['h1h2'] = parsedData['h1h2'];
      } else if (parsedData['abi_h1h2'] !== undefined) {
        abiData['h1h2'] = parsedData['abi_h1h2'];
      }

      if (parsedData['hnrd'] !== undefined) {
        abiData['hnrd'] = parsedData['hnrd'];
      } else if (parsedData['abi_hnrd'] !== undefined) {
        abiData['hnrd'] = parsedData['abi_hnrd'];
      }

      // Extract nested ABI metrics
      if (parsedData.abi && typeof parsedData.abi === 'object') {
        Object.entries(parsedData.abi).forEach(([key, value]) => {
          if (key !== 'encoded_plot') {
            const normalizedKey = key === 'value' ? 'abi' : key;
            abiData[normalizedKey] = value;
          }
        });
      }

      // Process remaining metrics
      abiKeys.forEach(key => {
        if (parsedData[key] !== undefined) {
          abiData[key] = parsedData[key];
        }
      });

      // Store the filtered data
      const hasAvqi = Object.keys(avqiData).length > 0;
      const hasAbi = Object.keys(abiData).length > 0;

      // Update state with filtered data
      setFilteredAvqiData(JSON.stringify(avqiData));
      setFilteredAbiData(JSON.stringify(abiData));

      // Auto-select the appropriate tab based on available data
      if ((activeTab === 'avqi' && !hasAvqi && hasAbi) ||
        (activeTab === 'abi' && !hasAbi && hasAvqi)) {
        setActiveTab(hasAvqi ? 'avqi' : 'abi');
      }
    } catch (error) {
      console.error('Error parsing chart data:', error);
    }
  }, [data]);

  return (
    <View style={styles.container}>
      {/* Tab Navigation - AVQI tab first */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 5,
        marginBottom: 5,
        paddingHorizontal: 20,
      }}>
        <TabButton
          title="AVQI Chart"
          isActive={activeTab === 'avqi'}
          onPress={() => setActiveTab('avqi')}
          variant="chart"
        />
        <TabButton
          title="ABI Chart"
          isActive={activeTab === 'abi'}
          onPress={() => setActiveTab('abi')}
          variant="chart"
        />
      </View>

      {/* Chart Display */}
      <View style={styles.chartContainer}>
        {activeTab === 'avqi' ? (
          <Chart data={filteredAvqiData} />
        ) : (
          <Chart data={JSON.stringify({ ...JSON.parse(filteredAbiData), _chartType: 'ABI' })} />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  chartContainer: {
    width: '100%',
    position: 'relative',
  },
});

export default ChartTabs;