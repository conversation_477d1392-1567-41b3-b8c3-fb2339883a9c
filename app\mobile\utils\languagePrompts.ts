// Language-specific text prompts for voice recording
export interface LanguagePrompt {
  connectedSpeech: string;
  sustainedVowel: string;
  instruction: string;
}

export const languagePrompts: Record<string, LanguagePrompt> = {
  // English
  'en-US': {
    connectedSpeech: 'When light strikes raindrops in the air, they act like a prism and form a rainbow',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: 'Align your eyes with the on-screen eyes frame, make sure the microphone is within 10cm from your face. Click the RECORD button and read the following sentence out loud.',
  },
  // Vietnamese
  'vi-VN': {
    connectedSpeech: '<PERSON>hi ánh sáng mặt trời chiếu vào những hạt mưa trong không khí, chúng hoạt động giống như một lăng kính và tạo thành cầu vồng.',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: 'Căn chỉnh mắt của bạn với khung mắt trên màn hình, đảm bảo micrô cách mặt bạn trong vòng 10cm. Nhấp vào nút GHI ÂM và đọc to câu sau đây.',
  },
  // Spanish
  'es-ES': {
    connectedSpeech: 'El viento del norte y el sol discutían sobre cuál de los dos era más fuerte cuando un viajero apareció con un abrigo puesto.',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: 'Alinee sus ojos con el marco de ojos en pantalla, asegúrese de que el micrófono esté a menos de 10 cm de su cara. Haga clic en el botón GRABAR y lea la siguiente frase en voz alta.',
  },
  // German
  'de-DE': {
    connectedSpeech: 'Der Nordwind und die Sonne stritten sich, wer von ihnen der Stärkere sei, als ein Reisender vorbeikam, in einen warmen Mantel gehüllt.',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: 'Richten Sie Ihre Augen am Augenrahmen auf dem Bildschirm aus und stellen Sie sicher, dass sich das Mikrofon in einem Abstand von 10 cm von Ihrem Gesicht befindet. Klicken Sie auf die Schaltfläche AUFNAHME und lesen Sie den folgenden Satz laut vor.',
  },
  // French
  'fr-FR': {
    connectedSpeech: 'Le Vent du Nord et le Soleil se disputaient pour savoir lequel des deux était le plus fort.',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: 'Alignez vos yeux avec le cadre des yeux à l\'écran, assurez-vous que le microphone est à moins de 10 cm de votre visage. Cliquez sur le bouton ENREGISTRER et lisez la phrase suivante à haute voix.',
  },
  // Italian
  'it-IT': {
    connectedSpeech: 'Il Vento del Nord e il Sole si misero a litigare su chi dei due fosse il più forte, quando arrivò un viaggiatore avvolto in un pesante mantello.',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: 'Allinea i tuoi occhi con la cornice degli occhi sullo schermo, assicurati che il microfono sia entro 10 cm dal tuo viso. Fai clic sul pulsante REGISTRA e leggi ad alta voce la seguente frase.',
  },
  // Japanese
  'ja-JP': {
    connectedSpeech: '北風と太陽がどちらが強いかで言い争っているところに、厚いコートを着た旅人が通りかかりました。',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: '画面上の目のフレームに目を合わせ、マイクが顔から10cm以内にあることを確認してください。録音ボタンをクリックして、次の文を声に出して読んでください。',
  },
  // Korean
  'ko-KR': {
    connectedSpeech: '북풍과 태양이 누가 더 강한지 다투고 있을 때, 두꺼운 외투를 입은 여행자가 지나갔습니다.',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: '화면의 눈 프레임에 눈을 맞추고 마이크가 얼굴에서 10cm 이내에 있는지 확인하세요. 녹음 버튼을 클릭하고 다음 문장을 소리 내어 읽으세요.',
  },
  // Russian
  'ru-RU': {
    connectedSpeech: 'Северный ветер и солнце спорили, кто из них сильнее, когда мимо проходил путешественник в тёплом пальто.',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: 'Выровняйте глаза с рамкой глаз на экране, убедитесь, что микрофон находится на расстоянии 10 см от вашего лица. Нажмите кнопку ЗАПИСЬ и прочитайте следующее предложение вслух.',
  },
  // Chinese
  'zh-CN': {
    connectedSpeech: '北风和太阳争论谁更强大，当一个穿着厚外套的旅人经过时。',
    sustainedVowel: 'aaaaaaaaa...',
    instruction: '将您的眼睛与屏幕上的眼睛框对齐，确保麦克风距离您的脸部10厘米以内。点击录制按钮，大声朗读以下句子。',
  },
};

// Default to English if the language code is not found
export const getLanguagePrompt = (languageCode: string): LanguagePrompt => {
  return languagePrompts[languageCode] || languagePrompts['en-US'];
};
