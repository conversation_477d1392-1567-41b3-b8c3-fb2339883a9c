#!/usr/bin/praat --run

# TITLE OF THE SCRIPT: ACOUSTIC BREATHINESS INDEX (ABI) for backend use

form ABI Input
    sentence cs_file cs.wav
    sentence sv_file sv.wav
endform

# Path resolution
if startsWith(cs_file$, "/") = 0
    cs_path$ = shellDirectory$ + "/" + cs_file$
else
    cs_path$ = cs_file$
endif

if startsWith(sv_file$, "/") = 0
    sv_path$ = shellDirectory$ + "/" + sv_file$
else
    sv_path$ = sv_file$
endif

# Set fixed output path
abi_path$ = shellDirectory$ + "/outputs/praat/abi.txt"

# Read audio files
Read from file: cs_path$
Rename: "cs"
Read from file: sv_path$
Rename: "sv"

# High-pass filter
select Sound cs
Filter (stop Hann band)... 0 34 0.1
Rename... cs2

# Detect voiced segments
select Sound cs2
Copy... original
samplingRate = Get sampling frequency
intermediateSamples = Get sampling period
Create Sound... onlyVoice 0 0.001 'samplingRate' 0
select Sound original
To TextGrid (silences)... 50 0.003 -25 0.1 0.1 silence sounding
select Sound original
plus TextGrid original
Extract intervals where... 1 no "does not contain" silence
Concatenate
select Sound chain
Rename... onlyLoud
select TextGrid original
Remove

# Filter short unvoiced parts
select Sound onlyLoud
signalEnd = Get end time
windowBorderLeft = Get start time
windowWidth = 0.03
windowBorderRight = windowBorderLeft + windowWidth
globalPower = Get power in air
voicelessThreshold = globalPower * (30 / 100)

select Sound onlyLoud
extremeRight = signalEnd - windowWidth
while windowBorderRight < extremeRight
    Extract part... 'windowBorderLeft' 'windowBorderRight' Rectangular 1.0 no
    select Sound onlyLoud_part
    partialPower = Get power in air
    if partialPower > voicelessThreshold
        call checkZeros 0
        if (zeroCrossingRate <> undefined) and (zeroCrossingRate < 3000)
            select Sound onlyVoice
            plus Sound onlyLoud_part
            Concatenate
            Rename... onlyVoiceNew
            select Sound onlyVoice
            Remove
            select Sound onlyVoiceNew
            Rename... onlyVoice
        endif
    endif
    select Sound onlyLoud_part
    Remove
    windowBorderLeft = windowBorderLeft + 0.03
    windowBorderRight = windowBorderLeft + 0.03
    select Sound onlyLoud
endwhile
select Sound onlyVoice

procedure checkZeros zeroCrossingRate
    start = 0.0025
    startZero = Get nearest zero crossing... 'start'
    findStart = startZero
    findStartZeroPlusOne = startZero + intermediateSamples
    startZeroPlusOne = Get nearest zero crossing... 'findStartZeroPlusOne'
    zeroCrossings = 0
    strips = 0
    while (findStart < 0.0275) and (findStart <> undefined)
        while startZeroPlusOne = findStart
            findStartZeroPlusOne = findStartZeroPlusOne + intermediateSamples
            startZeroPlusOne = Get nearest zero crossing... 'findStartZeroPlusOne'
        endwhile
        afstand = startZeroPlusOne - startZero
        strips = strips + 1
        zeroCrossings = zeroCrossings + 1
        findStart = startZeroPlusOne
    endwhile
    zeroCrossingRate = zeroCrossings / afstand
endproc

# Combine cs (voiced) and sv
select Sound sv
sv_start = Get start time
sv_end = Get end time
Extract part... sv_start sv_end Rectangular 1 no
Rename... sv2

select Sound onlyVoice
plus Sound sv2
Concatenate
Rename... abi

# Acoustic measures
select Sound abi
To PowerCepstrogram... 60 0.002 5000 50
cpps = Get CPPS... no 0.01 0.001 60 330 0.05 Parabolic 0.001 0 Straight Robust

# Pitch and PointProcess creation
select Sound abi
To Pitch... 0 70 600
Rename... pitch_abi

select Sound abi
plus Pitch pitch_abi
To PointProcess (cc)
Rename... point_abi

# Select all required objects in correct order
select Sound abi
plus Pitch pitch_abi
plus PointProcess point_abi

# Now it's safe to call Voice report
voiceReport$ = Voice report... 0 0 70 600 1.3 1.6 0.03 0.45

jitterLocal = extractNumber (voiceReport$, "Jitter (local): ") * 100
shimmerLocal = extractNumber (voiceReport$, "Shimmer (local): ") * 100
shimmerLocaldB = extractNumber (voiceReport$, "Shimmer (local, dB): ")
psd = extractNumber (voiceReport$, "Standard deviation of period: ")

select Sound abi
To Harmonicity (gne)... 500 4500 1000 80
gneMaximum = Get maximum

# Placeholder measures from LTAS (mock)
finalhfno6000 = 0.5
finalhnrd = 0.8
finalH1H2 = 1.2

# ABI Calculation
abi = (5.0447730915-(0.172*cpps)-(0.193*jitterLocal)-(1.283*gneMaximum)-(0.396*finalhfno6000)+(0.01*finalhnrd)+(0.017*finalH1H2)+(1.473*shimmerLocaldB)-(0.088*shimmerLocal)-(68.295*psd))*2.9257400394

# Output to file
appendFileLine: abi_path$, "ABI: " + fixed$(abi, 2)
appendFileLine: abi_path$, "CPPS: " + fixed$(cpps, 2)
appendFileLine: abi_path$, "Jitter: " + fixed$(jitterLocal, 2)
appendFileLine: abi_path$, "GNE: " + fixed$(gneMaximum, 2)
appendFileLine: abi_path$, "HFNO: " + fixed$(finalhfno6000, 2)
appendFileLine: abi_path$, "HNRD: " + fixed$(finalhnrd, 2)
appendFileLine: abi_path$, "H1H2: " + fixed$(finalH1H2, 2)
appendFileLine: abi_path$, "Shimmer (dB): " + fixed$(shimmerLocaldB, 2)
appendFileLine: abi_path$, "Shimmer (%): " + fixed$(shimmerLocal, 2)
appendFileLine: abi_path$, "Period SD: " + fixed$(psd, 5) + newline$

# Clean up
select all
minus Sound cs
minus Sound sv
minus Sound abi
Remove
