import {Dispatch, SetStateAction} from 'react';
import {SQLiteDatabase} from 'react-native-sqlite-storage';
import RNFS from 'react-native-fs';

export type AudioFile = {
  audio_id: number;
  filePath: string;
  fileSvPath: string;
  name: string;
  isAnalysed: boolean;
  status: string;
  result: string;
  taskId: string;
  spectrogramPath: string;
  waveFormPath: string;
  avqiGram: string;
  abiGram: string;
};

export const fetchAudioFiles = async (
  db: SQLiteDatabase,
  setAudioFiles: Dispatch<SetStateAction<AudioFile[]>>,
) => {
  try {
    const result = await db.executeSql('SELECT * FROM AudioFiles;');
    const rows = result[0].rows;
    let audioFiles: AudioFile[] = [];

    for (let i = 0; i < rows.length; i++) {
      audioFiles.push(rows.item(i));
    }

    setAudioFiles(audioFiles.reverse());
  } catch (error) {
    console.error('Error fetching AudioFiles:', error);
    return [];
  }
};

export const insertAudioFile = async (
  db: SQLiteDatabase,
  filePath: string,
  fileSvPath: string,
  name: string,
  status: string,
  result: string,
  taskId: string,
  setAudioFiles: Dispatch<SetStateAction<AudioFile[]>>,
) => {
  try {
    await db.executeSql(
      'INSERT INTO AudioFiles (filePath, fileSvPath, name, status, result, taskId, spectrogramPath, waveFormPath, avqiGram, abiGram) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);',
      [filePath, fileSvPath, name, status, result, taskId, '', '', '', ''],
    );
    await fetchAudioFiles(db, setAudioFiles);
    db.close();
  } catch (error) {
    console.error('Error inserting AudioFile:', error);
  }
};

export const createNewAudioTable = async (db: SQLiteDatabase) => {
  try {
    const results = await db.executeSql('SELECT * FROM AudioFiles;');
    const rows = results[0].rows;
    let audioFiles: AudioFile[] = [];

    for (let i = 0; i < rows.length; i++) {
      audioFiles.push(rows.item(i));
    }
    await db.executeSql(
      `CREATE TABLE IF NOT EXISTS new_audio (
        phrase_id INTEGER PRIMARY KEY AUTOINCREMENT,
        text TEXT,
        isLiked BOOLEAN DEFAULT 0,
        useCount INTEGER DEFAULT 0,
        lastUsed TEXT
      );`,
    );

    audioFiles.map(async audio => {
      await db.executeSql(
        'INSERT INTO AudioFiles (filePath, fileSvPath, name, status, result, taskId, spectrogramPath, waveFormPath, avqiGram, abiGram) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);',
        [
          audio.filePath,
          audio.fileSvPath,
          audio.name,
          audio.status,
          audio.result,
          audio.taskId,
          audio.spectrogramPath,
          audio.waveFormPath,
          audio.avqiGram,
          audio.abiGram,
        ],
      );
    });

    await db.executeSql('DROP TABLE AudioFiles;');
    await db.executeSql('ALTER TABLE new_audio RENAME TO AudioFiles;');
  } catch (error) {
    console.error('Error creating new table:', error);
  }
};

export const createTable = async (db: SQLiteDatabase) => {
  try {
    await db.executeSql(
      'CREATE TABLE IF NOT EXISTS AudioFiles (audio_id INTEGER PRIMARY KEY AUTOINCREMENT, filePath TEXT, fileSvPath TEXT, name TEXT, isAnalysed BOOLEAN DEFAULT 0, status TEXT, result TEXT, taskId TEXT, spectrogramPath TEXT, waveFormPath TEXT, avqiGram TEXT, abiGram TEXT);',
    );
  } catch (error) {
    console.error('Error creating AudioFiles table:', error);
  }
};

export const updateAudioFile = async (
  db: SQLiteDatabase,
  audioFile: AudioFile,
  setAudioFiles: Dispatch<SetStateAction<AudioFile[]>>,
) => {
  try {
    await db.executeSql(
      'UPDATE AudioFiles SET isAnalysed = ?, status = ?, result = ?, taskId = ?, spectrogramPath = ?, waveFormPath = ?, avqiGram = ?, abiGram = ?  WHERE audio_id = ?;',
      [
        audioFile.isAnalysed,
        audioFile.status,
        audioFile.result,
        audioFile.taskId,
        audioFile.spectrogramPath,
        audioFile.waveFormPath,
        audioFile.avqiGram,
        audioFile.abiGram,
        audioFile.audio_id,
      ],
    );
    await fetchAudioFiles(db, setAudioFiles);
  } catch (error) {
    console.error('Error updating AudioFile:', error);
  }
};

export const deleteAudioFile = async (
  db: SQLiteDatabase,
  audioFile: AudioFile,
  setAudioFiles: Dispatch<SetStateAction<AudioFile[]>>,
) => {
  try {
    // Delete the database entry
    await db.executeSql('DELETE FROM AudioFiles WHERE audio_id = ?;', [
      audioFile.audio_id,
    ]);

    // Delete the actual audio files from storage
    try {
      if (audioFile.filePath) {
        const csFileData = JSON.parse(audioFile.filePath);
        if (csFileData && csFileData.uri && csFileData.uri.startsWith('file://')) {
          await RNFS.unlink(csFileData.uri).catch(err =>
            console.log('Error deleting CS file:', err)
          );
        }
      }

      if (audioFile.fileSvPath) {
        const svFileData = JSON.parse(audioFile.fileSvPath);
        if (svFileData && svFileData.uri && svFileData.uri.startsWith('file://')) {
          await RNFS.unlink(svFileData.uri).catch(err =>
            console.log('Error deleting SV file:', err)
          );
        }
      }
    } catch (fileError) {
      console.error('Error deleting audio files from storage:', fileError);
    }

    // Update the audio files list
    await fetchAudioFiles(db, setAudioFiles);
    db.close();
  } catch (error) {
    console.error('Error deleting audio file:', error);
  }
};
