import React, { FC, useEffect, useRef, useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  useWindowDimensions,
  AppState,
} from 'react-native';
import { useTheme } from 'react-native-paper';
import { WebView, WebViewMessageEvent } from 'react-native-webview';

interface RealTimeChartProps {
  volumeData: number;
  pitchData: number | null;
  isSpeaking: boolean;
  showVolume: boolean;
  showPitch: boolean;
}

const BUFFER_SIZE = 50;

const RealTimeChart: FC<RealTimeChartProps> = ({
  volumeData,
  pitchData,
  isSpeaking,
  showVolume,
  showPitch,
}) => {
  const theme = useTheme();
  const webViewRef = useRef<WebView>(null);
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const appState = useRef(AppState.currentState);

  const dataBufferRef = useRef({
    volume: Array(BUFFER_SIZE).fill(10),
    pitch: Array(BUFFER_SIZE).fill(null),
    isPitchActive: false,
    lastPitch: null as number | null,
    lastUpdate: 0,
  });

  const repeatRef = useRef(0);

  // Track if chart is ready
  const [chartReady, setChartReady] = useState(false);

  // Calculate dimensions based on screen size
  const chartWidth = windowWidth * 0.95;
  const chartHeight = windowHeight * 0.35;

  // Message handler for chart initialization
  const onMessage = (event: WebViewMessageEvent) => {
    if (event.nativeEvent.data === 'chartReady') {
      setChartReady(true);
    }
  };

  // Track app state changes
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      appState.current = nextAppState;
    });

    return () => subscription.remove();
  }, []);

  // Process new data only when props change
  useEffect(() => {
    const now = Date.now();
    if (!chartReady) return;

    const validPitch =
      pitchData !== null && pitchData > 80 && pitchData < 1000
        ? pitchData
        : null;
    const validVolume =
      volumeData >= 0 && volumeData < 200
        ? volumeData
        : dataBufferRef.current.volume[BUFFER_SIZE - 1] || 10;

    for (let i = 0; i < BUFFER_SIZE - 1; i++) {
      dataBufferRef.current.volume[i] = dataBufferRef.current.volume[i + 1];
      dataBufferRef.current.pitch[i] = dataBufferRef.current.pitch[i + 1];
    }

    dataBufferRef.current.volume[BUFFER_SIZE - 1] = validVolume;
    if (validPitch === dataBufferRef.current.lastPitch) {
      repeatRef.current++;
    } else {
      repeatRef.current = 0;
      dataBufferRef.current.lastPitch = validPitch;
    }

    const finalPitch = repeatRef.current > 4 ? null : validPitch;
    dataBufferRef.current.pitch[BUFFER_SIZE - 1] = finalPitch;
    dataBufferRef.current.lastUpdate = now;
  }, [volumeData, pitchData, chartReady]);

  // Set up pitch range bands once when chart is ready
  useEffect(() => {
    if (!chartReady || !webViewRef.current) return;

    // Set up the pitch range bands once
    const setupRangeScript = `
      if (window.myChart) {
        // Set up range bands based on visibility
        window.myChart.data.datasets[2].data = Array(${BUFFER_SIZE}).fill(${showPitch ? 255 : 'null'});
        window.myChart.data.datasets[3].data = Array(${BUFFER_SIZE}).fill(${showPitch ? 165 : 'null'});
        window.myChart.data.datasets[4].data = Array(${BUFFER_SIZE}).fill(${showPitch ? 155 : 'null'});
        window.myChart.data.datasets[5].data = Array(${BUFFER_SIZE}).fill(${showPitch ? 85 : 'null'});
        window.myChart.update('none');
      }
    `;

    webViewRef.current.injectJavaScript(setupRangeScript);
  }, [chartReady, showPitch]);

  // Use interval for consistent chart updates
  useEffect(() => {
    if (!chartReady || !webViewRef.current) return;

    let interval: NodeJS.Timeout;

    // Only run interval when app is active
    if (appState.current === 'active') {
      interval = setInterval(() => {
        // Skip updates if webView is gone or chart isn't ready
        if (!webViewRef.current || !chartReady) return;

        const pitchValue = showPitch
          ? dataBufferRef.current.pitch[BUFFER_SIZE - 1]
          : null;
        const volumeValue = showVolume
          ? dataBufferRef.current.volume[BUFFER_SIZE - 1]
          : null;

        // Optimized script that only shifts and pushes new data points
        const script = `
          if (window.myChart) {
            // Update only the actual data points with shift+push
            window.myChart.data.datasets[0].data.shift();
            window.myChart.data.datasets[0].data.push(${JSON.stringify(pitchValue)});
            
            window.myChart.data.datasets[1].data.shift();
            window.myChart.data.datasets[1].data.push(${JSON.stringify(volumeValue)});
            
            requestAnimationFrame(() => window.myChart.update('none'));
          }
        `;

        webViewRef.current.injectJavaScript(script);
      }, 100); // ~10 FPS
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [chartReady, showPitch, showVolume, appState.current]);

  // Create HTML content only once using useMemo
  const htmlContent = useMemo(
    () => `
     <!DOCTYPE html>
     <html>
       <head>
         <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
         <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
         <style>
           body { 
             margin: 0; 
             padding: 0;
             background-color: ${theme.colors.background};
             height: 100vh;
             overflow: hidden;
           }
           canvas { 
             width: 100% !important;
             height: 100% !important;
           }
           #chartContainer {
             width: 100%;
             height: 100%;
             display: flex;
             justify-content: center;
             align-items: center;
           }
         </style>
       </head>
       <body>
         <div id="chartContainer">
           <canvas id="myChart"></canvas>
         </div>
         <script>
           // Create blank labels array
           const xLabels = Array(${BUFFER_SIZE}).fill('');
           
           // Create datasets array - only include range datasets if pitch is shown
           const datasets = [
             {
               label: 'Pitch',
               data: Array(${BUFFER_SIZE}).fill(null),
               borderColor: 'rgb(255, 123, 123)',
               backgroundColor: 'rgba(255, 123, 123, 0.1)',
               borderWidth: 2,
               tension: 0.3,
               pointRadius: 0,
               fill: false,
               yAxisID: 'y-pitch',
               spanGaps: false
             },
             {
               label: 'Volume',
               data: Array(${BUFFER_SIZE}).fill(null),
               borderColor: 'rgb(75, 158, 249)',
               backgroundColor: 'rgba(75, 158, 249, 0.1)',
               borderWidth: 2,
               tension: 0.3,
               pointRadius: 0,
               fill: false,
               yAxisID: 'y-volume',
               spanGaps: true
             }
           ];
           
           // Only add range datasets if pitch is shown
           if (${showPitch}) {
             // Female range indicator (165-255 Hz)
             datasets.push({
               label: 'Female Range',
               data: Array(${BUFFER_SIZE}).fill(255),
               borderWidth: 0,
               borderColor: 'transparent',
               backgroundColor: 'rgba(217, 227, 36, 0.2)',
               fill: '+1',
               yAxisID: 'y-pitch',
               pointRadius: 0
             });
             
             datasets.push({
               data: Array(${BUFFER_SIZE}).fill(165),
               borderWidth: 0,
               borderColor: 'transparent',
               fill: false,
               yAxisID: 'y-pitch',
               pointRadius: 0
             });
             
             // Male range indicator (85-155 Hz)
             datasets.push({
               label: 'Male Range',
               data: Array(${BUFFER_SIZE}).fill(155),
               borderWidth: 0,
               borderColor: 'transparent',
               backgroundColor: 'rgba(79, 195, 199, 0.2)',
               fill: '+1',
               yAxisID: 'y-pitch',
               pointRadius: 0
             });
             
             datasets.push({
               data: Array(${BUFFER_SIZE}).fill(85),
               borderWidth: 0,
               borderColor: 'transparent',
               fill: false,
               yAxisID: 'y-pitch',
               pointRadius: 0
             });
           }
           
           // Create chart with initial empty data
           const ctx = document.getElementById('myChart').getContext('2d');
           window.myChart = new Chart(ctx, {
             type: 'line',
             data: {
               labels: xLabels,
               datasets: datasets
             },
             options: {
               devicePixelRatio: 0.75, // Lower resolution for better performance
               responsive: true,
               maintainAspectRatio: false,
               animation: false,
               events: [],
               elements: {
                 line: {
                   tension: 0.3,
                   capBezierPoints: true
                 }
               },
               layout: {
                 padding: {
                   left: 5,
                   right: 5,
                   top: 5,
                   bottom: 5
                 }
               },
               interaction: {
                 mode: 'none'
               },
               scales: {
                 x: {
                   display: false
                 },
                 'y-pitch': {
                   type: 'linear',
                   display: true,
                   position: 'left',
                   title: {
                     display: false
                   },
                   min: 50,
                   max: 550,
                   ticks: {
                     stepSize: 100,
                     font: {
                       size: 10
                     },
                   
                   },
                   grid: {
                     color: 'rgba(75, 72, 72, 0.2)'
                   }
                 },
                 'y-volume': {
                   type: 'linear',
                   display: true,
                   position: 'right',
                   title: {
                     display: false
                   },
                   min: 0,
                   max: 100,
                   ticks: {
                     stepSize: 20,
                     font: {
                       size: 10
                     },
            
                   },
                   grid: {
                     color: 'rgba(75, 72, 72, 0.2)'
                   }
                 }
               },
               plugins: {
                 legend: {
                   display: false
                 },
                 tooltip: {
                   enabled: false
                 }
               }
             }
           });
           
           // Notify React Native that chart is ready
           window.addEventListener('load', function() {
             window.ReactNativeWebView.postMessage('chartReady');
           });
         </script>
       </body>
     </html>
   `,
    [theme.colors.background, showPitch],
  );

  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.axisLabelsRow, { width: chartWidth }]}>
        <Text style={[styles.axisLabel, { color: '#FF7870' }]}>Hz</Text>
        <Text style={[styles.axisLabel, { color: '#3C80F3' }]}>dB</Text>
      </View>
      <WebView
        ref={webViewRef}
        source={{ html: htmlContent }}
        style={[styles.chart, { width: chartWidth, height: chartHeight }]}
        scrollEnabled={false}
        onMessage={onMessage}
        onError={e => console.warn('WebView error:', e.nativeEvent)}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        thirdPartyCookiesEnabled={false}
        cacheEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chart: {
    borderRadius: 10,
  },
  axisLabelsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  axisLabel: {
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default RealTimeChart;
