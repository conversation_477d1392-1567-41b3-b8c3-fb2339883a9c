import {Dispatch, SetStateAction, useEffect, useRef, useState} from 'react';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {
  Phrase,
  deletePhrase,
  updatePhraseIsLiked,
  updatePhraseLanguage,
  updatePhraseUseCount,
} from '../../../services/phraseManageService';
import SQLite from 'react-native-sqlite-storage';
import countryFlags from '../../../utils/language';
import moment from 'moment';

const useSentenceItem = (
  phrase: Phrase,
  currentTime: string,
  setPlayingPreview: Dispatch<SetStateAction<boolean>>,
  isSpeaking: boolean,
) => {
  const [text, setText] = useState('');
  const [lastUsed, setLastUsed] = useState('');

  const [showPreview, setShowPreview] = useState<boolean>(false);

  const [changeLanguage, setChangeLanguage] = useState<boolean>(false);

  const {setPhrases, sortOption, autoEndPreview} = useSorting();

  const languageCode = phrase.languageCode;

  const {iconImages} = countryFlags();

  const handleChangeLanguage = async (languageCode: string) => {
    const db = await SQLite.openDatabase({
      name: 'phrases.db',
      location: 'default',
    });

    await updatePhraseLanguage(
      db,
      phrase,
      languageCode,
      setPhrases,
      sortOption,
    );
    setChangeLanguage(false);
  };

  useEffect(() => {
    if (phrase !== null) {
      setText(phrase.text);
      setLastUsed(moment(phrase.lastUsed).fromNow());
    }
    if (isSpeaking) {
      setShowPreview(true);
      setPlayingPreview(true);
    } else if (autoEndPreview) {
      setShowPreview(false);
      setPlayingPreview(false);
    }
  }, [isSpeaking]);

  useEffect(() => {
    if (phrase !== null) {
      setLastUsed(moment(phrase.lastUsed).fromNow());
    }
  }, [phrase.lastUsed, currentTime]);

  const handleDelete = async () => {
    const db = await SQLite.openDatabase({
      name: 'phrases.db',
      location: 'default',
    });
    if (setPhrases) {
      await deletePhrase(db, phrase.phrase_id, setPhrases, sortOption);
    }
  };

  const handleUpdate = async () => {
    const db = await SQLite.openDatabase({
      name: 'phrases.db',
      location: 'default',
    });
    if (setPhrases) {
      const updatedPhrase = {...phrase, isLiked: !phrase.isLiked};
      setPhrases(prev => {
        return prev.map(p => {
          if (p.phrase_id === updatedPhrase.phrase_id) {
            return updatedPhrase;
          } else {
            return p;
          }
        });
      });
      await updatePhraseIsLiked(
        db,
        phrase,
        !phrase.isLiked,
        setPhrases,
        sortOption,
      );
    }
  };

  const handleUpdateLastUsed = async () => {
    console.log('called update last used');
    const db = await SQLite.openDatabase({
      name: 'phrases.db',
      location: 'default',
    });
    await updatePhraseUseCount(
      db,
      phrase,
      phrase.useCount + 1,
      setPhrases,
      sortOption,
    );
  };

  return {
    handleDelete,
    handleUpdate,
    handleChangeLanguage,
    handleUpdateLastUsed,
    text,
    lastUsed,
    iconImages,
    languageCode,
    showPreview,
    setShowPreview,
    changeLanguage,
    setChangeLanguage,
  };
};

export default useSentenceItem;
