import {StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';

export const ExerciseSheetStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    addButton: {
      alignItems: 'flex-start',
      // height: 30,
    },
    optionItemLabel: {
      paddingLeft: 12,
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      gap: 1,
    },
    sectionDivider: {
      width: '120%',
      height: 1,
      backgroundColor: '#ccc',
      marginHorizontal: 10,
      marginLeft: '-5%',
      marginTop: -5,
    },
    modalContainer: {
      backgroundColor: 'white',
      borderRadius: 20,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5, // for Android
      padding: 20,
      marginHorizontal: 30,
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 5,
    },
    modalMessage: {
      fontSize: 18,
    },
    buttonContainer: {
      flexDirection: 'row',
      display: 'flex',
      justifyContent: 'space-between',
      gap: 20,
      marginTop: 25,
    },
    deleteButton: {
      width: 120,
      height: 50,
    },
    textStyle: {
      fontWeight: 'bold',
      fontSize: 18,
    },
    input: {
      textAlignVertical: 'top',
      fontSize: 14,
      width: '100%',
      height: 45,
    },
    button: {
      marginTop: 10,
      borderWidth: 0,
    },
    bottomSheet: {
      width: '100%',
      rowGap: 10,
      padding: 10,
      position: 'relative',
      paddingBottom: 130,
    },
    buttonSheetContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 20,
      position: 'absolute',
      bottom: 150,
      left: '32%',
      borderRadius: 50,
    },

    optionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 10,
      justifyContent: 'space-between',
      borderBottomWidth: 1,
      borderBottomColor: '#CED7DF', // or use theme.colors.outline if you want dynamic theming
      paddingBottom: 8, // optional: adds spacing between content and border
      marginBottom: 8,
    },
    counterText: {
      fontSize: 16,
      fontWeight: 'bold',
      marginLeft: 20,
      color: theme.colors.primary,
    },
    counterNumber: {
      fontSize: 20,
      fontWeight: 'bold',
      marginHorizontal: 20,
    },
    counterCount: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dropDownSelect: {
      width: '50%',
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
    },
    dropDownButon: {
      paddingHorizontal: 5,
      paddingVertical: 2,
      backgroundColor: theme.colors.primary,
    },
    dropDownButonText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: 'white',
    },
    inputTarget: {
      textAlignVertical: 'top',
      fontSize: 17,
      width: 70,
      height: 45,
    },
  });
};
