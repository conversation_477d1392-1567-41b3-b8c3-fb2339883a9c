import os
from dotenv import load_dotenv

# Load .env file
load_dotenv()

class Configs:
    CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", 'redis://localhost:6379/0')
    CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", 'redis://localhost:6379/0')

    UPLOAD_FOLDER_PATH = os.getenv("UPLOAD_FOLDER_PATH")
    PRAAT_BIN_PATH = os.getenv("PRAAT_BIN_PATH", "/usr/local/bin/praat")
    AVQI_SCRIPT_PATH = os.getenv("AVQI_SCRIPT_PATH", "/app/scripts/avqi_v3.praat")
    ABI_SCRIPT_PATH = os.getenv("ABI_SCRIPT_PATH", "/app/scripts/abi.praat")

    SECRET_KEY = os.getenv("SECRET_KEY", "secret-key")
    CLIENT_ID = os.getenv("CLIENT_ID")
    CLIENT_SECRET = os.getenv("CLIENT_SECRET")

    PRAAT_OUTPUT_DIR = os.getenv("PRAAT_OUTPUT_DIR", "./outputs/praat")
    TASK_OUTPUT_DIR = os.getenv("TASK_OUTPUT_DIR", "./outputs/tasks")
    
    DATABASE_URL = os.getenv("DATABASE_URL")