import os
from fastapi import <PERSON><PERSON><PERSON>
from app.routes import analyze, auth, task_status, uploads
from fastapi.middleware.cors import CORSMiddleware
from app.repository.db import engine, Base
from contextlib import asynccontextmanager
import logging
from fastapi.staticfiles import StaticFiles


logger = logging.getLogger("app")


@asynccontextmanager  # startup
async def lifespan(app: FastAPI):
    Base.metadata.create_all(bind=engine)
    logger.info("Connected to PostgreSQL database!")
    yield


app = FastAPI(lifespan=lifespan)


@app.get("/health")
def health_check():
    return {"status": "ok"}


app.include_router(analyze.router, prefix="/api/analyze")
app.include_router(auth.router, prefix="/api/auth")
app.include_router(task_status.router, prefix="/api/task")
app.include_router(uploads.router, prefix="/api/uploads")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
