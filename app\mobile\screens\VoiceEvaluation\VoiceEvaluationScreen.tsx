import { Plus } from 'lucide-react-native';
import { View } from 'react-native';
import { IconButton, Text } from 'react-native-paper';
import { ScrollView } from 'react-native-gesture-handler';
import { TopNav } from '../../components/TopNav/TopNav';
import VoiceAnalyzeItem from '../../components/VoiceEvaluation/VoiceAnalyzeItem/VoiceAnalyzeItem';
import useVoiceEvaluationStyle from './styles/VoiceEvaluationStyle';
import { useVoiceEvaluationScreen } from './hooks/useVoiceEvaluationScreen';
import { AudioFile } from '../../services/audioFileManageService';

const groupAudioFilesByDate = (audioFiles: AudioFile[]) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const thisWeekStart = new Date(today);
  thisWeekStart.setDate(today.getDate() - today.getDay());

  const groups: { [key: string]: AudioFile[] } = {
    'Today': [],
    'This Week': [],
  };

  audioFiles.forEach(file => {
    const dateMatch = file.name.match(/(\d{1,2})(st|nd|rd|th)\s+(\w+),\s+(\d{4})\s+(\d{2}):(\d{2})/);
    if (dateMatch) {
      const [, day, , month, year, hour, minute] = dateMatch;
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'];
      const monthIndex = monthNames.indexOf(month);

      if (monthIndex !== -1) {
        const fileDate = new Date(parseInt(year), monthIndex, parseInt(day), parseInt(hour), parseInt(minute));

        if (fileDate >= today) {
          groups['Today'].push(file);
        } else if (fileDate >= thisWeekStart) {
          groups['This Week'].push(file);
        }
      }
    }
  });

  return groups;
};

const VoiceEvaluationScreen = () => {
  const {
    audioFiles,
    theme,
    handleNavigateToRecordAudio,
  } = useVoiceEvaluationScreen();

  const style = useVoiceEvaluationStyle();
  const groupedFiles = groupAudioFilesByDate(audioFiles);

  return (
    <View style={style.mainContainer}>
      <TopNav title="Voice Evaluation"></TopNav>
      <ScrollView contentContainerStyle={style.ScrollViewStyled}>
        {/* Today Section */}
        {groupedFiles['Today'].length > 0 && (
          <View style={style.sectionContainer}>
            <Text style={style.sectionTitle}>Today</Text>
            <View style={style.sectionDivider}></View>
            {groupedFiles['Today'].map((item, index) => (
              <VoiceAnalyzeItem key={`today-${index}`} audioFile={item} />
            ))}
          </View>
        )}

        {/* This Week Section */}
        {groupedFiles['This Week'].length > 0 && (
          <View style={style.sectionContainer}>
            <Text style={style.sectionTitle}>This Week</Text>
            <View style={style.sectionDivider}></View>
            {groupedFiles['This Week'].map((item, index) => (
              <VoiceAnalyzeItem key={`week-${index}`} audioFile={item} />
            ))}
          </View>
        )}

        <View style={{ height: 84 }} />
      </ScrollView>
      <View style={style.SpeakIconStyled}>
        <IconButton
          icon={() => <Plus color="white" size={30} />}
          size={40}
          mode="contained-tonal"
          containerColor={theme.colors.primary}
          onPress={handleNavigateToRecordAudio}
        />
      </View>
    </View>
  );
};

export default VoiceEvaluationScreen;
