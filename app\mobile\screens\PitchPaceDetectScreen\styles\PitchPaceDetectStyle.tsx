
import { StyleSheet, useWindowDimensions } from 'react-native';
import { useTheme } from 'react-native-paper';

export const PitchPaceDetectStyle = () => {
  const theme = useTheme()
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();
  const scaleW = screenWidth / 375;
  const scaleH = screenHeight / 667;
  const dynamicPadding = Math.min(50 * scaleW, 80);

  return StyleSheet.create({
   
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
    },

    // Hz/dB above chart
    axisLabelsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginHorizontal: 16 * scaleW,
      marginTop: 8 * scaleH,
      marginBottom: 4 * scaleH,
    },
    axisLabel: {
      fontSize: 14 * scaleW,
      fontWeight: '500',
    },

    chartContainer: {
      flex: 1,
      marginHorizontal: 16 * scaleW,
      marginTop: 8 * scaleH,
    },

    // Toggles row
    toggleRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingVertical: 16 * scaleH,
      paddingHorizontal: dynamicPadding,
    },

    // Each pill + value
    pillContainer: {
      alignItems: 'center',
    },

    // Pill itself
    pill: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 6 * scaleH,
      paddingHorizontal: 16 * scaleW,
      borderRadius: 25 * scaleW,
      borderWidth: 2,
    },

    // Circle wrapper
    circleBase: {
      width: 24 * scaleW,
      height: 24 * scaleW,
      borderRadius: 12 * scaleW,
      marginRight: 8 * scaleW,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
    },

    // Inner dot (active only)
    dotInnerRed: {
      width: 12 * scaleW,
      height: 12 * scaleW,
      borderRadius: 6 * scaleW,
      backgroundColor: '#FF7870',
    },
    dotInnerBlue: {
      width: 12 * scaleW,
      height: 12 * scaleW,
      borderRadius: 6 * scaleW,
      backgroundColor: '#3C80F3',
    },

    // Pill text
    pillText: {
      fontSize: 16 * scaleW,
      fontWeight: '500',
    },

    // PITCH (red) states
    pillActiveRed: { backgroundColor: '#FF7870', borderColor: 'transparent' },
    pillInactiveRed: { backgroundColor: '#fff', borderColor: '#FF7870' },
    circleActiveRed: { backgroundColor: '#fff', borderColor: '#fff' },
    circleInactiveRed: { backgroundColor: 'transparent', borderColor: '#FF7870' },
    pillTextActiveRed: { color: '#fff' },
    pillTextInactiveRed: { color: '#FF7870' },

    // VOLUME (blue) states
    pillActiveBlue: { backgroundColor: '#3C80F3', borderColor: 'transparent' },
    pillInactiveBlue: { backgroundColor: '#fff', borderColor: '#3C80F3' },
    circleActiveBlue: { backgroundColor: '#fff', borderColor: '#fff' },
    circleInactiveBlue: { backgroundColor: 'transparent', borderColor: '#3C80F3' },
    pillTextActiveBlue: { color: '#fff' },
    pillTextInactiveBlue: { color: '#3C80F3' },

    // Value under pill
    valueUnderPill: {
      marginTop: 4 * scaleH,
      fontSize: 15 * scaleW,
      fontWeight: '800',
    },
  });
};