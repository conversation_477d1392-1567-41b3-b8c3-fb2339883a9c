import {Search, X} from 'lucide-react-native';
import React from 'react';
import {View} from 'react-native';
import {Appbar} from 'react-native-paper';
import {SearchButtonStyle} from '../styles/SearchButtonStyle';

type SearchButtonProps = {
  isSearching: boolean;
  setIsSearching: (value: boolean) => void;
};

export const SearchButton: React.FC<SearchButtonProps> = ({
  isSearching,
  setIsSearching,
}) => {
  const style = SearchButtonStyle();
  return (
    <View>
      <Appbar.Action
        icon={
          !isSearching
            ? () => <Search color="white" />
            : () => <X color="white" />
        }
        color="white"
        onPress={() => setIsSearching(!isSearching)}
      />
    </View>
  );
};
