/* eslint-disable react-native/no-inline-styles */
import {Bookmark, Mic, MicOff} from 'lucide-react-native';
import {StyleSheet} from 'nativewind';
import React, {Dispatch, SetStateAction} from 'react';
import {View} from 'react-native';
import {Card, IconButton, TextInput, useTheme} from 'react-native-paper';
import SQLite from 'react-native-sqlite-storage';
import SpeakButton from '../../../components/SpeakButton/SpeakButton';
import {useVoiceInputModal} from '../hooks/useVoiceInputModal';
import {VoiceInputModalStyle} from '../styles/VoiceInputModalStyle';

// Enable debugging
SQLite.DEBUG(false);
SQLite.enablePromise(true);

interface VoiceInputModalProps {
  setIsListening?: Dispatch<SetStateAction<boolean>>;
  isListening?: boolean;
  setShowModal: Dispatch<SetStateAction<boolean>>;
}

const VoiceInputModal: React.FC<VoiceInputModalProps> = ({
  setIsListening,
  isListening,
  setShowModal,
}) => {
  const {
    isSpeaking,
    setIsSpeaking,
    message,
    setMessage,
    isFocused,
    _startRecognizing,
    _stopRecognizing,
    handleSaveSentence,
    theme,
  } = useVoiceInputModal(setShowModal, setIsListening, isListening);

  const styles = VoiceInputModalStyle();

  return (
    <Card contentStyle={styles.container}>
      <TextInput
        style={{
          textAlignVertical: 'top',
          fontSize: 14,
          width: '100%',
          height: 80,
        }}
        multiline={true}
        dense={true}
        label="Speak here..."
        value={message}
        mode="outlined"
        autoFocus={isFocused}
        onChangeText={input => setMessage(input)}
        outlineColor={theme.colors.primary}
        onFocus={_stopRecognizing}
      />

      <Card.Actions style={styles.buttonContainer}>
        <IconButton
          onPress={isListening ? _stopRecognizing : _startRecognizing}
          style={styles.button}
          mode="outlined"
          size={18}
          icon={() =>
            isListening ? (
              <MicOff size={24} color="#FF7870" />
            ) : (
              <Mic size={24} color={theme.colors.primary} />
            )
          }
        />

        <View style={styles.separator} />

        <View>
          <SpeakButton
            text={message}
            isSpeaking={isSpeaking}
            setIsSpeaking={setIsSpeaking}
            onPress={_stopRecognizing}
            // languageCode={language.code}
          />
        </View>

        <View style={styles.separator} />

        <IconButton
          onPress={handleSaveSentence}
          style={styles.button}
          mode="outlined"
          size={18}
          icon={() => <Bookmark size={24} color={theme.colors.primary} />}
        />
      </Card.Actions>
    </Card>
  );
};

export default VoiceInputModal;
