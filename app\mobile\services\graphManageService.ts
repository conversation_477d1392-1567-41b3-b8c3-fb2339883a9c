import {Dispatch, SetStateAction} from 'react';
import {SQLiteDatabase, openDatabase} from 'react-native-sqlite-storage';
import {Lesson} from './lessonManageService';

export type Graph = {
  id: number;
  date: Date;
  complete: LessonCount[];
};

export type LessonCount = {
  id: number;
  name: string;
  count: number;
};

export const openGraphDatabase = async () => {
  return openDatabase({
    name: 'Graph.db',
    location: 'default',
  });
};

export const createGraphTable = async (db: SQLiteDatabase) => {
  try {
    await db.executeSql(
      'CREATE TABLE IF NOT EXISTS Graph (id INTEGER PRIMARY KEY AUTOINCREMENT, date DATE, complete TEXT);',
    );
    // console.log('Graph tables created or updated successfully');
  } catch (error) {
    console.error('Error creating or updating graph tables:', error);
  }
};

export const openGraphTemp = async () => {
  return openDatabase({
    name: 'GraphTemp.db',
    location: 'default',
  });
};

export const createGraphTemp = async (db: SQLiteDatabase) => {
  try {
    await db.executeSql(
      'CREATE TABLE IF NOT EXISTS GraphTemp (id INTEGER PRIMARY KEY AUTOINCREMENT, date DATEx);',
    );
    // console.log('Graph Temp tables created or updated successfully');
  } catch (error) {
    console.error('Error creating or updating graph temp tables:', error);
  }
};

export const insertGraphTemp = async (db: SQLiteDatabase, date: Date[]) => {
  try {
    for (let i = 0; i < date.length; i++) {
      // const formattedDate = date[i].toISOString().split('T')[0];
      const formattedDate = formatDate(date[i]);
      await db.executeSql('INSERT INTO GraphTemp (date) VALUES (?);', [
        formattedDate,
      ]);
    }
    // console.log('Graph Temp inserted successfully', date);
  } catch (error) {
    console.error('Error inserting Graph Temp:', error);
  }
};

export const insertGraph = async (
  db: SQLiteDatabase,
  date: Date,
  complete: LessonCount[],
  setLessonCount?: Dispatch<SetStateAction<LessonCount[]>>,
) => {
  try {
    // const formattedDate = date.toISOString().split('T')[0];
    const formattedDate = formatDate(date);
    const contentJSON = JSON.stringify(complete);
    const [existingDate] = await db.executeSql(
      'SELECT * FROM Graph WHERE date = ?;',
      [formattedDate],
    );
    if (existingDate.rows.length > 0) {
      // console.log('Date with already exists in the database');

      return;
    }

    await db.executeSql('INSERT INTO Graph (date, complete) VALUES (?, ?);', [
      formattedDate,
      contentJSON,
    ]);
    if (setLessonCount) {
      setLessonCount(complete);
    }

    // console.log('Graph inserted successfully', date);
  } catch (error) {
    console.error('Error inserting Graph:', error);
  }
};

const defaultComplete: LessonCount[] = [{id: 0, name: '', count: 0}];

export const fetchGraph_3m = async (
  db: SQLiteDatabase,
  setGraph: Dispatch<SetStateAction<Graph[]>>,
) => {
  try {
    // Calculate the date 3 months ago
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

    // console.log('3 months ago:', threeMonthsAgo);

    const today = new Date();
    // const formattedToday = today.toISOString().split('T')[0];
    // const formatted3MonthsAgo = threeMonthsAgo.toISOString().split('T')[0];

    const formattedToday = formatDate(today);
    const formatted3MonthsAgo = formatDate(threeMonthsAgo);

    const jsonDefaultComplete = JSON.stringify(defaultComplete);

    // Create a temporary table to store dates
    await db.executeSql('CREATE TEMPORARY TABLE DateRange (date TEXT);');

    // Insert dates into the temporary table
    for (
      let d = new Date(threeMonthsAgo);
      d <= today;
      d.setDate(d.getDate() + 1)
    ) {
      await db.executeSql(
        'INSERT INTO DateRange (date) VALUES (?);',
        // [formatDate(d.toISOString().split('T')[0])]);
        [formatDate(d)],
      );
    }

    // Perform a left join with the Graph table
    const results = await db.executeSql(
      `
      SELECT DateRange.date AS date, Graph.complete
      FROM DateRange
      LEFT JOIN Graph ON DateRange.date = Graph.date
      WHERE DateRange.date BETWEEN ? AND ?;
    `,
      [formatted3MonthsAgo, formattedToday],
    );

    if (results[0].rows.length > 0) {
      const rows = results[0].rows;
      const graphs: Graph[] = [];
      for (let i = 0; i < rows.length; i++) {
        let item = rows.item(i);
        try {
          item.complete = item.complete
            ? JSON.parse(item.complete)
            : JSON.parse(jsonDefaultComplete);
          item.date = new Date(item.date);
        } catch (error) {
          console.error('Error parsing complete field:', error);
          item.complete = [defaultComplete];
        }
        graphs.push(item);
      }
      // console.log('fetch 3m:', graphs);
      setGraph(graphs);
    }
  } catch (error) {
    console.error('Error fetching graphs:', error);
  }
};

export const closeDatabase = async (db: SQLiteDatabase) => {
  try {
    await db.close();
  } catch (error) {
    console.error('Error closing database:', error);
  }
};

export const updateGraph = async (
  db: SQLiteDatabase,
  date: Date,
  lesson: LessonCount[],
  setLessonCount: Dispatch<SetStateAction<LessonCount[]>>,
) => {
  try {
    // const formattedDate = date.toISOString().split('T')[0];
    const formattedDate = formatDate(date);
    await db.executeSql('UPDATE Graph SET complete = ? WHERE date = ?', [
      JSON.stringify(lesson),
      formattedDate,
    ]);
    setLessonCount(lesson);
    // console.log('Graph updated successfully', date);
  } catch (error) {
    console.error('Error updating graph:', error);
  }
};

export const fetchLessonCount = async (
  db: SQLiteDatabase,
  setLessonCount: Dispatch<SetStateAction<LessonCount[]>>,
) => {
  try {
    const date = new Date();
    // const timezoneOffset = -(date.getTimezoneOffset() / 60); // Hours offset from UTC
    // const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // console.log(`Your local timezone is: ${timezone} (UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset} hours)`);
    // const formattedDate = date.toISOString().split('T')[0];
    const formattedDate = formatDate(date);
    // console.log("date",date.toLocaleString())

    const [existingGraph] = await db.executeSql(
      'SELECT * FROM Graph WHERE date = ?;',
      [formattedDate],
    );

    if (existingGraph.rows.length > 0) {
      // console.log('Graph with the same date already exists in the database');
      let graphData = existingGraph.rows.item(0);
      graphData.complete = JSON.parse(graphData.complete);

      // Update the graph in the database with the modified complete data
      updateGraph(db, date, graphData.complete, setLessonCount);
      // console.log('count', graphData.date);
    }
  } catch (error) {
    console.error('Error fetching lesson count:', error);
  }
};

export const countLesson = async (
  lesson: Lesson,
  setLessonCount: Dispatch<SetStateAction<LessonCount[]>>,
) => {
  try {
    const date = new Date();
    // const formattedDate = date.toISOString().split('T')[0];
    const formattedDate = formatDate(date);
    // console.log('date', formattedDate);
    const db = await openGraphDatabase();

    const [existingGraph] = await db.executeSql(
      'SELECT * FROM Graph WHERE date = ?;',
      [formattedDate],
    );

    if (existingGraph.rows.length > 0) {
      // console.log('Graph with the same date already exists in the database');
      let graphData = existingGraph.rows.item(0);
      graphData.complete = JSON.parse(graphData.complete);

      // Find the lesson in the existing graph data
      let lessonIndex = graphData.complete.findIndex(
        (l: LessonCount) => l.id === lesson.id,
      );

      if (lessonIndex !== -1) {
        // If the lesson exists, increment its count
        graphData.complete[lessonIndex].count += 1;
      } else {
        // If the lesson does not exist, add it to the graph data with count 1
        const newLesson = {id: lesson.id, name: lesson.name, count: 1};
        graphData.complete.push(newLesson);
      }

      // Update the graph in the database with the modified complete data
      updateGraph(db, date, graphData.complete, setLessonCount);
      // console.log('count', graphData.date);
    } else {
      // If no graph exists for the date, create a new lesson count with count 1
      const newLessonCount = [{...lesson, count: 1}];
      insertGraph(db, date, newLessonCount, setLessonCount);
    }
  } catch (error) {
    console.error('Error counting lesson:', error);
  }
};

// export const formatDate = (dateString: string) => {
//   if (!dateString) {
//     return '';
//   }
//   const date = new Date(dateString);
//   if (isNaN(date.getTime())) {
//     console.error('Invalid date:', dateString);
//     return dateString; // Return original if invalid date
//   }

//   return date.toISOString().split('T')[0]; // Format as 'YYYY-MM-DD'
// };

export const formatDate = (date: Date) => {
  // Get the year, month, and day in the local timezone
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed, so add 1
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`; // Return in 'YYYY-MM-DD' format
};
