services:
  redis:
    image: "redis:alpine"

  server:
    build: ./app/backend
    volumes:
      - ./app/backend:/src
      - ./app/backend/uploads:/uploads
      - ./app/backend/src/static:/app/static
      - ./app/backend/src/outputs:/outputs
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - postgres
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --log-config log_conf.yaml

  celery_worker:
    build: ./app/backend
    command: celery -A app.extensions worker --loglevel=info --concurrency=1
    volumes:
      - ./app/backend/src:/app
      - ./app/backend/uploads:/uploads
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0

  monitor:
    image: mher/flower
    command: celery flower --broker=redis://redis:6379/0 --port=5555 --basic_auth=oscarzzzz:byebyebye
    ports:
      - "5555:5555"
    environment:
      CELERY_BROKER_URL: redis://redis:6379/0
      CELERY_RESULT_BACKEND: redis://redis:6379/0
    depends_on:
      - redis
      - celery_worker

  nginx:
    build:
      context: ./app/nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./app/nginx/${NGINX_CONFIG_FILE}:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - ./app/backend/src/static:/app/backend/src/static
    depends_on:
      - server

  postgres:
    image: postgres:latest
    restart: always
    environment:
      POSTGRES_USER: "${POSTGRES_USER}"
      POSTGRES_PASSWORD: "${POSTGRES_PASSWORD}"
      POSTGRES_DB: "${POSTGRES_DB}"
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  test:
    build: ./app/backend
    depends_on:
      - server
    entrypoint: >
      sh -c "python -u tests/run_test.py"
    volumes:
      - ./app/backend/src/outputs:/outputs

volumes:
  portal_data:
  pgdata:
