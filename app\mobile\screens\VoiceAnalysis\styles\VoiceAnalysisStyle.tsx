import { StyleSheet } from 'nativewind';
import { useTheme } from 'react-native-paper';

const useVoiceAnalysisStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    mainContainer: {
      flex: 1,
      backgroundColor: '#fff',
    },
    header: {
      backgroundColor: theme.colors.primary,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 14,
      paddingHorizontal: 4,
      elevation: 4,
    },
    headerTitle: {
      color: 'white',
      fontSize: 20,
      fontWeight: 'bold',
    },
    contentContainer: {
      flex: 1,
      padding: 0,
      paddingTop: 0,
    },
    gramContainer: {
      marginVertical: 0,
      marginBottom: 0,
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      padding: 0,
    },
    image: {
      width: '100%',
      height: 'auto',
      aspectRatio: 2.5,
      resizeMode: 'contain',
      margin: 0,
      padding: 0,
      backgroundColor: 'transparent',
    },
    imageTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: 5,
      color: '#333',
    },
    visualizationImage: {
      width: '100%',
      height: 'auto',
      aspectRatio: 3.5,
      resizeMode: 'contain',
      margin: 0,
      padding: 0,
      backgroundColor: 'transparent',
    },
  });
};

export default useVoiceAnalysisStyle;
