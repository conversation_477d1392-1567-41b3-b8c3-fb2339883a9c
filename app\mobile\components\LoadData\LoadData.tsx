import {useEffect} from 'react';
import {useSorting} from '../SortingContextProvider/SortingContextProvider';
import {initialData} from '../../utils/databaseSetup';
import AuthService from '../../services/authService';
import {initTts, setSpeakRate} from '../../services/ttsService';
import {
  updatePhraseTable,
  fetchPhrases,
  openPhrasesDatabase,
} from '../../services/phraseManageService';

const LoadData = ({children}: {children: React.ReactNode}) => {
  const {setToken, setPhrases, sortOption, playbackSpeed} = useSorting();
  useEffect(() => {
    initTts();
    setSpeakRate(playbackSpeed);
    (async () => {
      await initialData();
      await AuthService().getAccessToken(setToken);

      const p_db = await openPhrasesDatabase();
      await updatePhraseTable(p_db);
      await fetchPhrases(p_db, setPhrases, sortOption);
      console.log(p_db);
      console.log('phrases fetched');
    })();
  }, []);

  useEffect(() => {
    playbackSpeed && setSpeakRate(playbackSpeed);
  }, [playbackSpeed]);

  return <>{children}</>;
};

export default LoadData;
