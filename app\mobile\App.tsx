import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { NavigationContainer } from '@react-navigation/native';
import React, { useEffect } from 'react';
import { LogBox } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { MD3LightTheme, PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import SplashScreen from 'react-native-splash-screen';
import BottomNav from './components/BottomNav/BottomNav';
import { SortingContextProvider } from './components/SortingContextProvider/SortingContextProvider';
import LoadData from './components/LoadData/LoadData';

LogBox.ignoreAllLogs();

const theme = {
    ...MD3LightTheme,
    colors: {
        ...MD3LightTheme.colors,

        primary: '#3C80F3',
        secondary: '115C38',
        accent: '#397954',
        background: '#ffffff',
        textColor: '#000',
        secondaryContainer: '#397954',
        onSecondaryContainer: 'white',
        surfaceDisabled: '#D9D9D9',
    },
    fonts: {
        ...MD3LightTheme.fonts,
        fontFamily: 'Inter',
        fontSize: 16,
    },
    roundness: 4, // border-radius
};

export default function App() {

    useEffect(() => {
        SplashScreen.hide();

        // Orientation.lockToPortrait();

        // return () => {
        //   Orientation.unlockAllOrientations;
        // };
    }, []);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <SortingContextProvider>
                <LoadData>
                    <SafeAreaProvider>
                        <PaperProvider theme={theme}>
                            <BottomSheetModalProvider>
                                <NavigationContainer>
                                    <BottomNav />
                                </NavigationContainer>
                            </BottomSheetModalProvider>
                        </PaperProvider>
                    </SafeAreaProvider>
                </LoadData>
            </SortingContextProvider>
        </GestureHandlerRootView>
    );
}
