from fastapi import APIRouter, Query
from fastapi.responses import FileResponse
from app.config import Configs
import os

router = APIRouter(tags=["Uploads"])
config = Configs()

@router.get("/health")
async def uploads_health_check():
    return {"status": "ok", "message": "Uploads route is reachable"}

@router.get("")
async def uploaded_file(filename: str = Query(..., description="Name of the uploaded file")):
    file_path = os.path.join(config.UPLOAD_FOLDER_PATH, filename)
    if os.path.exists(file_path):
        return FileResponse(path=file_path)
    return {"error": "File not found"}

