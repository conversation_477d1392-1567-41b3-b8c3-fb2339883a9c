import {
  Pencil,
  Plus,
  Save,
  Minus,
  ChevronLeft,
  ChevronRight,
  Calendar<PERSON>heck,
  Music2,
  RotateCw,
} from 'lucide-react-native';
import React from 'react';
import {
  FlatList,
  Image,
  ListRenderItem,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import {
  Button,
  IconButton,
  Modal,
  Portal,
  Text,
  TextInput,
} from 'react-native-paper';
import BottomSheetWrapper from '../../components/BottomSheetComponent/BottomSheetWrapper';
import {TopNav} from '../../components/TopNav/TopNav';
import {useExerciseSheet} from './hooks/useExerciseSheet';
import ExerciseCard from './components/ExerciseCard';
import {ExerciseSheetStyle} from './styles/ExerciseSheetStyle';

const ExerciseSheet = () => {
  const {
    currentLesson,
    navigation,
    editModal,
    theme,
    decrementCounter,
    counter,
    incrementCounter,
    exercises,
    visible,
    hideModal,
    value,
    newName,
    setNewName,
    handleEdit,
    currentExercise,
    handleDelete,
    handleDeleteNoConfirm,
    showBottomSheet,
    setIsEditing,
    setInputFieldNumber,
    isEditing,
    bottomSheetModalRef,
    editName,
    setEditName,
    inputFieldNumber,
    handleContentChange,
    handleCancelContent,
    handleUpdate,
    handleAddExercise,
    addContentField,
    confirmDeleteEx,
    editExercise,
    selectedExercise,
  } = useExerciseSheet();

  const styles = ExerciseSheetStyle();

  return (
    <View style={styles.container}>
      <TopNav
        title={currentLesson.name}
        backFunction={() => navigation.navigate('LessonPage')}>
        <IconButton
          style={{borderWidth: 0}}
          icon={() => <Pencil size={24} color="#FFFFFF" />}
          mode="outlined"
          size={18}
          onPress={editModal}
        />
      </TopNav>

      <View style={styles.optionItem}>
        <View style={styles.optionItemLabel}>
          <Image
            source={require('../../assets/exerciseIcons/melody.png')}
            style={{width: 30, height: 30}}
          />
          <Text style={styles.counterText}>Melody Pattern</Text>
        </View>

        <View style={styles.dropDownSelect}>
          <IconButton
            mode="contained-tonal"
            icon={() => <RotateCw size={25} color="#E3E3E3" strokeWidth={3} />}
            size={30}
            containerColor={theme.colors.background}
          />

          <Button style={styles.dropDownButon}>
            <Text style={styles.dropDownButonText}>Rising Pattern</Text>
          </Button>
        </View>
      </View>

      <View style={styles.optionItem}>
        <View style={styles.optionItemLabel}>
          <Image
            source={require('../../assets/exerciseIcons/speakSample.png')}
            style={{width: 30, height: 30}}
          />
          <Text style={styles.counterText}>Voice Sample</Text>
        </View>

        <View style={styles.dropDownSelect}>
          <IconButton
            mode="contained-tonal"
            icon={() => <RotateCw size={25} color="#E3E3E3" strokeWidth={3} />}
            size={30}
            containerColor={theme.colors.background}
          />
          <Button style={styles.dropDownButon}>
            <Text style={styles.dropDownButonText}>Rising Pattern</Text>
          </Button>
        </View>
      </View>

      <View style={styles.optionItem}>
        <View style={styles.optionItemLabel}>
          <CalendarCheck size={30} color="#E3E3E3" />
          <Text style={styles.counterText}>Target Exercises Per Day:</Text>
        </View>

        <View style={styles.counterCount}>
          <IconButton
            mode="contained-tonal"
            icon={() => <ChevronLeft size={30} color={theme.colors.primary} />}
            size={30}
            containerColor={theme.colors.background}
            onPress={decrementCounter}
          />
          <View
            style={{
              borderWidth: 1.5,
              borderColor: theme.colors.primary,
              borderRadius: 50,
              paddingVertical: 3,
              paddingHorizontal: 5,
            }}>
            <Text style={styles.counterNumber}>{counter}</Text>
          </View>

          <IconButton
            mode="contained-tonal"
            icon={() => <ChevronRight size={30} color={theme.colors.primary} />}
            size={30}
            containerColor={theme.colors.background}
            onPress={incrementCounter}
          />
        </View>
      </View>

      <ScrollView contentContainerStyle={styles.bottomSheet}>
        {exercises.map(exercise => (
          <ExerciseCard
            key={exercise.id}
            text={exercise.name}
            content={exercise.content}
            buttonOne={() => handleDeleteNoConfirm(exercise)}
            buttonTwo={() => editExercise(exercise)}
            buttonThree={() => selectedExercise(exercise)}
          />
        ))}
        <View style={styles.sectionDivider} />
        <View style={styles.addButton}>
          <Button
            onPress={() => {
              showBottomSheet();
              setIsEditing(false);
              setInputFieldNumber(['']);
            }}>
            <Text
              style={{
                fontWeight: 'bold',
                fontSize: 16,
                color: theme.colors.primary,
              }}>
              Add More +
            </Text>
          </Button>
        </View>
      </ScrollView>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={hideModal}
          contentContainerStyle={styles.modalContainer}>
          {value == 'edit' ? (
            <>
              <Text style={styles.modalTitle}>
                Edit "{currentLesson.name}"?
              </Text>
              <TextInput
                label={currentLesson.name}
                value={newName}
                onChangeText={text => setNewName(text)}
                style={styles.input}
                mode="outlined"
              />
              <View style={styles.buttonContainer}>
                <Button
                  mode="contained"
                  onPress={() => handleEdit()}
                  contentStyle={styles.deleteButton}>
                  <Text style={[styles.textStyle, {color: 'white'}]}>
                    Confirm
                  </Text>
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => hideModal()}
                  contentStyle={styles.deleteButton}>
                  <Text style={[styles.textStyle, {color: '#4c4c4c'}]}>
                    Cancel
                  </Text>
                </Button>
              </View>
            </>
          ) : (
            <>
              <Text style={styles.modalTitle}>
                Delete "{currentExercise.name}"?
              </Text>
              <Text style={styles.modalMessage}>
                This action cannot be undone.
              </Text>
              <View style={styles.buttonContainer}>
                <Button
                  mode="contained"
                  onPress={() => handleDelete()}
                  contentStyle={styles.deleteButton}>
                  <Text style={[styles.textStyle, {color: 'white'}]}>
                    Confirm
                  </Text>
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => hideModal()}
                  contentStyle={styles.deleteButton}>
                  <Text style={[styles.textStyle, {color: '#4c4c4c'}]}>
                    Cancel
                  </Text>
                </Button>
              </View>
            </>
          )}
        </Modal>
      </Portal>

      <BottomSheetWrapper
        title={isEditing ? 'Edit Exercise' : 'Add New Exercise'}
        bottomSheetModalRef={bottomSheetModalRef}>
        <View style={{height: '100%', paddingBottom: 120}}>
          <ScrollView contentContainerStyle={styles.bottomSheet}>
            <TextInput
              label="Name"
              value={isEditing ? editName : newName}
              onChangeText={text =>
                isEditing ? setEditName(text) : setNewName(text)
              }
              style={styles.input}
              mode="outlined"
            />
            {inputFieldNumber.map((item, index) => (
              <TextInput
                key={index}
                label={`Content ${index + 1}`}
                value={inputFieldNumber[index]}
                onChangeText={text => handleContentChange(text, index)}
                style={styles.input}
                mode="outlined"
                right={
                  <TextInput.Icon
                    icon="close"
                    style={{marginTop: 19}}
                    forceTextInputFocus={false}
                    onPress={() => handleCancelContent(index)}
                  />
                }
              />
            ))}
          </ScrollView>

          <View
            style={[
              styles.buttonSheetContainer,
              {backgroundColor: theme.colors.primary},
            ]}>
            <IconButton
              mode="outlined"
              style={styles.button}
              size={36}
              onPress={() => (isEditing ? handleUpdate() : handleAddExercise())}
              icon={() => <Save size={30} color={'white'} />}
            />
            <IconButton
              mode="outlined"
              onPress={addContentField}
              size={36}
              style={styles.button}
              icon={() => <Plus size={30} color={'white'} />}
            />
          </View>
        </View>
      </BottomSheetWrapper>
    </View>
  );
};

export default ExerciseSheet;
