import React, {Dispatch, SetStateAction, useEffect, useState} from 'react';
import {
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import countryFlags, {
  LanguageItem,
  languageNameLocal,
  languagesTemplate,
} from '../../utils/language';
import {Modal, Portal} from 'react-native-paper';

type LanguageSelectorProps = {
  languageCode: string;
  handleChangeLanguage?: (lanuageCode: string) => Promise<void>;
  handleChangeLanguageGlobal?: (language: LanguageItem) => void;
  showLanguage: boolean;
  setShowLanguage: Dispatch<SetStateAction<boolean>>;
};

const {iconImages} = countryFlags();

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  languageCode,
  handleChangeLanguageGlobal,
  handleChangeLanguage,
  showLanguage,
  setShowLanguage,
}) => {
  const [languages, setLanguages] = useState<LanguageItem[]>(languagesTemplate);

  useEffect(() => {
    if (languageCode) {
      setLanguages(
        languages.map(item => ({
          ...item,
          isSelected: item.code === languageCode,
        })),
      );
    }
  }, [languageCode]);

  const styles = StyleSheet.create({
    languageBackground: {
      width: '100%',
      height: '100%',
      position: 'absolute',
      zIndex: 10,
      elevation: 10,
    },
    languageContainer: {
      position: 'absolute',
      left: 0,
      right: 0,
      zIndex: 10,
      elevation: 10,
      marginHorizontal: 50,
      backgroundColor: 'white',
      paddingVertical: 20,
      borderRadius: 25,
      shadowColor: 'transparent',
      shadowOffset: {width: 0, height: 0},
      shadowOpacity: 0,
      shadowRadius: 0,
    },
    languageSelector: {
      paddingHorizontal: 8,
    },
    languageButtonSelected: {
      color: 'white',
      backgroundColor: '#007bff', // Blue background for selected item
    },

    languageButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 10,
      margin: 8,
      //   backgroundColor: '#f0f0f0',
      borderRadius: 25,
      width: '100%',
      gap: 4,
      justifyContent: 'flex-end',
    },
    languageIcon: {
      width: 28,
      height: 28,
    },
    languageTextFocused: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#FEFFFF', // Default text color
    },
    languageText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#000000',
    },
  });

  return (
    <Portal>
      <Modal
        visible={showLanguage}
        onDismiss={() => {
          setShowLanguage(false);
        }}>
        <View
          style={styles.languageContainer}
          onStartShouldSetResponder={() => true}>
          <FlatList
            data={languages}
            numColumns={2}
            keyExtractor={item => item.code}
            contentContainerStyle={styles.languageSelector}
            renderItem={({item}) => (
              <TouchableOpacity
                style={[
                  styles.languageButton,
                  item.isSelected && styles.languageButtonSelected,
                ]}
                onPress={() => {
                  handleChangeLanguage && handleChangeLanguage(item.code);
                  handleChangeLanguageGlobal &&
                    handleChangeLanguageGlobal(item);
                  setShowLanguage(false);
                }}>
                <Text
                  style={
                    item.isSelected
                      ? styles.languageTextFocused
                      : styles.languageText
                  }>
                  {
                    languageNameLocal[
                      item.name as keyof typeof languageNameLocal
                    ]
                  }
                </Text>
                <Image
                  source={iconImages[item.code as keyof typeof iconImages]}
                  style={styles.languageIcon}
                />
              </TouchableOpacity>
            )}
          />
        </View>
      </Modal>
    </Portal>
  );
};

export default LanguageSelector;
