import {createMaterialBottomTabNavigator} from '@react-navigation/material-bottom-tabs';
import {StyleSheet} from 'nativewind';
import * as React from 'react';
import {useTheme} from 'react-native-paper';
import PitchPaceDetectScreen from '../../screens/PitchPaceDetectScreen/PitchPaceDetectScreen';
import VoiceAmplification from '../../screens/VoiceAmplificationScreen/VoiceAmplification';
import {LessonNavigator} from '../../screens/Navigation/LessonStack';
import {VoiceEvaluationNavigator} from '../../screens/Navigation/VoiceEvaluationStack';
import {Image, View} from 'react-native';
import SettingsScreen from '../../screens/Settings/SettingsScreen';

const Tab = createMaterialBottomTabNavigator();

const BottomNav = () => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    activeIndicatorStyle: {
      height: 50,
      width: 50,
      borderRadius: 25,
      backgroundColor: 'white',
      marginBottom: 5,
    },

    iconContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      paddingBottom: 18,
    },

    iconStyle: {
      width: 25,
      height: 25,
      resizeMode: 'contain',
    },

    barStyle: {
      height: 70,
      width: '95%',
      borderRadius: 40,
      overflow: 'hidden',
      alignSelf: 'center',
      // justifyContent: 'center',
      backgroundColor: theme.colors.primary,
      // Remove bottom offset if it's causing icon misalignment
      // position: 'relative',
      // bottom: 20
    },

    navBackground: {
      width: '100%',

      position: 'absolute',
      height: '100%',
      backgroundColor: theme.colors.background,
      bottom: 0,
      paddingBottom: 20,
    },
  });

  return (
    <View style={styles.navBackground}>
      <Tab.Navigator
        screenOptions={{}}
        barStyle={styles.barStyle}
        initialRouteName="Voice"
        shifting={true}
        activeColor="#4FC3C7"
        inactiveColor="#FEFFFF"
        activeIndicatorStyle={styles.activeIndicatorStyle}
        labeled={false}
        sceneAnimationEnabled={false}>
        <Tab.Screen
          name="Voice"
          component={VoiceAmplification}
          options={{
            tabBarIcon: ({color}) => (
              <View style={styles.iconContainer}>
                <Image
                  source={require('../../assets/navIcons/voiceAmp.png')}
                  style={[styles.iconStyle, {tintColor: color}]}
                />
              </View>
            ),
          }}
        />
        <Tab.Screen
          name="Lessons"
          component={LessonNavigator}
          options={{
            tabBarIcon: ({color}) => (
              <View style={styles.iconContainer}>
                <Image
                  source={require('../../assets/navIcons/lessons.png')}
                  style={[styles.iconStyle, {tintColor: color}]}
                />
              </View>
            ),
          }}
        />
        <Tab.Screen
          name="PitchPaceDetect"
          component={PitchPaceDetectScreen}
          options={{
            tabBarIcon: ({color}) => (
              <View style={styles.iconContainer}>
                <Image
                  source={require('../../assets/navIcons/voiceDetect.png')}
                  style={[styles.iconStyle, {tintColor: color}]}
                />
              </View>
            ),
          }}
        />
        <Tab.Screen
          name="Voice Evaluation"
          component={VoiceEvaluationNavigator}
          options={{
            tabBarIcon: ({color}) => (
              <View style={styles.iconContainer}>
                <Image
                  source={require('../../assets/navIcons/voiceAnalysis.png')}
                  style={[styles.iconStyle, {tintColor: color}]}
                />
              </View>
            ),
          }}
        />

        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            tabBarIcon: ({color}) => (
              <View style={styles.iconContainer}>
                <Image
                  source={require('../../assets/navIcons/settings.png')}
                  style={[styles.iconStyle, {tintColor: color}]}
                />
              </View>
            ),
          }}
        />
      </Tab.Navigator>
    </View>
  );
};

export default BottomNav;
