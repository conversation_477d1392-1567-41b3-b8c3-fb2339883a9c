export type LanguageItem = {
  name: string;
  code: string;
  voiceCode: string;
  isSelected: boolean;
};

export const languagesTemplate = [
  {name: 'English', code: 'en-US', voiceCode: 'en_US', isSelected: false},
  {name: 'Vietnamese', code: 'vi-VN', voiceCode: 'vi-VN', isSelected: false},
  {name: 'Spanish', code: 'es-ES', voiceCode: 'es_CL', isSelected: false},
  {name: 'German', code: 'de-DE', voiceCode: 'de_DE', isSelected: false},
  {name: 'French', code: 'fr-FR', voiceCode: 'fr_FR', isSelected: false},
  {name: 'Italian', code: 'it-IT', voiceCode: 'it_IT', isSelected: false},
  {name: 'Japanese', code: 'ja-JP', voiceCode: 'ja_JP', isSelected: false},
  {name: 'Korean', code: 'ko-KR', voiceCode: 'ko_KR', isSelected: false},
  {name: 'Russian', code: 'ru-RU', voiceCode: 'ru_RU', isSelected: false},
  {
    name: 'Chinese',
    code: 'zh-CN',
    voiceCode: 'zh_Hans_SG',
    isSelected: false,
  },
];

export const languageNameLocal = {
  English: 'English',
  Vietnamese: 'Tiếng Việt',
  Spanish: 'Español',
  German: 'Deutsch',
  French: 'Français',
  Italian: 'Italiano',
  Japanese: '日本語',
  Korean: '한국어',
  Russian: 'Русский',
  Chinese: '中文',
};

const countryFlags = () => {
  const assetPath = '../assets/';

  const images = {
    'en-US': require(assetPath + 'united-kingdom.png'),
    'vi-VN': require(assetPath + 'vietnam.png'),
    'es-ES': require(assetPath + 'spain.png'),
    'da-DK': require(assetPath + 'denmark.png'),
    'de-DE': require(assetPath + 'germany.png'),
    'fr-FR': require(assetPath + 'france.png'),
    'it-IT': require(assetPath + 'italy.png'),
    'ja-JP': require(assetPath + 'japan.png'),
    'ko-KR': require(assetPath + 'south-korea.png'),
    'ru-RU': require(assetPath + 'russia.png'),
    'zh-CN': require(assetPath + 'china.png'),
  };

  const flagIconsPath = '../assets/flagIcons/';

  const iconImages = {
    'en-US': require(flagIconsPath + 'united-kingdom.png'),
    'vi-VN': require(flagIconsPath + 'vietnam.png'),
    'es-ES': require(flagIconsPath + 'spain.png'),
    'da-DK': require(flagIconsPath + 'denmark.png'),
    'de-DE': require(flagIconsPath + 'germany.png'),
    'fr-FR': require(flagIconsPath + 'france.png'),
    'it-IT': require(flagIconsPath + 'italy.png'),
    'ja-JP': require(flagIconsPath + 'japan.png'),
    'ko-KR': require(flagIconsPath + 'south-korea.png'),
    'ru-RU': require(flagIconsPath + 'russia.png'),
    'zh-CN': require(flagIconsPath + 'china.png'),
  };

  return {
    iconImages,
    images,
  };
};

export default countryFlags;
