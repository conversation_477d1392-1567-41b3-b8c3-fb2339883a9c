import {StyleSheet} from 'nativewind';
import React, {ReactNode} from 'react';
import {Appbar, IconButton, useTheme} from 'react-native-paper';
import {ArrowLeft} from 'lucide-react-native';

type TopNavProps = {
  title: string;
  backFunction?: () => void;
  action?: void;
  stopSearching?: () => void;
  isSearching?: boolean;
  searchBar?: React.ReactNode;
  children?: React.ReactNode;
};
export const TopNav: React.FC<TopNavProps> = ({
  title,
  backFunction,
  action,
  stopSearching,
  isSearching,
  searchBar,
  children,
}) => {
  const theme = useTheme();
  const style = StyleSheet.create({
    header: {
      backgroundColor: theme.colors.primary,
      height: 80,
    },
  });

  return (
    <Appbar.Header style={style.header} mode="center-aligned">
      {backFunction ? (
        <IconButton
          icon={() => <ArrowLeft size={24} color="white" />}
          onPress={backFunction}
        />
      ) : null}

      {isSearching && stopSearching ? (
        <IconButton
          icon={() => <ArrowLeft size={24} color="white" />}
          onPress={stopSearching}
        />
      ) : null}

      <Appbar.Content
        title={isSearching ? searchBar : title}
        color="white"
        titleStyle={{fontWeight: 'bold'}}
      />

      {action ? (
        <Appbar.Action icon="dots-vertical" onPress={() => action} />
      ) : null}
      {children || null}
    </Appbar.Header>
  );
};
