import {StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';

export const AnalysisScreenStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginTop: 20,
      backgroundColor: 'transparent',
    },
    notSelected: {
      backgroundColor: 'transparent',
    },
    selectedButton: {
      backgroundColor: theme.colors.primary,
      textDecorationColor: 'white',
    },
    modalContainer: {
      backgroundColor: 'white',
      borderRadius: 20,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5, // for Android
      padding: 20,
      marginHorizontal: 10,
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
    },
    infoView: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      position: 'relative',
      marginTop: 30,
      marginBottom: 20,
    },
    renderTitle: {
      marginVertical: 30,
      gap: 10,
    },
    titleContent: {
      width: 90,
      color: theme.colors.primary,
    },
    cardStyle: {
      width: 130,
      height: 110, // Makes the card square
      marginBottom: 20,
      paddingHorizontal: 0,
    },
    contentContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    titleStyle: {
      fontSize: 12, // Smaller font size for the title
    },
    dataStyle: {
      fontSize: 24,
      fontWeight: 'bold',
      textAlign: 'center',
    },
  });
};
