FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .

RUN apt-get update && apt-get install -y \
    build-essential \
    ninja-build \
    libatlas-base-dev \
    libfftw3-dev \
    libgtk-3-0 \
    libglib2.0-0 \
    libx11-6 \
    libpangocairo-1.0-0 \
    libharfbuzz0b \
    libpango-1.0-0 \
    libfreetype6 \
    libfontconfig1 \
    libxext6 \
    libxcursor1 \
    libxrandr2 \
    libxrender1 \
    libc++1 \
    libc++abi1 \
    libgtk-3-0 \
    libpulse0 \
    libasound2 \
    && rm -rf /var/lib/apt/lists/*

RUN pip install cmake==3.24

COPY requirements.txt .
ENV CMAKE_ARGS="-DCMAKE_POLICY_VERSION_MINIMUM=3.5"
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

COPY tools/praat /usr/local/bin/praat
RUN chmod +x /usr/local/bin/praat

EXPOSE 8000

ENV PYTHONPATH=/app/src