import {selectExercise} from '../../../services/exerciseManageService';
import {
  deleteLesson,
  fetchLesson,
  Lesson,
  openLessonDatabase,
} from '../../../services/lessonManageService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useLessonNavigation} from '../../../utils/useAppNavigation';
import {
  fetchGraph_3m,
  closeDatabase,
  fetchLessonCount,
  openGraphDatabase,
} from '../../../services/graphManageService';
import {useEffect, useState} from 'react';
import React from 'react';
import {useTheme} from 'react-native-paper';

export const useLessonPage = () => {
  const navigation = useLessonNavigation();
  const theme = useTheme();
  const {
    lessons,
    exercises,
    currentLesson,
    lessonCount,
    isPlayAllMode,
    setLessons,
    setExercises,
    setCurrentLesson,
    setCurrentExercise,
    setNextExercise,
    setGraph,
    setLessonCount,
    setPlayAllLesson,
  } = useSorting();

  const [visible, setVisible] = useState(false);
  const [lastSync, setLastSync] = useState<number>(0);
  const [patientId, setPatientId] = useState<string>('P1');

  useEffect(() => {
    (async () => {
      const l_db = await openLessonDatabase();
      const g_db = await openGraphDatabase();
      await fetchLesson(l_db, setLessons);
      await fetchLessonCount(g_db, setLessonCount);
    })();
  }, [exercises]);

  const showModal = () => setVisible(true);
  const hideModal = () => setVisible(false);

  const turnOnPlayAllLesson = () => setPlayAllLesson(true);
  const turnOffPlayAllLesson = () => setPlayAllLesson(false);

  const confirmDeleteLesson = async (lesson: Lesson) => {
    setCurrentLesson(lesson);
    showModal();
  };

  const selectedLesson = async (lesson: Lesson) => {
    try {
      setExercises(lesson.exercises);
      setCurrentLesson(lesson);
      navigation.navigate('ExerciseSheet');
    } catch (error) {
      console.error('Error selecting lesson:', error);
    }
  };

  const selectedExercise = async (lesson: Lesson) => {
    try {
      setCurrentLesson(lesson);
      setExercises(lesson.exercises);
      if (lesson.exercises.length > 0) {
        selectExercise(
          lesson.exercises[0],
          lesson.exercises,
          setCurrentExercise,
          setNextExercise,
        );
        navigation.navigate('ExerciseDetail');
      } else {
        console.error('No exercises found for the given lesson');
      }
    } catch (error) {
      console.error('Error selecting exercise:', error);
    }
  };

  const deleteL = async () => {
    const db = await openLessonDatabase();
    await deleteLesson(currentLesson.id, setLessons);
    hideModal();
  };

  const deleteLessonNoConfirm = async (lesson: Lesson) => {
    setCurrentLesson(lesson);
    // const db = await openLessonDatabase();
    await deleteLesson(lesson.id, setLessons);
    // hideModal();
  };

  const showGraph = async () => {
    const db = await openGraphDatabase();
    await fetchGraph_3m(db, setGraph);
    navigation.navigate('AnalysisScreen');
    await closeDatabase(db);
  };

  const mergedLessons = lessons.map(lesson => {
    const lessonCountObj = lessonCount.find(
      countObj => countObj.id === lesson.id,
    );
    return {
      ...lesson,
      lessonCount: lessonCountObj ? lessonCountObj.count : 0,
    };
  });

  return {
    showGraph,
    visible,
    hideModal,
    currentLesson,
    deleteL,
    mergedLessons,
    confirmDeleteLesson,
    deleteLessonNoConfirm,
    selectedLesson,
    selectedExercise,
    turnOffPlayAllLesson,
    turnOnPlayAllLesson,
    theme,
    exercises,
    setExercises,
    lastSync,
    setLastSync,
    patientId,
  };
};
