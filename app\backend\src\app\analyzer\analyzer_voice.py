import subprocess
import numpy as np
import matplotlib.pyplot as plt
import os
import base64
import librosa
import librosa.display
import datetime
from app.config import Configs

def analyze_voice(cs_file, sv_file, task_id):
    start_time = datetime.datetime.now()
    print(f"[{task_id}] Processing started at: {start_time}")
    
    config = Configs()

    praat_output_dir = config.PRAAT_OUTPUT_DIR
    task_output_dir = os.path.join(config.TASK_OUTPUT_DIR, task_id)

    os.makedirs(praat_output_dir, exist_ok=True)
    os.makedirs(task_output_dir, exist_ok=True)    
    
    praat_bin_path = config.PRAAT_BIN_PATH
    avqi_script_path = config.AVQI_SCRIPT_PATH
    abi_script_path = config.ABI_SCRIPT_PATH
    
    if not os.path.exists(avqi_script_path):
        raise FileNotFoundError(f"AVQI script not found at: {avqi_script_path}")
    if not os.path.exists(abi_script_path):
        raise FileNotFoundError(f"ABI script not found at: {abi_script_path}")

    subprocess.run([praat_bin_path, "--run", avqi_script_path, cs_file, sv_file], check=True)
    subprocess.run([praat_bin_path, "--run", abi_script_path, cs_file, sv_file], check=True)

    y_cs, sr = librosa.load(cs_file, sr=None)
    y_sv, sr2 = librosa.load(sv_file, sr=None)
    if sr != sr2:
        raise ValueError("Sampling rates of CS and SV files do not match")

    y_cs_trimmed, _ = librosa.effects.trim(y_cs, top_db=25)
    y_sv_tail = y_sv[-int(3 * sr):] if len(y_sv) >= int(3 * sr) else y_sv
    y_avqi = np.concatenate((y_cs_trimmed, y_sv_tail))

    waveform_path = os.path.join(task_output_dir, "waveform.png")
    plt.figure(figsize=(12, 3))
    librosa.display.waveshow(y_avqi, sr=sr, color='black')
    plt.title("AVQI Waveform")
    plt.tight_layout()
    plt.savefig(waveform_path)
    plt.close()

    spectrogram_path = os.path.join(task_output_dir, "spectrogram.png")
    D = librosa.amplitude_to_db(np.abs(librosa.stft(y_avqi)), ref=np.max)
    plt.figure(figsize=(12, 3))
    librosa.display.specshow(D, sr=sr, x_axis='time', y_axis='log', cmap='binary')
    plt.colorbar(format='%+2.0f dB')
    plt.title("AVQI Spectrogram")
    plt.tight_layout()
    plt.savefig(spectrogram_path)
    plt.close()

    with open(waveform_path, "rb") as f:
        encoded_waveform = base64.b64encode(f.read()).decode("utf-8")
    with open(spectrogram_path, "rb") as f:
        encoded_spectrogram = base64.b64encode(f.read()).decode("utf-8")

    avqi_path = os.path.join(praat_output_dir, "avqi.txt")
    avqi_results = {}
    with open(avqi_path, "r") as f:
        for line in f:
            if ": " in line:
                key, value = line.strip().split(": ")
                avqi_results[key.strip()] = float(value)

    avqi_value = avqi_results.get("AVQI", 0.0)
    avqi_plot_path = os.path.join(task_output_dir, "avqi_plot.png")
    _plot_score(avqi_value, 2.95, 'AVQI', avqi_plot_path)

    with open(avqi_plot_path, "rb") as f:
        encoded_avqi = base64.b64encode(f.read()).decode("utf-8")

    abi_path = os.path.join(praat_output_dir, "abi.txt")
    abi_results = {}
    with open(abi_path, "r") as f:
        for line in f:
            if ": " in line:
                key, value = line.strip().split(": ")
                abi_results[key.strip()] = float(value)

    abi_value = abi_results.get("ABI", 0.0)
    abi_plot_path = os.path.join(task_output_dir, "abi_plot.png")
    _plot_score(abi_value, 3.44, 'ABI', abi_plot_path)

    with open(abi_plot_path, "rb") as f:
        encoded_abi = base64.b64encode(f.read()).decode("utf-8")

    total_duration = (datetime.datetime.now() - start_time).total_seconds()
    print(f"[{task_id}] Processing completed, duration: {total_duration:.2f} seconds")

    return {
        "avqi": {
            "value": format(avqi_value, ".2f"),
            "cpps": format(avqi_results.get("CPPS", 0.0), ".2f"),
            "hnr": format(avqi_results.get("HNR", 0.0), ".2f"),
            "shimmer_percent": format(avqi_results.get("Shimmer (%)", 0.0), ".2f"),
            "shimmer_db": format(avqi_results.get("Shimmer (dB)", 0.0), ".2f"),
            "slope": format(avqi_results.get("Slope", 0.0), ".2f"),
            "tilt": format(avqi_results.get("Tilt", 0.0), ".2f"),
            "encoded_plot": encoded_avqi,
            "encoded_waveform": encoded_waveform,
            "encoded_spectrogram": encoded_spectrogram
        },
        "abi": {
            "value": format(abi_value, ".2f"),
            "cpps": format(abi_results.get("CPPS", 0.0), ".2f"),
            "jitter": format(abi_results.get("Jitter", 0.0), ".2f"),
            "gne": format(abi_results.get("GNE", 0.0), ".2f"),
            "hfno": format(abi_results.get("HFNO", 0.0), ".2f"),
            "hnrd": format(abi_results.get("HNRD", 0.0), ".2f"),
            "h1h2": format(abi_results.get("H1H2", 0.0), ".2f"),
            "shimmer_db": format(abi_results.get("Shimmer (dB)", 0.0), ".2f"),
            "shimmer_percent": format(abi_results.get("Shimmer (%)", 0.0), ".2f"),
            "period_sd": format(abi_results.get("Period SD", 0.0), ".5f"),
            "encoded_plot": encoded_abi
        }
    }

def _plot_score(score, safe_max, label, save_path):
    plt.figure(figsize=(8, 2.4))
    plt.fill_between([0, 10], 0, 1, color='#e60000', alpha=0.5, label='Max Range (0-10)')
    plt.fill_between([0, safe_max], 0, 1, color='#008000', alpha=0.6, label=f'Safe Range (0-{safe_max})')
    
    for x in range(1, 11):
        plt.axvline(x=x, color='gray', linestyle=':', linewidth=0.8)
        plt.text(x, -0.05, str(x), ha='center', va='top', fontsize=9)
        
    plt.axvline(x=score, color='blue', linestyle='--', label=f'{label} = {score:.2f}')
    plt.xlim(0, 10)
    plt.ylim(0, 1)
    plt.title(f"{label} Visualization")
    plt.legend(loc='upper right')
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()
