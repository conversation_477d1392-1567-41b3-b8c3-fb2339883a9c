import time
import os

from typing import Annotated
from fastapi import APIRouter, UploadFile, File, Header
from fastapi.responses import JSONResponse

from app.routes.auth import validate_token
from app.utils.file import allowed_file
from app.tasks.analyze_voice import analyze_voice_task
from app.config import Configs

router = APIRouter(tags=["Analyze"])
config = Configs()

@router.get("/health")
async def analyze_health_check():
    return {"status": "ok", "message": "Analyze route is reachable"}

@router.post("")
async def analyze(
    files: Annotated[list[UploadFile], File(..., description="multiple audio files (cs/sv pairs)")],
    authorization: Annotated[str | None, Header()] = None
):
    if not authorization:
        return JSONResponse(content={'error': 'Unauthorized'}, status_code=401)

    token = authorization.split()[1]
    token_data = validate_token(token)

    if not token_data:
        return JSONResponse(content={'error': 'Invalid token'}, status_code=405)
    if time.time() > token_data['expires_at']:
        return JSONResponse(content={'error': 'Token expired'}, status_code=405)

    if not files:
        return JSONResponse(content={'status': 'failed', 'code': 400, 'message': 'No files part in the request'}, status_code=400)

    if len(files) % 2 != 0:
        return JSONResponse(content={'status': 'failed', 'code': 400, 'message': 'Mismatched number of cs/sv files'}, status_code=400)

    tasks = []

    for i in range(0, len(files), 2):
        file_cs = files[i]
        file_sv = files[i+1]

        if not (allowed_file(file_cs.filename) and allowed_file(file_sv.filename)):
            continue

        cs_path = os.path.join(config.UPLOAD_FOLDER_PATH, file_cs.filename)
        sv_path = os.path.join(config.UPLOAD_FOLDER_PATH, file_sv.filename)

        with open(cs_path, "wb") as f:
            f.write(await file_cs.read())
        with open(sv_path, "wb") as f:
            f.write(await file_sv.read())

        task = analyze_voice_task.delay(cs_path, sv_path)
        tasks.append({'task_id': task.id, 'file_cs': file_cs.filename, 'file_sv': file_sv.filename})

    return JSONResponse(content={'message': 'Batch upload started', 'tasks': tasks, 'code': 202}, status_code=202)
