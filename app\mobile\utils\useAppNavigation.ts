import {NavigationProp, useNavigation} from '@react-navigation/native';
import {LessonStackParamList} from '../screens/Navigation/LessonStack';
import {VoiceAmplificationStackParamList} from '../screens/Navigation/VoiceAmplificationStack';
import {VoiceEvaluationStackParamList} from '../screens/Navigation/VoiceEvaluationStack';

export const useLessonNavigation = () => {
  return useNavigation<NavigationProp<LessonStackParamList>>();
};

export const useVoiceNavigation = () => {
  return useNavigation<NavigationProp<VoiceAmplificationStackParamList>>();
};

export const useVoiceEvaluationNavigation = () => {
  return useNavigation<NavigationProp<VoiceEvaluationStackParamList>>();
};
