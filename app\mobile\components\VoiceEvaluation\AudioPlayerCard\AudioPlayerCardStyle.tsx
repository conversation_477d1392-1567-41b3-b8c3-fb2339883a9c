import { StyleSheet } from 'react-native';
import { useTheme } from 'react-native-paper';

const useAudioPlayerCardStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    mainContainer: {
      width: '100%',
      display: 'flex',
      gap: 30,
      paddingVertical: 12,
      paddingHorizontal: 25,
      alignItems: 'center',
    },

    buttonContainer: {
      display: 'flex',
      flexDirection: 'row',
    },

    waveFormContainer: {
      display: 'flex',
      flexDirection: 'column',
      width: '70%',
      borderRadius: 20,
      overflow: 'hidden',
      paddingBottom: 30,
      paddingTop: 10,
      marginTop: 5,
      marginBottom: 10,
      height: 130,
    },

    innerWaveFormContainer: {
      margin: 10,
      marginBottom: 10,
      backgroundColor: 'white',
      borderRadius: 50,
      padding: 5,
      width: '80%',
      alignSelf: 'center',
      flexDirection: 'row',
      alignItems: 'center',
    },

    playButtonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },

    playButton: {
      backgroundColor: theme.colors.primary,
      margin: 0,
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },

    waveFormAndTimeContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 10,
    },

    timeText: {
      color: '#888',
      fontSize: 14,
      marginLeft: 5,
    },

    bottomButtonsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 15,
      paddingBottom: 0,
      marginTop: 10,
      gap: 100, // Increased gap between icons
    },

    actionButton: {
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      margin: 0,
      width: 40,
      height: 40,
      borderRadius: 20,
    },

    CardStyle: {
      display: 'flex',
      paddingTop: 8,
      paddingHorizontal: 10,
      paddingBottom: 20,
      flexDirection: 'column',
      justifyContent: 'flex-start',
      alignItems: 'center',
      borderRadius: 20,
      width: '100%',
      overflow: 'hidden',
    },

    titleStyle: {
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      height: 50,
      paddingHorizontal: 10,
    },

    textStyle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
  });
};

export default useAudioPlayerCardStyle;
