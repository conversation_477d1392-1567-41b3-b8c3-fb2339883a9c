import { StyleSheet } from 'react-native';
import { useTheme } from 'react-native-paper';
import { SearchBar } from 'react-native-screens';

export const VoiceAmplificationStyle = (showModal: boolean) => {
  const theme = useTheme();
  return StyleSheet.create({
    SafeViewStyled: {
      height: '100%',
      width: '100%',
      position: 'relative',
      backgroundColor: theme.colors.background,
    },
    ScrollViewStyled: {
      width: '100%',
      // rowGap: 20,
      // padding: 10,
      paddingTop: 20,
      position: 'relative',
      // height: '90%',
      backgroundColor: theme.colors.background,
    },
    SpeakIconStyled: {
      height: '12%',
      display: 'flex',
      alignItems: 'center',
      backgroundColor: 'transparent',
      position: 'absolute',
      bottom: 0,
      right: '6%',
    },

    modalStyled: {
      height: '100%',
      backgroundColor: 'transparent',
      marginHorizontal: 30,
      position: 'relative',
    },

    inputStyle: {
      position: 'absolute',
      bottom: 0,
      top: 340,
      display: showModal ? 'flex' : 'none',
    },

    appBarHeader: {
      backgroundColor: theme.colors.primary,
    },

    sectionHeader: {
      color: theme.colors.primary,
      fontWeight: 'bold',
      paddingLeft: 30,
      paddingVertical: 20,
    },

    sectionDivider: {
      width: '120%',
      height: 1,
      backgroundColor: '#ccc',
      marginHorizontal: 10,
      marginLeft: '-5%',
      marginTop: -5,
    },

    searchBar: {
      width: '100%',
      height: 45,
      alignSelf: 'center',
      backgroundColor: theme.colors.background,
    },

    searchBarBox: {
      display: 'flex',
      position: 'absolute',
      height: 80,
      width: '100%',
      justifyContent: 'center',
    },

    input: {
      position: 'relative',
      alignSelf: 'center',
      // opacity: 0.2,
      color: '#000000',
      fontWeight: '600',
      left: -20,
    },
  });
};
