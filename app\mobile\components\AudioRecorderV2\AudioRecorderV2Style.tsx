import {StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';
import {scale} from '../VoiceEvaluation/AudioRecorder/metrics';

const useAudioRecorderV2Style = () => {
  const theme = useTheme();
  return StyleSheet.create({
    container: {
      paddingHorizontal: 25,
      display: 'flex',
      justifyContent: 'space-between',
      flexDirection: 'row',
      borderRadius: 50,
      backgroundColor: theme.colors.primary,
    },
    textContainer: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',

      //   backgroundColor: 'white',
    },

    staticWaveformView: {
      width: '100%',
      height: scale(75),
    },
  });
};

export default useAudioRecorderV2Style;
