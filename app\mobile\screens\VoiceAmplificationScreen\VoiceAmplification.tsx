import {<PERSON><PERSON><PERSON><PERSON>, Mic} from 'lucide-react-native';
import {StyleSheet} from 'nativewind';
import React from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {IconButton, Modal, Portal, Searchbar} from 'react-native-paper';
import SentenceItem from './components/SentenceItem';
import {TopNav} from '../../components/TopNav/TopNav';
import VoiceInputModal from './components/VoiceInputModal';
import {SearchButton} from './components/SearchButton';
import {useVoiceAmplification} from './hooks/useVoiceAmplification';
import {VoiceAmplificationStyle} from './styles/VoiceAmplificationStyle';
import {Phrase} from '../../services/phraseManageService';
import {SentencePreview} from './components/SentencePreview';
import SpeakButton from '../../components/SpeakButton/SpeakButton';

const VoiceAmplification = () => {
  const {
    isSearching,
    setIsSearching,
    searchQuery,
    setSearchQuery,
    firstNotLikedIndex,
    filteredPhrases,
    currentTime,
    theme,
    isListening,
    setIsListening,
    handleDismissModal,
    handleShowModal,
    showModal,
    setShowModal,
    playingPreview,
    setPlayingPreview,
    iconImages,
    currentPhrase,
    setCurrentPhrase,
    setHighlightWord,
    setHighlightWordIndex,
    highlightedWordIndex,
    isSpeaking,
    setIsSpeaking,
  } = useVoiceAmplification();

  const style = VoiceAmplificationStyle(showModal);

  const renderSentenceItem = (phrase: Phrase) => {
    return (
      <SentenceItem
        key={phrase.phrase_id}
        phrase={phrase}
        currentTime={currentTime}
        setPlayingPreview={setPlayingPreview}
        setHighlightWord={setHighlightWord}
        setHighlightWordIndex={setHighlightWordIndex}
        isSpeaking={isSpeaking}
        setIsSpeaking={setIsSpeaking}
        setCurrentPhrase={() => setCurrentPhrase(phrase)}
      />
    );
  };

  return (
    <View style={style.SafeViewStyled}>
      <TopNav
        title="Voice Amplification"
        stopSearching={() => setIsSearching(false)}
        isSearching={isSearching}
        searchBar={
          <Searchbar
            placeholder="Search"
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoFocus
            style={style.searchBar}
            inputStyle={style.input}
            icon={() => null}
            clearIcon={() => null}
          />
        }>
        {!playingPreview && (
          <SearchButton
            isSearching={isSearching}
            setIsSearching={setIsSearching}
          />
        )}
      </TopNav>

      <ScrollView contentContainerStyle={style.ScrollViewStyled}>
        {!isSearching ? (
          <Text style={style.sectionHeader}>Favorites</Text>
        ) : (
          <Text style={style.sectionHeader}>Matched</Text>
        )}
        <View style={style.sectionDivider}></View>
        {firstNotLikedIndex !== -1
          ? filteredPhrases
              .slice(0, firstNotLikedIndex)
              .map(phrase => renderSentenceItem(phrase))
          : filteredPhrases.map(phrase => renderSentenceItem(phrase))}

        {!isSearching && <Text style={style.sectionHeader}>Others</Text>}
        {!isSearching && <View style={style.sectionDivider}></View>}
        {firstNotLikedIndex !== -1 &&
          filteredPhrases
            .slice(firstNotLikedIndex)
            .map(phrase => renderSentenceItem(phrase))}

        <View style={{height: 84}} />
      </ScrollView>

      {!playingPreview && (
        <View style={style.SpeakIconStyled}>
          <IconButton
            icon={() => <Mic color="white" size={30} />}
            size={40}
            mode="contained-tonal"
            containerColor={theme.colors.primary}
            onPress={handleShowModal}
          />
        </View>
      )}

      {playingPreview && (
        <>
          <View style={[style.SpeakIconStyled, {zIndex: 6}]}>
            <SpeakButton
              text={currentPhrase?.text || ''}
              isSpeaking={isSpeaking}
              setIsSpeaking={setIsSpeaking}
              languageCode={currentPhrase?.languageCode}
              setHighlightWord={setHighlightWord}
              setHighlightWordIndex={setHighlightWordIndex}
              size={35}
              buttonSize={40}
              buttonMode={'contained-tonal'}
              buttonColor={theme.colors.primary}
            />
          </View>
          <SentencePreview
            sentence={currentPhrase?.text || ''}
            highlightedWord={highlightedWordIndex}
            image={
              iconImages[
                (currentPhrase
                  ? currentPhrase.languageCode
                  : 'en-US') as keyof typeof iconImages
              ]
            }
            setIsSpeaking={setIsSpeaking}
            setPlayingPreview={setPlayingPreview}
          />
        </>
      )}

      <Portal>
        <Modal
          visible={showModal}
          onDismiss={handleDismissModal}
          contentContainerStyle={style.modalStyled}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{flex: 1, justifyContent: 'center'}}>
            <TouchableWithoutFeedback onPress={handleDismissModal}>
              <View style={{height: 420}} />
            </TouchableWithoutFeedback>

            <VoiceInputModal
              setIsListening={setIsListening}
              isListening={isListening}
              setShowModal={setShowModal}
            />

            <TouchableWithoutFeedback onPress={handleDismissModal}>
              <View style={{height: 150}} />
            </TouchableWithoutFeedback>
          </KeyboardAvoidingView>
        </Modal>
      </Portal>
    </View>
  );
};

export default VoiceAmplification;
