import {Dimensions, StyleSheet} from 'react-native';
import {Gesture} from 'react-native-gesture-handler';
import {useTheme} from 'react-native-paper';
import {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

export const LessonCardStyle = (handleDelete: any) => {
  const theme = useTheme();
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(1);
  const backgroundColor = useSharedValue('#FFFFFF');

  const {width: SCREEN_WIDTH} = Dimensions.get('window');
  const TRANSLATE_X_THRESHOLD = SCREEN_WIDTH / 3;
  const ICON_SHOW_TRESHOLD = SCREEN_WIDTH / 5;

  const panGesture = Gesture.Pan()
    .activeOffsetX([-20, 20]) // Must swipe more than 10px left or right
    .failOffsetY([-10, 10])
    .onUpdate(e => {
      if (e.translationX < 0) {
        translateX.value = e.translationX;
      }

      if (translateX.value < -TRANSLATE_X_THRESHOLD) {
        translateX.value = -TRANSLATE_X_THRESHOLD - 1;
      } else if (translateX.value > TRANSLATE_X_THRESHOLD) {
        translateX.value = TRANSLATE_X_THRESHOLD + 1;
      }

      if (translateX.value < -(SCREEN_WIDTH / 100)) {
        backgroundColor.value = '#FF7870';
      } else {
        backgroundColor.value = '#FFFFFF';
      }
    })
    .onEnd(e => {
      const shouldDelete = translateX.value < -TRANSLATE_X_THRESHOLD;

      if (shouldDelete) {
        translateX.value = withTiming(-SCREEN_WIDTH);
        opacity.value = withTiming(0, undefined, () => {
          runOnJS(handleDelete)();
        });
      } else {
        translateX.value = withTiming(0);
      }
    });

  const rDeleteIconStyle = useAnimatedStyle(() => {
    const opacity = withTiming(translateX.value < -ICON_SHOW_TRESHOLD ? 1 : 0);
    return {opacity};
  });

  const rStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: translateX.value,
      },
    ],
  }));

  const bgAnimatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: backgroundColor.value,
    };
  });

  const rTaskContainerStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  const styles = StyleSheet.create({
    horizontal: {
      flexDirection: 'row', // Arrange items horizontally
      alignItems: 'center', // Center items vertically
      justifyContent: 'space-between',
    },
    card: {
      width: '100%',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.background,
    },
    lessonInfo: {
      display: 'flex',
      flexDirection: 'column',
      padding: 10,
      flexGrow: 1,
    },
    textContainer: {
      fontWeight: 'bold',
      fontSize: 18,
      color: '#000',
    },
    subTitle: {
      fontWeight: '600',
      opacity: 0.6,
      color: '#000',
    },
    button: {
      flexDirection: 'row', // Arrange items horizontally
      justifyContent: 'flex-end', // Space items evenly
    },
    deleteIcon: {
      position: 'absolute',
      right: '5%',
      display: 'flex',
      top: '50%',
      transform: [{translateY: -12}],
    },
    swipeBg: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      width: '100%',
    },
  });

  return {
    styles,
    rTaskContainerStyle,
    bgAnimatedStyle,
    rStyle,
    panGesture,
    rDeleteIconStyle,
  };
};
