import React, {useEffect, useState} from 'react';
import {View} from 'react-native';

import AudioRecorderPlayer, {
  AVEncoderAudioQualityIOSType,
  AVEncodingOption,
  AudioEncoderAndroidType,
  AudioSourceAndroidType,
} from 'react-native-audio-recorder-player';

type AudioRecorderProps = {
  isRecording: boolean;

  setFilePath: React.Dispatch<React.SetStateAction<string>>;
  setIsRecording: React.Dispatch<React.SetStateAction<boolean>>;
  isActive: boolean;
};

const audioRecorderPlayer = new AudioRecorderPlayer();

const AudioRecorderV2: React.FC<AudioRecorderProps> = ({
  isRecording,
  setFilePath,
  setIsRecording,
  isActive,
}) => {
  const [recordSecs, setRecordSecs] = useState<number>(0);
  const [recordTime, setRecordTime] = useState<string>('00:00:00');
  const [currentPositionSec, setCurrentPositionSec] = useState<number>(0);
  const [currentDurationSec, setCurrentDurationSec] = useState<number>(0);
  const [playTime, setPlayTime] = useState<string>('00:00:00');
  const [duration, setDuration] = useState<string>('00:00:00');

  const [isRecordingState, setIsRecordingState] = useState<boolean>(false);

  useEffect(() => {
    if (isRecording) {
      if (!isActive) {
        onStopRecord();
      }
      onStartRecord();
    } else {
      onStopRecord();
    }

    audioRecorderPlayer.addRecordBackListener((e: any) => {
      setRecordSecs(e.current_position);
      setRecordTime(audioRecorderPlayer.mmssss(Math.floor(e.current_position)));
    });

    return () => {
      audioRecorderPlayer.removeRecordBackListener();
    };
  }, [isRecording]);

  const onStartRecord = async () => {
    let currentTime = Date.now();
    const path = `file_${currentTime}.wav`;
    const audioSet = {
      AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
      AudioSourceAndroid: AudioSourceAndroidType.MIC,
      AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
      AVNumberOfChannelsKeyIOS: 2,
      AVFormatIDKeyIOS: AVEncodingOption.wav,
    };
    audioRecorderPlayer.addRecordBackListener((e: any) => {
      setRecordSecs(e.current_position);
      setRecordTime(audioRecorderPlayer.mmssss(Math.floor(e.current_position)));
    });
    setFilePath('');
    setIsRecordingState(true);
    const uri = await audioRecorderPlayer.startRecorder(path, audioSet);
  };

  const onStopRecord = async () => {
    if (!isRecordingState) {
      return;
    }
    setIsRecordingState(false);
    const result = await audioRecorderPlayer.stopRecorder();
    audioRecorderPlayer.removeRecordBackListener();
    setFilePath(result);
    setRecordSecs(0);
  };

  return <View></View>;
};

export default AudioRecorderV2;
