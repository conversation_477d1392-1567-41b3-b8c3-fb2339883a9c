from celery import shared_task
from app.analyzer.analyzer_voice import analyze_voice

@shared_task(name="app.tasks.analyze_voice.analyze_voice_task", bind=True, track_started=True)
def analyze_voice_task(self, file_cs_path, file_sv_path):
    task_id = self.request.id

    try:
        result = analyze_voice(file_cs_path, file_sv_path, task_id)
        return {
            'code': 200,
            'message': 'Request processed successfully',
            'data': result
        }
    except Exception as e:
        return {
            'status': 'failed',
            'code': 500,
            'message': str(e)
        }
