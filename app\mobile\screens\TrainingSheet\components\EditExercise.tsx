import React, {useState} from 'react';
import {Modal, View, Text} from 'react-native';
import {Exercise} from '../../../services/exerciseManageService';
import {TextInput, Button} from 'react-native-paper';
import {EditExerciseStyle} from '../styles/EditExerciseStyle';

type EditExerciseModalProps = {
  exercise: Exercise;
  visible: boolean;
  onClose: () => void;
  onSave: (updatedExercise: Exercise) => void;
};

const EditExerciseModal: React.FC<EditExerciseModalProps> = ({
  exercise,
  visible,
  onClose,
  onSave,
}) => {
  const [name, setName] = useState(exercise.name);
  const [content, setContent] = useState(exercise.content);

  const handleSave = () => {
    const updatedExercise = {...exercise, name};
    onSave(updatedExercise);
    onClose();
  };

  const styles = EditExerciseStyle();

  return (
    <Modal visible={visible} transparent={true} animationType="slide">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Edit Exercise</Text>
          <TextInput
            style={styles.input}
            value={name}
            onChangeText={setName}
            mode="outlined"
          />

          <TextInput
            style={styles.input}
            // value={content}
            // onChangeText={setContent}
            mode="outlined"
          />

          <Button style={styles.button} mode="contained" onPress={handleSave}>
            Save
          </Button>
          <Button mode="outlined" onPress={onClose}>
            Cancel
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default EditExerciseModal;
