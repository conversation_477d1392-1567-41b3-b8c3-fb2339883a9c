import { StyleSheet } from 'nativewind';
import React, { useEffect } from 'react';
import { Image, View } from 'react-native';
import {
  Camera,
  getCameraDevice,
  useCameraPermission,
} from 'react-native-vision-camera';

type RecordAudioProps = {
  isActive: boolean;
};

const RecordAudio: React.FC<RecordAudioProps> = ({ isActive }) => {
  const { hasPermission, requestPermission } = useCameraPermission();

  const devices = Camera.getAvailableCameraDevices();
  const device = getCameraDevice(devices, 'front');

  useEffect(() => {
    if (!hasPermission) {
      requestPermission();
    }
    if (device == null) {
      console.log('No camera found');
    }
  }, []);

  const style = StyleSheet.create({
    mainContainer: {
      height: 410,
      width: '100%',
    },

    circleFrameContainer: {
      width: '100%',
      display: 'flex',
      alignItems: 'center',
      overflow: 'hidden',
      height: 410,
    },

    circleFrame: {
      width: '100%',
      height: 420,
    },

    textContainer: {
      width: '100%',
      height: '26%',
      paddingVertical: 32,
      paddingHorizontal: 32,
      display: 'flex',
    },

    text: {
      fontSize: 20,
      fontWeight: 'bold',
      textAlign: 'center',
    },
  });

  return (
    <View style={style.mainContainer}>
      <View style={style.circleFrameContainer}>
        <View style={style.circleFrame}>
          {device && (
            <Camera
              device={device}
              resizeMode="cover"
              isActive={isActive}
              onPreviewStarted={() => console.log('Preview started!')}
              onPreviewStopped={() => console.log('Preview stopped!')}
              style={{ flex: 1 }}></Camera>
          )}
        </View>
        <View
          style={{
            position: 'absolute',
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            zIndex: 100,
            overflow: 'hidden',
            justifyContent: 'center',
            top: -100,
            // height: '100%',
          }}>
          <Image
            resizeMode="cover"
            style={{ height: 800 }}
            source={require('../../assets/genericFace.png')}></Image>
        </View>
      </View>

      {/* <View style={{paddingHorizontal: 40}}>
        <AudioRecorder
          filePath={file}
          setFilePath={setFile}
          isRecording={isRecording}></AudioRecorder>
        <AudioRecorderV2
          isActive={isActive}
          setFilePath={setFileTemp}
          isRecording={isRecording}></AudioRecorderV2>
      </View> */}
    </View>
  );
};

export default RecordAudio;