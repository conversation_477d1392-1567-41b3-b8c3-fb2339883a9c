import React from 'react';
import { ScrollView, View } from 'react-native';
import { Button, Card, IconButton, Text, TextInput } from 'react-native-paper';

import BottomSheetWrapper from '../../../components/BottomSheetComponent/BottomSheetWrapper';

import { Plus, ArrowLeft, Save, BookHeart, PenLine } from 'lucide-react-native';
import { useAddTemplate } from '../hooks/useAddTemplate';
import { AddTemplateStyle } from '../styles/AddTemplateStyle';

const AddTemplate = () => {
    const {
        handleAddExercise,
        bottomSheetModalRef,
        value,
        changeScreen,
        templates,
        addTemplateExercises,
        lessonName,
        setLessonName,
        handleAddLessonAuto,
        theme,
    } = useAddTemplate();

    const styles = AddTemplateStyle();

    return (
        <View>
            {/* <IconButton
        mode="contained-tonal"
        icon={() => <Plus size={30} color="white" />}
        size={40}
        containerColor={theme.colors.primary}
        onPress={handleAddExercise}
      /> */}

            <Button style={styles.button} onPress={handleAddExercise}>
                <Text style={styles.buttonText}>Add More +</Text>
            </Button>
            <BottomSheetWrapper
                title={'Add New Session'}
                bottomSheetModalRef={bottomSheetModalRef}>
                {value == 'template' ? (
                    <ScrollView contentContainerStyle={styles.scrollView}>
                        <Card
                            key={'Manual'}
                            style={[styles.card, { backgroundColor: 'white' }]}
                            contentStyle={styles.cardContent}
                            mode="outlined"
                            onPress={() => changeScreen()}>
                            <View
                                style={{
                                    width: 70,
                                    height: 50,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                <PenLine color={theme.colors.tertiary} size={40} />
                            </View>
                            <Text
                                style={{
                                    fontSize: 20,
                                    color: theme.colors.tertiary,
                                }}>
                                Manual
                            </Text>
                        </Card>
                        {templates.map(item => (
                            <Card
                                key={item.name}
                                style={[styles.card, { backgroundColor: 'white' }]}
                                contentStyle={styles.cardContent}
                                mode="outlined"
                                onPress={() => addTemplateExercises(item)}>
                                <View
                                    style={{
                                        width: 70,
                                        height: 50,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}>
                                    <BookHeart color={theme.colors.primary} size={40} />
                                </View>
                                <Text
                                    style={{
                                        fontSize: 20,
                                        color: theme.colors.primary,
                                    }}>
                                    {item.name}
                                </Text>
                            </Card>
                        ))}
                    </ScrollView>
                ) : (
                    <View style={{ height: '100%', paddingBottom: 120 }}>
                        <ScrollView contentContainerStyle={styles.bottomSheet}>
                            <TextInput
                                label="Session Name"
                                value={lessonName}
                                onChangeText={text => setLessonName(text)}
                                style={styles.input}
                                mode="outlined"
                            />
                        </ScrollView>
                        <View style={[styles.buttonSheetContainer]}>
                            <View style={{ backgroundColor: 'gray', borderRadius: 50 }}>
                                <IconButton
                                    mode="outlined"
                                    style={styles.button}
                                    size={36}
                                    onPress={changeScreen}
                                    icon={() => <ArrowLeft size={30} color={'white'} />}
                                />
                            </View>
                            <View
                                style={{
                                    backgroundColor: theme.colors.primary,
                                    borderRadius: 50,
                                }}>
                                <IconButton
                                    mode="outlined"
                                    style={styles.button}
                                    size={36}
                                    onPress={() => handleAddLessonAuto()}
                                    icon={() => <Save size={30} color={'white'} />}
                                />
                            </View>
                        </View>
                    </View>
                )}
            </BottomSheetWrapper>
        </View>
    );
};
export default AddTemplate;
