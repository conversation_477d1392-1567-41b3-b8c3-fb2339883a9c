import {
    IWaveformRef,
    PermissionStatus,
    PlayerState,
    RecorderState,
    UpdateFrequency,
    Waveform,
} from '@simform_solutions/react-native-audio-waveform';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, Linking, NativeModules, Platform, View } from 'react-native';
import { useTheme } from 'react-native-paper';

import { IAudioWaveforms } from '@simform_solutions/react-native-audio-waveform/lib/types';
import useAudioRecorderStyle from './AudioRecorderStyle';

export interface ListItem {
    fromCurrentUser: boolean;
    path: string;
}

type AudioRecorderProps = {
    isRecordingState: boolean;
    filePath: string;
    // waveFormRef: RefObject<IWaveformRef>;
};

const AudioRecorder: React.FC<AudioRecorderProps> = ({
    isRecordingState,
    filePath,
    // waveFormRef,
}) => {
    const style = useAudioRecorderStyle();
    const [isRecording, setIsRecording] = useState<boolean>(false);
    const [recorderState, setRecorderState] = useState(RecorderState.stopped);
    const waveFormRef = useRef<IWaveformRef>(null);

    const { checkHasAudioRecorderPermission, getAudioRecorderPermission } =
        useAudioPermission();

    useEffect(() => {
        if (isRecordingState) {
            handleRecorderAction();
        } else {
            // Only try to stop if we were actually recording
            if (isRecording) {
                stopRecording();
            }
            resetWaveform();
        }
    }, [isRecordingState]);

    useEffect(() => {
        return () => {
            try {
                // More defensive cleanup
                if (isRecording) {
                    stopRecording();
                }
                resetWaveform();
            } catch (error) {
                console.error('Error in cleanup:', error);
            }
        };
    }, []);

    const handleRecorderAction = async () => {
        let hasPermission = await checkHasAudioRecorderPermission();

        if (hasPermission === PermissionStatus.granted) {
            startRecording();
        } else if (hasPermission === PermissionStatus.undetermined) {
            const permissionStatus = await getAudioRecorderPermission();
            if (permissionStatus === PermissionStatus.granted) {
                startRecording();
            }
        } else {
            Linking.openSettings();
        }
    };

    const startRecording = () => {
        // Check if already recording to prevent multiple start attempts
        if (isRecording) {
            console.log('Already recording, ignoring start request');
            return;
        }

        // Check if waveform ref exists
        if (!waveFormRef.current) {
            console.error('Waveform ref is null, cannot start recording');
            Alert.alert(
                "Recording Error",
                "Failed to initialize recording. Please try again.",
                [{ text: "OK" }]
            );
            return;
        }

        const doStartRecording = async () => {
            try {
                // For iOS, add a small delay before starting recording
                if (Platform.OS === 'ios') {
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                setIsRecording(true);

                // Use a lower update frequency on iOS to reduce processing load
                const frequency = Platform.OS === 'ios'
                    ? UpdateFrequency.low
                    : UpdateFrequency.medium;

                // Double-check that waveFormRef.current is not null
                if (waveFormRef.current) {
                    await waveFormRef.current.startRecord({
                        updateFrequency: frequency,
                    });
                } else {
                    throw new Error('Waveform reference is null');
                }

            } catch (err) {
                console.error(`${Platform.OS}: Error starting recording:`, err);
                setIsRecording(false); // Reset state if start fails

                // Show error to user
                Alert.alert(
                    "Recording Error",
                    "Failed to start recording. Please try again.",
                    [{ text: "OK" }]
                );
            }
        };

        // Start recording with proper error handling
        doStartRecording().catch(error => {
            console.error(`${Platform.OS}: Exception in startRecording:`, error);
            setIsRecording(false); // Reset state if exception occurs

            Alert.alert(
                "Recording Error",
                "An unexpected error occurred. Please try again.",
                [{ text: "OK" }]
            );
        });
    };

    const stopRecording = () => {
        if (!isRecording) {
            console.log('Not recording, no need to stop');
            return;
        }

        setIsRecording(false);

        // Make sure waveFormRef exists before trying to stop
        if (!waveFormRef.current) {
            console.log('Waveform ref is null, cannot stop recording');
            return;
        }

        const doStopRecording = async () => {
            try {
                // Platform-specific handling
                if (Platform.OS === 'ios') {
                    console.log('iOS: Attempting to stop recording');

                    // Add a small delay before stopping on iOS
                    await new Promise(resolve => setTimeout(resolve, 100));

                    try {
                        // Double-check that waveFormRef.current is not null
                        if (waveFormRef.current) {
                            await waveFormRef.current.stopRecord();
                            console.log('iOS: Recording stopped successfully');
                        } else {
                            console.warn('iOS: Waveform reference is null during stop');
                        }
                    } catch (stopError) {
                        console.error('iOS: Error stopping recording:', stopError);
                        // Continue even if there's an error - we need to clean up
                    }
                } else {
                    // Android handling
                    try {
                        // Double-check that waveFormRef.current is not null
                        if (waveFormRef.current) {
                            await waveFormRef.current.stopRecord();
                            console.log('Android: Recording stopped successfully');
                        } else {
                            console.warn('Android: Waveform reference is null during stop');
                        }
                    } catch (androidError) {
                        console.error('Android: Error stopping recording:', androidError);
                    }
                }
            } catch (error) {
                console.error('Unexpected error in stopRecording:', error);
            }
        };

        // Stop recording with proper error handling
        doStopRecording().catch(error => {
            console.error('Fatal error in stopRecording:', error);
        });
    };

    const resetWaveform = () => {
        try {
            console.log('Resetting waveform');
            setRecorderState(RecorderState.stopped);
            setIsRecording(false);

            if (waveFormRef.current) {
                if (Platform.OS === 'ios') {
                    try {
                        if (isRecording) {
                            waveFormRef.current.stopRecord().catch((err) => {
                                console.log('iOS: Error stopping waveform recording:', err);
                            });
                        }
                    } catch (iosError) {
                        console.log('iOS: Exception in waveform reset:', iosError);
                    }
                } else {
                    waveFormRef.current.stopRecord().catch((err) => {
                        console.log('Android: Waveform was not recording or error occurred:', err);
                    });
                }
            }
        } catch (error) {
            console.error('Error resetting waveform:', error);
        }
    };

    const theme = useTheme();

    return (
        <View style={[style.container]}>
            <View style={{ height: 60, flex: 1 }}>
                {filePath && !isRecordingState ? (
                    <StaticWaveForm path={filePath}></StaticWaveForm>
                ) : (
                    <Waveform
                        containerStyle={{ height: 60 }}
                        mode="live"
                        ref={waveFormRef}
                        candleSpace={2}
                        candleHeightScale={2}
                        onRecorderStateChange={setRecorderState}
                        waveColor={'white'}
                        candleWidth={4}></Waveform>
                )}
            </View>
            {/* <IconButton icon={'trash'} onPress={startRecording}></IconButton> */}
        </View>
    );
};

type WaveFormProps = {
    path: string;
};

export const StaticWaveForm: React.FC<WaveFormProps> = ({ path }) => {
    const waveFormRef = useRef<IWaveformRef>(null);
    const [playerState, setPlayerState] = useState(PlayerState.stopped);
    const [isLoading, setIsLoading] = useState(true);
    const [currentPlaying, setCurrentPlaying] = useState('');
    const [error, setError] = useState<string | null>(null);
    const style = useAudioRecorderStyle();
    const theme = useTheme();

    const [file, setFile] = useState<string>('');

    useEffect(() => {
        if (path !== '') {
            setFile(path);
        }
    }, [path]);

    return (
        <View style={{ height: 60, width: '100%' }}>
            {file ? (
                <Waveform
                    containerStyle={{ ...style.staticWaveformView, height: 60 }}
                    mode="static"
                    ref={waveFormRef}
                    path={file}
                    candleSpace={2}
                    candleWidth={3}
                    scrubColor={theme.colors.secondary}
                    waveColor={'#B5E8FF'}
                    candleHeightScale={2}
                    onPlayerStateChange={state => {
                        setPlayerState(state);
                        if (state === PlayerState.stopped && currentPlaying === path) {
                            setCurrentPlaying('');
                        }
                    }}
                    onPanStateChange={_state => {
                        // Pan state changed
                    }}
                    onError={error => {
                        console.error(`StaticWaveForm: Error with waveform: ${error}`);
                        setError(`Waveform error: ${error}`);
                    }}
                    onCurrentProgressChange={(_currentProgress, _songDuration) => {
                        // Progress updated
                    }}
                    onChangeWaveformLoadState={state => {
                        setIsLoading(state);
                    }}
                />
            ) : (
                <View style={{ ...style.staticWaveformView, height: 60 }} />
            )}
        </View>
    );
};

export const useAudioPermission = () => {
    const checkHasAudioRecorderPermission = () =>
        AudioWaveform.checkHasAudioRecorderPermission();

    const getAudioRecorderPermission = () =>
        AudioWaveform.getAudioRecorderPermission();

    const checkHasAudioReadPermission = () =>
        AudioWaveform.checkHasAudioReadPermission();

    const getAudioReadPermission = () => AudioWaveform.getAudioReadPermission();

    return {
        checkHasAudioRecorderPermission,
        getAudioRecorderPermission,
        checkHasAudioReadPermission,
        getAudioReadPermission,
    };
};

const LINKING_ERROR =
    "The package 'react-native-audio-waveform' doesn't seem to be linked. Make sure: \n\n" +
    Platform.select({ ios: "- You have run 'pod install'\n", default: '' }) +
    '- You rebuilt the app after installing the package\n' +
    '- You are not using Expo Go\n';

export const AudioWaveform: IAudioWaveforms = NativeModules.AudioWaveform
    ? NativeModules.AudioWaveform
    : new Proxy(
        {},
        {
            get() {
                throw new Error(LINKING_ERROR);
            },
        },
    );

export default AudioRecorder;