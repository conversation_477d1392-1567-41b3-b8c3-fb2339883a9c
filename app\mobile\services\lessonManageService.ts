import {Dispatch, SetStateAction} from 'react';
import {SQLiteDatabase, openDatabase} from 'react-native-sqlite-storage';
import {Exercise, openExerciseDatabase} from './exerciseManageService';

export type Lesson = {
  id: number;
  name: string;
  exercises: Exercise[];
  target: number;
};

type LessonAction = {
  setLessons: Dispatch<SetStateAction<Lesson[]>>;
};

export type LessonService = Lesson & LessonAction;

export const openLessonDatabase = async () => {
  return openDatabase({
    name: 'Lessons.db',
    location: 'default',
  });
};

export const fetchLesson = async (
  db: SQLiteDatabase,
  setLessons: Dispatch<SetStateAction<Lesson[]>>,
) => {
  try {
    const e_db = await openExerciseDatabase();
    const results = await db.executeSql(
      'SELECT * FROM Lessons ORDER BY target DESC, id DESC;',
    );
    const rows = results[0].rows;
    let lessons: Lesson[] = [];
    for (let i = 0; i < rows.length; i++) {
      const lesson: Lesson = rows.item(i);
      lesson.exercises = await fetchExercisesByLId(e_db, lesson.id);
      lessons.push(lesson);
      // console.log(lesson);
    }
    setLessons(lessons);
  } catch (error) {
    console.error('Error fetching Lessons:', error);
  }
};

export const fetchExercisesByLId = async (db: SQLiteDatabase, id: number) => {
  try {
    const results = await db.executeSql(
      'SELECT * FROM Exercises WHERE lesson_id = ?;',
      [id],
    );
    const rows = results[0].rows;
    let l_exercises: Exercise[] = [];
    for (let i = 0; i < rows.length; i++) {
      let item = rows.item(i);
      item.content = JSON.parse(item.content);
      l_exercises.push(item);
    }
    return [...l_exercises];
  } catch (error) {
    console.error('Error fetching exercises by lesson_id:', error);
    return [];
  }
};

export const createNewLessonTable = async (db: SQLiteDatabase) => {
  try {
    const results = await db.executeSql('SELECT * FROM Lessons;');
    const rows = results[0].rows;
    let lessons: Lesson[] = [];

    for (let i = 0; i < rows.length; i++) {
      lessons.push(rows.item(i));
    }
    await db.executeSql(
      `CREATE TABLE IF NOT EXISTS new_lesson (
        phrase_id INTEGER PRIMARY KEY AUTOINCREMENT,
        text TEXT,
        isLiked BOOLEAN DEFAULT 0,
        useCount INTEGER DEFAULT 0,
        lastUsed TEXT
      );`,
    );

    lessons.map(async lesson => {
      await db.executeSql('INSERT INTO Lessons (name, target) VALUES (?, ?);', [
        lesson.name,
        1,
      ]);
    });

    await db.executeSql('DROP TABLE Lessons;');
    await db.executeSql('ALTER TABLE new_lesson RENAME TO Lessons;');
  } catch (error) {
    console.error('Error creating new table:', error);
  }
};

export const createLessonTable = async (db: SQLiteDatabase) => {
  try {
    await db.executeSql(
      'CREATE TABLE IF NOT EXISTS Lessons (id INTEGER PRIMARY KEY AUTOINCREMENT, name TEXT, target INTEGER);',
    );
  } catch (error) {
    console.error('Error creating or updating lesson table:', error);
  }
};

export const insertLesson = async (
  db: SQLiteDatabase,
  name: string,
  setLessons?: Dispatch<SetStateAction<Lesson[]>>,
) => {
  try {
    await db.executeSql('INSERT INTO Lessons (name, target) VALUES (?, ?);', [
      name,
      1,
    ]);
    if (setLessons) {
      await fetchLesson(db, setLessons);
    }
  } catch (error) {
    console.error('Error inserting Lesson:', error);
  }
};

export const getLastLessonId = async (db: SQLiteDatabase) => {
  try {
    let lastId: number | null = null;

    await db.transaction(tx => {
      tx.executeSql(
        'SELECT id FROM Lessons ORDER BY id DESC LIMIT 1;',
        [],
        (_, {rows}) => {
          if (rows.length > 0) {
            lastId = rows.item(0).id;
          }
        },
        (_, error) => {
          console.error('Error fetching last lesson ID:', error);
          return false;
        },
      );
    });

    return lastId;
  } catch (error) {
    console.error('Error fetching last lesson ID:', error);
    return null;
  }
};

export const editLessonName = async (
  db: SQLiteDatabase,
  id: number,
  newName: string,
  setLessons: Dispatch<SetStateAction<Lesson[]>>,
  setCurrentLesson: Dispatch<SetStateAction<Lesson>>,
) => {
  try {
    await db.executeSql('UPDATE Lessons SET name = ? WHERE id = ?;', [
      newName,
      id,
    ]);
    await fetchLesson(db, setLessons);
    await fetchLessonById(db, id, setCurrentLesson);
    // console.log('Lesson name updated successfully');
  } catch (error) {
    console.error('Error updating Lesson name:', error);
  }
};

export const deleteLesson = async (
  id: number,
  setLessons: Dispatch<SetStateAction<Lesson[]>>,
) => {
  try {
    //Delete exercises of lesson
    const e_db = await openExerciseDatabase();
    const exercises = await fetchExercisesByLId(e_db, id);

    for (const ex of exercises) {
      await e_db.executeSql('DELETE FROM Exercises WHERE id = ?;', [ex.id]);
    }
    e_db.close();

    //Delete lesson
    const db = await openLessonDatabase();
    await db.executeSql('DELETE FROM Lessons WHERE id = ?;', [id]);
    await fetchLesson(db, setLessons);
    db.close();
    // console.log('Lesson deleted successfully');
  } catch (error) {
    console.error('Error deleting Lesson:', error);
  }
};

// export const deleteExLesson = async (lesson_id: number) => {
//   try {
//     const e_db = await openExerciseDatabase();
//     const exercises = await fetchExercisesByLId(e_db, lesson_id);

//     for (const ex of exercises) {
//       await e_db.executeSql('DELETE FROM Exercises WHERE id = ?;', [ex.id]);
//     }
//     // console.log(`All exercises for lesson id ${lesson_id} deleted successfully`);
//   } catch (error) {
//     console.error('Error deleting exercises:', error);
//   }
// };

export const fetchLessonById = async (
  db: SQLiteDatabase,
  id: number,
  setCurrentLesson: Dispatch<SetStateAction<Lesson>>,
) => {
  try {
    const results = await db.executeSql('SELECT * FROM Lessons WHERE id = ?;', [
      id,
    ]);
    if (results[0].rows.length > 0) {
      const item = results[0].rows.item(0);
      setCurrentLesson(item);
      return item as Lesson;
    } else {
      return null; // No exercise found with the given id
    }
  } catch (error) {
    console.error('Error fetching lesson by id:', error);
    return null;
  }
};

export const fetchLessonByName = async (db: SQLiteDatabase, name: string) => {
  try {
    const results = await db.executeSql(
      'SELECT * FROM Lessons WHERE name = ?;',
      [name],
    );
    if (results[0].rows.length > 0) {
      const item = results[0].rows.item(0);
      return item as Lesson;
    } else {
      return null; // No lesson found with the given name
    }
  } catch (error) {
    console.error('Error fetching lesson by id:', error);
    return null;
  }
};

export const getLessonNameById = async (db: SQLiteDatabase, id: number) => {
  try {
    const results = await db.executeSql(
      'SELECT name FROM Lessons WHERE id = ?;',
      [id],
    );
    if (results[0].rows.length > 0) {
      const item = results[0].rows.item(0);
      // console.log(item);
      return item as string;
    } else {
      return null; // No lesson found with the given name
    }
  } catch (error) {
    console.error('Error fetching lesson by id:', error);
    return null;
  }
};

export const setTarget = async (
  db: SQLiteDatabase,
  id: number,
  target: number,
  setLessons: Dispatch<SetStateAction<Lesson[]>>,
  setCurrentLesson: Dispatch<SetStateAction<Lesson>>,
) => {
  try {
    await db.executeSql('UPDATE Lessons SET target = ? WHERE id = ?;', [
      target,
      id,
    ]);
    await fetchLesson(db, setLessons);
    await fetchLessonById(db, id, setCurrentLesson);
    // console.log('Lesson target updated successfully', target);
  } catch (error) {
    console.error('Error updating Lesson target:', error);
  }
};
