import {useState, useRef, useEffect} from 'react';
import {Alert} from 'react-native';
import countryFlags, {
  LanguageItem,
  languagesTemplate,
} from '../../../utils/language';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';

type SettingsTab = 'App' | 'Profile' | 'Connect';

const useSettingsScreen = () => {
  const {autoEndPreview, setPreviewEnd, playbackSpeed, setPlaybackSpeed} =
    useSorting();

  const [activeTab, setActiveTab] = useState<SettingsTab>('App');
  const [localPlaybackSpeed, setLocalPlaybackSpeed] = useState<number>(
    playbackSpeed * 2,
  );
  const [autoExit, setAutoExit] = useState<boolean>(autoEndPreview);
  const {images} = countryFlags();
  const {language, setLanguage} = useSorting();
  const [showLanguageSelect, setShowLanguageSelect] = useState<boolean>(false);

  // Profile information state
  const [name, setName] = useState<string>('Dung Nguyen');
  const [dateOfBirth, setDateOfBirth] = useState<string>('08/09/2003');
  const [dateOfBirthDate, setDateOfBirthDate] = useState<Date>(
    new Date(2003, 8, 8),
  );
  const [gender, setGender] = useState<string>('Male');
  const [email, setEmail] = useState<string>('');

  // Original profile data for comparison using refs
  const originalName = useRef<string>('Dung Nguyen');
  const originalDateOfBirth = useRef<string>('08/09/2003');
  const originalGender = useRef<string>('Male');
  const originalEmail = useRef<string>('');

  // Edit mode state
  const [isEditingName, setIsEditingName] = useState<boolean>(false);
  const [isEditingEmail, setIsEditingEmail] = useState<boolean>(false);
  const [tempName, setTempName] = useState<string>(name);
  const [tempEmail, setTempEmail] = useState<string>(email);

  // Check if there are any changes to the profile
  const hasProfileChanges = (): boolean => {
    return (
      name !== originalName.current ||
      dateOfBirth !== originalDateOfBirth.current ||
      gender !== originalGender.current ||
      email !== originalEmail.current
    );
  };

  // Gender options
  const genderOptions = ['Male', 'Female', 'Other'];
  const [showGenderDropdown, setShowGenderDropdown] = useState<boolean>(false);

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);

  // Format date to display
  const formatDate = (date: Date): string => {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const year = date.getFullYear();
    return `${month.toString().padStart(2, '0')}/${day
      .toString()
      .padStart(2, '0')}/${year}`;
  };

  // Handle date change
  const handleDateChange = (date: Date) => {
    setDateOfBirthDate(date);
    setDateOfBirth(formatDate(date));
    setShowDatePicker(false);
  };

  // Handle gender selection
  const handleGenderSelect = (selectedGender: string) => {
    setGender(selectedGender);
    setShowGenderDropdown(false);
  };

  // Handle save profile changes
  const handleSaveProfile = () => {
    originalName.current = name;
    originalDateOfBirth.current = dateOfBirth;
    originalGender.current = gender;
    originalEmail.current = email;

    Alert.alert('Success', 'Profile changes saved successfully!');
  };

  const handlePlaybackAutoExit = (value: boolean) => {
    setAutoExit(value);
    setPreviewEnd(value);
  };

  const handleSetPlaybackSpeed = (value: number) => {
    setLocalPlaybackSpeed(value);
    setPlaybackSpeed(
      +(value / 2).toFixed(2) >= 1
        ? +(value / 2).toFixed(2) - 0.01
        : +(value / 2).toFixed(2),
    );
    console.log(playbackSpeed);
  };

  const [languages, setLanguages] = useState<LanguageItem[]>(languagesTemplate);

  // Update selected language when language context changes
  useEffect(() => {
    if (language) {
      setLanguages(
        languages.map(item => ({
          ...item,
          isSelected: item.code === language.code,
        })),
      );
    }
  }, [language]);

  // Initialize original values when component mounts
  useEffect(() => {
    originalName.current = name;
    originalDateOfBirth.current = dateOfBirth;
    originalGender.current = gender;
    originalEmail.current = email;
  }, []);

  const handleChangeLanguage = (selectedLanguage: {
    name: string;
    code: string;
    voiceCode: string;
    isSelected: boolean;
  }) => {
    setLanguages(
      languages.map(item => ({
        ...item,
        isSelected: item.code === selectedLanguage.code,
      })),
    );
    setLanguage(selectedLanguage);
  };

  const handleTabPress = (tab: SettingsTab) => {
    if (activeTab === 'Profile' && tab !== 'Profile') {
      setIsEditingName(false);
      setIsEditingEmail(false);
      setShowGenderDropdown(false);
    }
    setActiveTab(tab);
  };

  return {
    activeTab,
    setShowLanguageSelect,
    language,
    images,
    handleChangeLanguage,
    showLanguageSelect,
    localPlaybackSpeed,
    handleSetPlaybackSpeed,
    autoExit,
    handlePlaybackAutoExit,
    isEditingName,
    tempName,
    setTempName,
    setName,
    setIsEditingName,
    name,
    setShowDatePicker,
    dateOfBirth,
    showDatePicker,
    dateOfBirthDate,
    handleDateChange,
    showGenderDropdown,
    setShowGenderDropdown,
    gender,
    genderOptions,
    handleGenderSelect,
    isEditingEmail,
    setIsEditingEmail,
    tempEmail,
    setTempEmail,
    email,
    setEmail,
    hasProfileChanges,
    handleSaveProfile,
    handleTabPress,
  };
};

export default useSettingsScreen;
