# AudioPlayerCard Component

A unified audio player component that handles recording, playback, and review scenarios with consistent UI and waveform styling.

## Variants

### 1. **Playback** (Voice Analysis Audio Tab)
- Gradient card with checkmark and title
- Play/pause button with static waveform and time display
- Custom children slot for action buttons (e.g., ShareFileButton)

### 2. **Review** (Voice Recording Step 2)
- Same as playback but with re-record functionality
- Default re-record button (RotateCcw) when no children provided
- No share button by default

### 3. **Recording** (Voice Recording Steps 0 & 1)
- Simple container without gradient card styling
- Live waveform during recording, static waveform for playback
- Controlled externally via isRecording prop

## Usage

### Playback Variant (Voice Analysis)
```tsx
import AudioPlayerCard from '../AudioPlayerCard/AudioPlayerCard';
import ShareFileButton from '../ShareFile/ShareFileButton';

<AudioPlayerCard
  filePath={fileCs}
  filePathTemp={fileCs}
  title="Connected Speech"
  variant="playback">
  <ShareFileButton filePath={fileCs} iconColor="white" />
</AudioPlayerCard>
```

### Review Variant (Voice Recording Step 2)
```tsx
<AudioPlayerCard
  filePath={fileCs}
  filePathTemp={fileCsTemp}
  title="Connected Speech"
  variant="review"
  setCurrentPosition={setCurrentPosition}
/>
```

### Recording Variant (Voice Recording Steps 0 & 1)
```tsx
<AudioPlayerCard
  filePath={fileCs}
  title="Connected Speech"
  variant="recording"
  isRecording={isRecording}
  height={60}
/>
```

## Props

- `title: string` - Display title
- `filePath: string` - Audio file path
- `filePathTemp?: string` - Temporary file path for playback
- `variant: 'playback' | 'recording' | 'review'` - Component variant
- `isRecording?: boolean` - Recording state (recording variant)
- `setCurrentPosition?: (position: number) => void` - Navigation callback (review variant)
- `children?: React.ReactNode` - Custom action buttons
- `showTitle?: boolean` - Show/hide title (default: true)
- `showCheckmark?: boolean` - Show/hide checkmark (default: true)
- `height?: number` - Custom height for recording variant

## Features

- **Unified waveform styling** across all variants
- **Consistent audio playback logic** using audioPlayerManager
- **Platform-specific handling** for iOS audio paths
- **Automatic cleanup** of audio player subscriptions
- **Theme integration** for consistent colors
- **Flexible children** for custom action buttons

## File Structure
```
AudioPlayerCard/
├── AudioPlayerCard.tsx
├── AudioPlayerCardStyle.tsx
├── WaveformDisplay.tsx
└── README.md
```
