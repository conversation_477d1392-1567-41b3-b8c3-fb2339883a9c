import {Dimensions, ScaledSize} from 'react-native';

let {width, height}: ScaledSize = Dimensions.get('window');

if (width > height) {
  [width, height] = [height, width];
}

const guidelineBaseWidth = 375;
const guidelineBaseHeight = 812;

const baseWidth = width / guidelineBaseWidth;
const baseHeight = height / guidelineBaseHeight;

let baseSize = (baseWidth + baseHeight) / 2;

const scale = (size: number): number => Math.ceil(baseSize * size);

export {height, scale, width};