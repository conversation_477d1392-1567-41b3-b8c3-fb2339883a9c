import React, { useEffect, useState, useRef } from 'react';
import { View, TouchableOpacity, Platform } from 'react-native';
import { Text, IconButton, useTheme } from 'react-native-paper';
import { LinearGradient } from 'react-native-linear-gradient';
import { Check, Play, Pause, RotateCcw } from 'lucide-react-native';
import { StaticWaveForm } from '../AudioRecorder/AudioRecorder';
import { Waveform, IWaveformRef, UpdateFrequency } from 'react-native-audio-waveform';
import ShareFileButton from '../ShareFile/ShareFileButton';
import audioPlayerManager from '../../../services/audioPlayerManager';
import { useAudioPlayerCardStyle } from './AudioPlayerCardStyle';

export type AudioPlayerCardProps = {
  title: string;
  filePath: string;
  filePathTemp?: string;
  variant: 'playback' | 'recording' | 'review';
  isRecording?: boolean;
  setCurrentPosition?: (position: number) => void;
  children?: React.ReactNode;
  showTitle?: boolean;
  showCheckmark?: boolean;
  height?: number;
};

const AudioPlayerCard: React.FC<AudioPlayerCardProps> = ({
  title,
  filePath,
  filePathTemp,
  variant = 'playback',
  isRecording = false,
  setCurrentPosition,
  children,
  showTitle = true,
  showCheckmark = true,
  height,
}) => {
  const theme = useTheme();
  const styles = useAudioPlayerCardStyle(variant, height);
  const waveFormRef = useRef<IWaveformRef>(null);

  // Audio playback state (for playback and review variants)
  const componentId = `${title}-${filePathTemp || filePath}`;
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackTime, setPlaybackTime] = useState('0:00');

  // Format time for display (MM:SS format)
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${String(secs).padStart(2, '0')}`;
  };

  // Subscribe to audio player state changes (for playback variants)
  useEffect(() => {
    if (variant === 'recording') return;

    setIsPlaying(audioPlayerManager.isPlaying(componentId));

    const unsubscribe = audioPlayerManager.onPlayStateChanged(({ id, isPlaying }) => {
      if (id === componentId) {
        setIsPlaying(isPlaying);
      } else if (isPlaying) {
        setIsPlaying(false);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [componentId, variant]);

  const onStartPlay = async () => {
    try {
      const pathToPlay = filePathTemp || filePath;
      if (!pathToPlay) {
        console.error('AudioPlayerCard: Cannot play - file path is empty');
        return;
      }

      console.log(`AudioPlayerCard (${title}): Starting playback of ${pathToPlay}`);
      setPlaybackTime('0:00');

      if (Platform.OS === 'ios') {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      await audioPlayerManager.playAudio(pathToPlay, componentId);
    } catch (error) {
      console.error(`AudioPlayerCard (${title}): Error in onStartPlay:`, error);
      setIsPlaying(false);
    }
  };

  const onStopPlay = async () => {
    try {
      console.log(`AudioPlayerCard (${title}): Stopping playback`);
      if (audioPlayerManager.isPlaying(componentId)) {
        await audioPlayerManager.stopAudio();
      }
    } catch (error) {
      console.error(`AudioPlayerCard (${title}): Error in onStopPlay:`, error);
      setIsPlaying(false);
    }
  };

  const onRecordAgain = () => {
    if (title === 'Sustained Vowel') {
      setCurrentPosition && setCurrentPosition(1);
    } else {
      setCurrentPosition && setCurrentPosition(0);
    }
  };

  // Recording variant - simple waveform container
  if (variant === 'recording') {
    return (
      <View style={styles.recordingContainer}>
        <View style={styles.recordingWaveformContainer}>
          {filePath && !isRecording ? (
            <StaticWaveForm path={filePath} />
          ) : (
            <Waveform
              containerStyle={{ height: height || 60 }}
              mode="live"
              ref={waveFormRef}
              candleSpace={2}
              candleHeightScale={2}
              waveColor={'white'}
              candleWidth={4}
            />
          )}
        </View>
      </View>
    );
  }

  // Playback and Review variants - full gradient card
  return (
    <View style={styles.mainContainer}>
      <LinearGradient
        colors={['#e5f5f6', '#e2ecfc']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.cardStyle}>
        <LinearGradient
          colors={['#3C80F3', '#4EC3C7']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.waveFormContainer}>

          {/* Title with checkmark */}
          {showTitle && (
            <View style={styles.titleStyle}>
              {showCheckmark && (
                <Check size={20} color={theme.colors.primary} style={{ marginRight: 8 }} />
              )}
              <Text style={styles.textStyle}>{title}</Text>
            </View>
          )}

          {/* Audio player controls */}
          <View style={styles.innerWaveFormContainer}>
            <View style={styles.playButtonContainer}>
              <IconButton
                style={styles.playButton}
                icon={() =>
                  isPlaying ? (
                    <Pause size={20} color={'white'} fill={'white'} />
                  ) : (
                    <Play size={20} color={'white'} fill={'white'} />
                  )
                }
                size={20}
                onPress={() => (isPlaying ? onStopPlay() : onStartPlay())}
              />
            </View>
            <View style={styles.waveFormAndTimeContainer}>
              <View style={{ flex: 1, height: 40, justifyContent: 'center' }}>
                <StaticWaveForm path={filePath} />
              </View>
              <Text style={styles.timeText}>{playbackTime}</Text>
            </View>
          </View>

          {/* Bottom buttons */}
          <View style={styles.bottomButtonsContainer}>
            {children ? (
              children
            ) : (
              <View style={{ flexDirection: 'row', justifyContent: 'space-around', width: '50%' }}>
                <ShareFileButton filePath={filePathTemp || filePath} iconColor="white" />
                <RotateCcw
                  size={25}
                  color="white"
                  onPress={onRecordAgain}
                />
              </View>
            )}
          </View>
        </LinearGradient>
      </LinearGradient>
    </View>
  );
};

export default AudioPlayerCard;
