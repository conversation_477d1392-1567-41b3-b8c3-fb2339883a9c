import { BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useEffect, useRef, useState } from 'react';
import { useSorting } from '../../../components/SortingContextProvider/SortingContextProvider';
import useBottomSheet from '../../../components/BottomSheetComponent/useBottomSheet';
import { Alert } from 'react-native';

import {
    Exercise,
    insertEx,
    openExerciseDatabase,
} from '../../../services/exerciseManageService';
import {
    getLastLessonId,
    insertLesson,
    openLessonDatabase,
} from '../../../services/lessonManageService';
import { useTheme } from 'react-native-paper';

interface Template {
    name: string;
    icon: string;
    exercises: Exercise[];
}

const templatePath = '../Templates/';

export const useAddTemplate = () => {
    const theme = useTheme();
    const { language, setLanguage } = useSorting();
    const [selectedIcon, setSelectIcon] = useState<string>(language.code);
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);
    const { showBottomSheet, dismissBottomSheet } =
        useBottomSheet(bottomSheetModalRef);

    const { lessons, setLessons, setExercises } = useSorting();

    const [templates, setTemplates] = useState<Template[]>([]);
    const [value, setValue] = useState('template');
    const [lessonName, setLessonName] = React.useState('');

    useEffect(() => {
        const fetchTemplates = async () => {
            const templates = await loadTemplates();
            setTemplates(templates);
        };
        fetchTemplates();
    }, []);

    const addTemplateExercises = async (template: Template) => {
        const e_db = await openExerciseDatabase();
        const l_db = await openLessonDatabase();
        await insertLesson(l_db, template.name, setLessons);
        // const l = await fetchLessonByName(l_db, template.name);
        const l_id = await getLastLessonId(l_db);
        for (let i = 0; i < template.exercises.length; i++) {
            const lessonId = l_id || 0; // Assign a default value of 0 if l_id is undefined
            await insertEx(
                e_db,
                lessonId,
                template.exercises[i].name,
                template.exercises[i].content,
                setExercises,
            );
        }
        dismissBottomSheet();
    };

    const loadTemplates = () => {
        const template1 = require(templatePath + 'template1.json');
        const template2 = require(templatePath + 'template2.json');
        return [template1, template2];
    };

    const handleAddExercise = async () => {
        setValue('template');
        showBottomSheet();
    };

    const changeScreen = () => {
        if (value == 'data') {
            setValue('template');
        } else {
            setValue('data');
        }
    };

    const handleAddLessonAuto = async () => {
        if (!lessonName) {
            Alert.alert('Error', 'Please fill out all fields');
            return;
        }
        const db = await openLessonDatabase();
        await insertLesson(db, lessonName, setLessons);
        dismissBottomSheet();
        setLessonName('');
    };

    return {
        handleAddExercise,
        bottomSheetModalRef,
        value,
        changeScreen,
        templates,
        addTemplateExercises,
        lessonName,
        setLessonName,
        handleAddLessonAuto,
        theme,
    };
};
