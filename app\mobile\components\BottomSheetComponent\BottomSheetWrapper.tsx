// BottomSheetWrapper.js
import { BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { X } from 'lucide-react-native';
import { StyleSheet } from 'nativewind';
import React, { Dispatch, SetStateAction } from 'react';
import { View } from 'react-native';
import { Divider, IconButton, Text, useTheme } from 'react-native-paper';
import useBottomSheet from './useBottomSheet';

type BottomSheetWrapperProps = {
  title: string;
  children: React.ReactNode;
  bottomSheetModalRef: React.RefObject<any>;
  setIndex?: Dispatch<SetStateAction<number>>;
  children2?: any;
};

const BottomSheetWrapper: React.FC<BottomSheetWrapperProps> = ({
  title,
  children,
  bottomSheetModalRef,
  setIndex,
  children2,
}) => {
  const theme = useTheme();
  const { snapPoints, dismissBottomSheet, handleSheetChanges } =
    useBottomSheet(bottomSheetModalRef);

  const styles = StyleSheet.create({
    modalContainer: {
      width: '100%',
      margin: 0,
      backgroundColor: 'white',
    },
  });

  return (
    <View>
      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={1}
        snapPoints={snapPoints}
        enablePanDownToClose={true}
        onChange={index => {
          handleSheetChanges(index);
          setIndex && setIndex(index);
        }}>
        <BottomSheetView style={styles.modalContainer}>
          <View
            style={{
              width: '100%',
              height: 60,
              display: 'flex',
              alignItems: 'center',
              flexDirection: 'row',
              justifyContent: 'space-between',
              paddingHorizontal: 4,
            }}>
            {children2 ? (
              children2
            ) : (
              <IconButton
                style={{ borderWidth: 0 }}
                mode="outlined"
                disabled={true}
                icon={() => (
                  <X color={'transparent'} size={30}></X>
                )}></IconButton>
            )}
            <Text style={{ fontSize: 24, fontWeight: 'bold', color: theme.colors.primary }}>
              {title}
            </Text>
            <IconButton
              mode="outlined"
              style={{ borderWidth: 0 }}
              onPress={dismissBottomSheet}
              icon={() => <X size={30} color={'black'}></X>}></IconButton>
          </View>
          <Divider />
          {children}
        </BottomSheetView>
      </BottomSheetModal>
    </View>
  );
};

export default BottomSheetWrapper;
