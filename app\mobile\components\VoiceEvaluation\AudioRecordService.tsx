import { Dispatch, SetStateAction, useEffect, useState, useRef } from 'react';
import AudioRecord from 'react-native-audio-record';
import { PermissionsAndroid, Platform } from 'react-native';
import RNFS from 'react-native-fs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import audioSessionManager from '../../services/audioSessionManager';

type RecordingType = 'cs' | 'sv';

// Storage keys
const RECORDING_INDICES_KEY = '@VoiceBack:recordingIndices';
const RECORDING_SESSIONS_KEY = '@VoiceBack:recordingSessions';

// Recording session type
interface RecordingSession {
    id: string;
    timestamp: number;
    csIndex: number;
    svIndex: number;
    csPath: string;
    svPath: string;
}

// Keep a global counter for recording pairs
let globalRecordingIndex = 0;

// Keep track of the current session index
let currentSessionIndex = 0;

// Track if AudioRecord is currently in use
let isAudioRecordInUse = false;

/**
 * Scans the storage directories for existing CS and SV files
 */
const findNextAvailableIndex = async (): Promise<number> => {
    try {
        console.log('[GLOBAL] Scanning for existing recording files');

        // Get the base directories where files are stored
        let mainDir: string;
        let backupDir: string;

        if (Platform.OS === 'android') {
            mainDir = `${RNFS.ExternalStorageDirectoryPath}/Download/VoiceBack/`;
            backupDir = `${RNFS.ExternalDirectoryPath}/`;
        } else {
            mainDir = `${RNFS.DocumentDirectoryPath}/VoiceBack/`;
            backupDir = mainDir;
        }

        // Ensure directories exist
        try {
            await RNFS.mkdir(mainDir);
            if (mainDir !== backupDir) {
                await RNFS.mkdir(backupDir);
            }
        } catch (err) {
            console.error('[GLOBAL] Error creating directories:', err);
        }

        // Read files from both directories
        let mainFiles: RNFS.ReadDirItem[] = [];
        let backupFiles: RNFS.ReadDirItem[] = [];

        try {
            mainFiles = await RNFS.readDir(mainDir);
        } catch (err) {
            console.log('[GLOBAL] Error reading main directory:', err);
        }

        if (mainDir !== backupDir) {
            try {
                backupFiles = await RNFS.readDir(backupDir);
            } catch (err) {
                console.log('[GLOBAL] Error reading backup directory:', err);
            }
        }

        // Combine files from both directories
        const allFiles = [...mainFiles, ...backupFiles];

        const usedIndices = new Set<number>();

        allFiles.forEach(file => {
            const filename = file.name || '';

            const match = filename.match(/^cs(\d+)\.wav$/);
            if (match) {
                const index = parseInt(match[1], 10);
                usedIndices.add(index);
            }
        });

        // Convert to array and sort
        const indicesArray = Array.from(usedIndices).sort((a, b) => a - b);

        // Find the first gap or the next available index
        let nextIndex = 0;
        for (const index of indicesArray) {
            if (index !== nextIndex) {
                // Found a gap
                break;
            }
            nextIndex++;
        }

        console.log(`[GLOBAL] Next available index: ${nextIndex}`);

        // Update the global index if the found index is higher
        if (nextIndex > globalRecordingIndex) {
            globalRecordingIndex = nextIndex;
        }

        return nextIndex;
    } catch (error) {
        console.error('[GLOBAL] Error finding next available index:', error);
        // Fallback to the current global index
        return globalRecordingIndex;
    }
};

/**
 * Saves recording session information to AsyncStorage
 */
const saveRecordingSession = async (session: RecordingSession): Promise<void> => {
    try {
        // Get existing sessions
        const sessionsJson = await AsyncStorage.getItem(RECORDING_SESSIONS_KEY);
        const sessions: RecordingSession[] = sessionsJson ? JSON.parse(sessionsJson) : [];

        // Add new session
        sessions.push(session);

        // Save updated sessions
        await AsyncStorage.setItem(RECORDING_SESSIONS_KEY, JSON.stringify(sessions));

        console.log(`[GLOBAL] Saved recording session with ID: ${session.id}`);
    } catch (error) {
        console.error('[GLOBAL] Error saving recording session:', error);
    }
};

/**
 * Initializes the global recording index from AsyncStorage
 */
const initializeRecordingIndex = async (): Promise<void> => {
    try {
        const storedIndex = await AsyncStorage.getItem(RECORDING_INDICES_KEY);

        if (storedIndex !== null) {
            globalRecordingIndex = parseInt(storedIndex, 10);
            console.log(`[GLOBAL] Initialized global recording index from storage: ${globalRecordingIndex}`);
        } else {
            globalRecordingIndex = await findNextAvailableIndex();
            console.log(`[GLOBAL] Initialized global recording index from file scan: ${globalRecordingIndex}`);
        }
    } catch (error) {
        console.error('[GLOBAL] Error initializing recording index:', error);
        globalRecordingIndex = 0;
    }
};

initializeRecordingIndex().catch(err => {
    console.error('[GLOBAL] Error in initial index initialization:', err);
});

/**
 * Resets the global AudioRecord instance
 */
export const resetAudioRecordGlobal = async (): Promise<void> => {
    try {
        console.log(`[GLOBAL] [${Platform.OS}] Attempting to reset AudioRecord`);

        if (Platform.OS === 'ios') {
            if (isAudioRecordInUse) {
                console.log('[GLOBAL] [iOS] Carefully stopping AudioRecord');
                try {
                    await new Promise(resolve => setTimeout(resolve, 100));

                    const result = await AudioRecord.stop();
                    console.log('[GLOBAL] [iOS] Successfully stopped AudioRecord:', result);
                } catch (iosStopError) {
                    console.log('[GLOBAL] [iOS] Error stopping AudioRecord (expected if not recording):', iosStopError);
                } finally {
                    isAudioRecordInUse = false;
                }
            }

            console.log('[GLOBAL] [iOS] Deactivating audio session');
            await audioSessionManager.deactivate();

            await new Promise(resolve => setTimeout(resolve, 300));

            try {
                AudioRecord.init({
                    sampleRate: 44100,
                    channels: 1,
                    bitsPerSample: 16,
                    audioSource: 1,
                    wavFile: 'ios_temp.wav',
                });
                console.log('[GLOBAL] [iOS] AudioRecord re-initialized with default settings');
            } catch (initError) {
                console.log('[GLOBAL] [iOS] Error re-initializing AudioRecord (non-critical):', initError);
            }

            console.log('[GLOBAL] [iOS] AudioRecord reset complete');
        } else {
            if (isAudioRecordInUse) {
                try {
                    await AudioRecord.stop();
                    console.log('[GLOBAL] [Android] Successfully stopped AudioRecord');
                } catch (e) {
                    console.log('[GLOBAL] [Android] Error stopping AudioRecord (expected if not recording):', e);
                }
            }

            isAudioRecordInUse = false;

            try {
                AudioRecord.init({
                    sampleRate: 44100,
                    channels: 1,
                    bitsPerSample: 16,
                    audioSource: 1,
                    wavFile: 'android_temp.wav',
                });
                console.log('[GLOBAL] [Android] AudioRecord re-initialized with default settings');
            } catch (initError) {
                console.log('[GLOBAL] [Android] Error re-initializing AudioRecord (non-critical):', initError);
            }

            console.log('[GLOBAL] [Android] AudioRecord reset complete');
        }
    } catch (error) {
        console.error(`[GLOBAL] [${Platform.OS}] Error in resetAudioRecordGlobal:`, error);
        isAudioRecordInUse = false;
    }
};

/**
 * Audio recording service for voice evaluation
 */
const AudioRecordService = (
    setFilePath: Dispatch<SetStateAction<string>>,
    setFilePathTmp: Dispatch<SetStateAction<string>>,
    setIsRecordingState: Dispatch<SetStateAction<boolean>>,
    isRecordingState: boolean,
    recordingType: RecordingType = 'cs',
) => {
    // Local state for recording time
    const [, setRecordSecs] = useState<number>(0);
    const [, setRecordTime] = useState<string>('00:00:00');

    // Reference to the timer interval
    const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

    // Track if this instance is recording
    const isRecordingRef = useRef<boolean>(false);

    // Clean up on unmount
    useEffect(() => {
        return () => {
            console.log(`[${recordingType}] [${Platform.OS}] Component unmounting, cleaning up`);

            // Clear the timer if it exists
            if (recordingTimerRef.current) {
                clearInterval(recordingTimerRef.current);
                recordingTimerRef.current = null;
            }

            // If this instance is recording, stop it
            if (isRecordingRef.current) {
                console.log(`[${recordingType}] [${Platform.OS}] Still recording on unmount, stopping`);

                if (Platform.OS === 'ios') {
                    try {
                        console.log(`[${recordingType}] [iOS] Carefully stopping AudioRecord on unmount`);
                        AudioRecord.stop()
                            .then((path) => {
                                console.log(`[${recordingType}] [iOS] Successfully stopped recording on unmount, file at: ${path}`);
                                isAudioRecordInUse = false;
                                isRecordingRef.current = false;
                            })
                            .catch((err) => {
                                console.log(`[${recordingType}] [iOS] Error stopping on unmount:`, err);
                                isAudioRecordInUse = false;
                                isRecordingRef.current = false;
                            });
                    } catch (e) {
                        console.log(`[${recordingType}] [iOS] Exception stopping on unmount:`, e);
                        isAudioRecordInUse = false;
                        isRecordingRef.current = false;
                    }
                } else {
                    // Android handling
                    try {
                        AudioRecord.stop();
                        isAudioRecordInUse = false;
                        isRecordingRef.current = false;
                    } catch (e) {
                        console.log(`[${recordingType}] [Android] Error stopping on unmount:`, e);
                        isAudioRecordInUse = false;
                        isRecordingRef.current = false;
                    }
                }
            }

            if (isAudioRecordInUse) {
                console.log(`[${recordingType}] [${Platform.OS}] AudioRecord still marked as in use on unmount, resetting flag`);
                isAudioRecordInUse = false;
            }
        };
    }, [recordingType]);

    /**
     * Check and request microphone permission
     */
    const checkPermission = async (): Promise<boolean> => {
        if (Platform.OS === 'android') {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
                    {
                        title: 'Microphone Permission',
                        message: 'App needs access to your microphone',
                        buttonNeutral: 'Ask Me Later',
                        buttonNegative: 'Cancel',
                        buttonPositive: 'OK',
                    },
                );
                return granted === PermissionsAndroid.RESULTS.GRANTED;
            } catch (err) {
                console.error(`[${recordingType}] Permission error:`, err);
                return false;
            }
        }
        return true;
    };

    /**
     * Format time for display
     */
    const formatTime = (seconds: number): string => {
        const hrs = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${String(hrs).padStart(2, '0')}:${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    };

    /**
     * Start recording
     */
    const onStartRecord = async (): Promise<void> => {
        console.log(`[${recordingType}] [${Platform.OS}] Starting recording`);

        try {
            // First, make sure AudioRecord is not in use
            if (isAudioRecordInUse) {
                console.log(`[${recordingType}] [${Platform.OS}] AudioRecord is in use, resetting first`);
                await resetAudioRecordGlobal();
            }

            // Configure audio session for iOS
            if (Platform.OS === 'ios') {
                console.log(`[${recordingType}] [iOS] Configuring audio session for recording`);
                await audioSessionManager.configureForRecording();
            }

            // Check for permission
            const hasPermission = await checkPermission();
            if (!hasPermission) {
                console.error(`[${recordingType}] [${Platform.OS}] No microphone permission granted`);
                return;
            }

            // For CS recordings, find the next available index and set the current session index
            // For SV recordings, use the current session index
            if (recordingType === 'cs') {
                // Find the next available index (filling gaps if they exist)
                currentSessionIndex = await findNextAvailableIndex();

                // Update the global index for the next recording session
                globalRecordingIndex = currentSessionIndex + 1;

                // Save the updated index
                try {
                    await AsyncStorage.setItem(RECORDING_INDICES_KEY, globalRecordingIndex.toString());
                    console.log(`[${recordingType}] [${Platform.OS}] Updated global recording index: ${globalRecordingIndex}`);
                } catch (err) {
                    console.error(`[${recordingType}] [${Platform.OS}] Error saving global recording index:`, err);
                }
            }

            const indexToUse = currentSessionIndex;
            console.log(`[${recordingType}] [${Platform.OS}] Using index: ${indexToUse}`);

            // Create the full path to save the file in the device's storage
            let documentsPath;
            let externalPath;

            if (Platform.OS === 'android') {
                documentsPath = `${RNFS.ExternalStorageDirectoryPath}/Download/VoiceBack/`;
                externalPath = `${RNFS.ExternalDirectoryPath}/`;
            } else {
                // On iOS, use the documents directory which is accessible to the app
                documentsPath = `${RNFS.DocumentDirectoryPath}/VoiceBack/`;
                externalPath = documentsPath;
            }

            // Make sure both directories exist
            try {
                // Create the main directory
                await RNFS.mkdir(documentsPath);
                console.log(`[${recordingType}] [${Platform.OS}] Main directory created/exists: ${documentsPath}`);

                // Create the backup directory
                await RNFS.mkdir(externalPath);
                console.log(`[${recordingType}] [${Platform.OS}] Backup directory created/exists: ${externalPath}`);
            } catch (err) {
                console.error(`[${recordingType}] [${Platform.OS}] Error creating directories:`, err);
                // Fallback to internal storage if external fails
                documentsPath = `${RNFS.DocumentDirectoryPath}/`;
                console.log(`[${recordingType}] [${Platform.OS}] Falling back to internal storage: ${documentsPath}`);
                try {
                    await RNFS.mkdir(documentsPath);
                } catch (mkdirError) {
                    console.error(`[${recordingType}] [${Platform.OS}] Error creating fallback directory:`, mkdirError);
                }
            }

            // Use the recordingType parameter to determine the filename
            const filename = `${recordingType}${indexToUse}.wav`;
            const fullPath = `${documentsPath}${filename}`;
            const backupPath = `${externalPath}${filename}`;

            console.log(`[${recordingType}] [${Platform.OS}] Will save recording to: ${fullPath}`);
            console.log(`[${recordingType}] [${Platform.OS}] Backup path: ${backupPath}`);

            // Update state
            setFilePath(fullPath);
            setFilePathTmp(fullPath);

            if (Platform.OS === 'ios') {
                console.log(`[${recordingType}] [iOS] Adding delay before initialization`);
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            console.log(`[${recordingType}] [${Platform.OS}] Initializing AudioRecord with filename: ${filename}`);

            try {
                AudioRecord.init({
                    sampleRate: 44100,
                    channels: 1,
                    bitsPerSample: 16,
                    audioSource: 1,
                    wavFile: filename,
                });
            } catch (initError) {
                console.error(`[${recordingType}] [${Platform.OS}] Error initializing AudioRecord:`, initError);
                throw initError;
            }

            // Mark AudioRecord as in use
            isAudioRecordInUse = true;

            // Update recording state before starting
            setIsRecordingState(true);
            isRecordingRef.current = true;

            if (Platform.OS === 'ios') {
                console.log(`[${recordingType}] [iOS] Carefully starting AudioRecord`);
                try {
                    AudioRecord.start();
                    console.log(`[${recordingType}] [iOS] AudioRecord started successfully`);
                } catch (iosStartError) {
                    console.error(`[${recordingType}] [iOS] Error starting AudioRecord:`, iosStartError);

                    // Clean up on error
                    isAudioRecordInUse = false;
                    isRecordingRef.current = false;
                    setIsRecordingState(false);

                    throw iosStartError;
                }
            } else {
                console.log(`[${recordingType}] [Android] Starting AudioRecord`);
                AudioRecord.start();
            }

            setRecordSecs(0);
            setRecordTime('00:00:00');

            // Use a timer to track recording time
            const startTime = Date.now();
            const timer = setInterval(() => {
                const currentPosition = Date.now() - startTime;
                setRecordSecs(currentPosition);
                setRecordTime(formatTime(Math.floor(currentPosition / 1000)));
            }, 500);

            // Store the timer ID
            recordingTimerRef.current = timer;

            console.log(`[${recordingType}] [${Platform.OS}] Recording started successfully`);
        } catch (error) {
            console.error(`[${recordingType}] [${Platform.OS}] Error in start recording:`, error);

            // Clean up on error
            isAudioRecordInUse = false;
            isRecordingRef.current = false;
            setIsRecordingState(false);

            if (recordingTimerRef.current) {
                clearInterval(recordingTimerRef.current);
                recordingTimerRef.current = null;
            }
        }
    };

    /**
     * Stop recording
     */
    const onStopRecord = async (): Promise<void> => {
        console.log(`[${recordingType}] [${Platform.OS}] Stopping recording, isRecording: ${isRecordingState}`);

        // If we're not recording, just return
        if (!isRecordingState || !isRecordingRef.current) {
            console.log(`[${recordingType}] [${Platform.OS}] Not recording, nothing to stop`);
            return;
        }

        // Update state immediately to prevent multiple stop attempts
        setIsRecordingState(false);
        isRecordingRef.current = false;

        // Clear the timer
        if (recordingTimerRef.current) {
            clearInterval(recordingTimerRef.current);
            recordingTimerRef.current = null;
        }

        // Variable to store the original path
        let originalPath = '';

        try {
            // Only try to stop if AudioRecord is in use
            if (!isAudioRecordInUse) {
                console.log(`[${recordingType}] [${Platform.OS}] AudioRecord not in use, nothing to stop`);
                return;
            }

            if (Platform.OS === 'ios') {
                console.log(`[${recordingType}] [iOS] Carefully stopping AudioRecord`);

                try {
                    // Stop recording and get the path to the recorded file
                    originalPath = await AudioRecord.stop();
                    console.log(`[${recordingType}] [iOS] Recording stopped. Original file saved at: ${originalPath}`);
                } catch (iosStopError) {
                    console.error(`[${recordingType}] [iOS] Error stopping recording:`, iosStopError);
                    isAudioRecordInUse = false;
                    return;
                }
            } else {
                // Android handling
                console.log(`[${recordingType}] [Android] Stopping AudioRecord`);
                originalPath = await AudioRecord.stop();
                console.log(`[${recordingType}] [Android] Recording stopped. Original file saved at: ${originalPath}`);
            }

            // Mark AudioRecord as not in use
            isAudioRecordInUse = false;

            // Deactivate audio session on iOS
            if (Platform.OS === 'ios') {
                console.log(`[${recordingType}] [iOS] Deactivating audio session after recording`);
                await audioSessionManager.deactivate();
            }

            // Ensure the file exists
            const fileExists = await RNFS.exists(originalPath);
            console.log(`[${recordingType}] Original file exists: ${fileExists}`);

            if (fileExists) {
                // Get file info
                const fileInfo = await RNFS.stat(originalPath);
                console.log(`[${recordingType}] Original file size: ${fileInfo.size} bytes`);

                // Extract the filename from the path
                const filename = originalPath.split('/').pop();
                console.log(`[${recordingType}] Extracted filename: ${filename}`);

                // Create paths for external storage
                let externalPath;
                let backupPath;

                if (Platform.OS === 'android') {
                    // On Android, save to Download folder (visible in file explorer)
                    externalPath = `${RNFS.ExternalStorageDirectoryPath}/Download/VoiceBack/${filename}`;
                    // Also save to the app's external directory as a backup
                    backupPath = `${RNFS.ExternalDirectoryPath}/${filename}`;
                } else {
                    // On iOS, use the documents directory
                    externalPath = `${RNFS.DocumentDirectoryPath}/VoiceBack/${filename}`;
                    backupPath = externalPath;
                }

                // Make sure the directories exist
                try {
                    // Create the main directory
                    const mainDir = `${RNFS.ExternalStorageDirectoryPath}/Download/VoiceBack/`;
                    await RNFS.mkdir(mainDir);
                    console.log(`[${recordingType}] Created/verified main directory: ${mainDir}`);

                    // Create the backup directory
                    const backupDir = `${RNFS.ExternalDirectoryPath}/`;
                    await RNFS.mkdir(backupDir);
                    console.log(`[${recordingType}] Created/verified backup directory: ${backupDir}`);
                } catch (err) {
                    console.error(`[${recordingType}] Error creating directories:`, err);
                }

                // Copy the file to external storage asynchronously
                try {
                    console.log(`[${recordingType}] Copying file from ${originalPath} to ${externalPath}`);

                    setFilePath(originalPath);
                    setFilePathTmp(originalPath);

                    const copyPromises = [];

                    // Copy to external storage
                    copyPromises.push(
                        RNFS.copyFile(originalPath, externalPath)
                            .then(() => {
                                console.log(`[${recordingType}] File copied to external storage: ${externalPath}`);
                                // Update paths only after successful copy
                                setFilePath(externalPath);
                                setFilePathTmp(externalPath);

                                // Save recording session information
                                if (recordingType === 'cs') {
                                    // Create a new session ID
                                    const sessionId = `session_${Date.now()}`;

                                    // Create a new recording session
                                    const newSession: RecordingSession = {
                                        id: sessionId,
                                        timestamp: Date.now(),
                                        csIndex: currentSessionIndex,
                                        svIndex: currentSessionIndex,
                                        csPath: externalPath,
                                        svPath: ''
                                    };

                                    // Save the session
                                    saveRecordingSession(newSession).catch(err => {
                                        console.error(`[${recordingType}] [${Platform.OS}] Error saving recording session:`, err);
                                    });
                                } else if (recordingType === 'sv') {
                                    // Get existing sessions
                                    AsyncStorage.getItem(RECORDING_SESSIONS_KEY)
                                        .then(sessionsJson => {
                                            if (sessionsJson) {
                                                const sessions: RecordingSession[] = JSON.parse(sessionsJson);

                                                // Find the session with the current index
                                                const sessionIndex = sessions.findIndex(s => s.svIndex === currentSessionIndex && !s.svPath);

                                                if (sessionIndex !== -1) {
                                                    // Update the session with the SV path
                                                    sessions[sessionIndex].svPath = externalPath;

                                                    // Save the updated sessions
                                                    AsyncStorage.setItem(RECORDING_SESSIONS_KEY, JSON.stringify(sessions))
                                                        .then(() => {
                                                            console.log(`[${recordingType}] [${Platform.OS}] Updated recording session with SV path`);
                                                        })
                                                        .catch(err => {
                                                            console.error(`[${recordingType}] [${Platform.OS}] Error updating recording session:`, err);
                                                        });
                                                }
                                            }
                                        })
                                        .catch(err => {
                                            console.error(`[${recordingType}] [${Platform.OS}] Error getting recording sessions:`, err);
                                        });
                                }
                            })
                            .catch(err => {
                                console.error(`[${recordingType}] Error copying to external storage:`, err);
                            })
                    );

                    // Also copy to backup location if different
                    if (externalPath !== backupPath) {
                        copyPromises.push(
                            RNFS.copyFile(originalPath, backupPath)
                                .then(() => {
                                    console.log(`[${recordingType}] File copied to backup location: ${backupPath}`);
                                })
                                .catch(err => {
                                    console.error(`[${recordingType}] Error copying to backup location:`, err);
                                })
                        );
                    }

                    // Execute all copy operations in parallel
                    Promise.all(copyPromises).catch(err => {
                        console.error(`[${recordingType}] Error in file copy operations:`, err);
                    });

                } catch (copyError) {
                    console.error(`[${recordingType}] Error in copy process:`, copyError);
                    // If copy setup fails, use the original path
                    console.log(`[${recordingType}] Using original path due to copy error: ${originalPath}`);
                    setFilePath(originalPath);
                    setFilePathTmp(originalPath);
                }
            } else {
                console.error(`[${recordingType}] Original file does not exist: ${originalPath}`);
                setFilePath(originalPath);
                setFilePathTmp(originalPath);
            }
        } catch (error) {
            console.error(`[${recordingType}] Error stopping recording:`, error);

            isAudioRecordInUse = false;
        }
    };

    return {
        onStartRecord,
        onStopRecord,
        resetAudioRecord: resetAudioRecordGlobal
    };
};

/**
 * Get all recording sessions
 */
export const getAllRecordingSessions = async (): Promise<RecordingSession[]> => {
    try {
        const sessionsJson = await AsyncStorage.getItem(RECORDING_SESSIONS_KEY);
        if (sessionsJson) {
            const sessions: RecordingSession[] = JSON.parse(sessionsJson);
            return sessions.sort((a, b) => b.timestamp - a.timestamp);
        }
        return [];
    } catch (error) {
        console.error('[GLOBAL] Error getting recording sessions:', error);
        return [];
    }
};

/**
 * Get a recording session by ID
 */
export const getRecordingSessionById = async (id: string): Promise<RecordingSession | null> => {
    try {
        const sessions = await getAllRecordingSessions();
        const session = sessions.find(s => s.id === id);
        return session || null;
    } catch (error) {
        console.error('[GLOBAL] Error getting recording session by ID:', error);
        return null;
    }
};

/**
 * Get a recording session by index
 */
export const getRecordingSessionByIndex = async (index: number): Promise<RecordingSession | null> => {
    try {
        const sessions = await getAllRecordingSessions();
        const session = sessions.find(s => s.csIndex === index || s.svIndex === index);
        return session || null;
    } catch (error) {
        console.error('[GLOBAL] Error getting recording session by index:', error);
        return null;
    }
};

/**
 * Check if a file exists and is accessible
 */
export const checkFileExists = async (filePath: string): Promise<boolean> => {
    try {
        if (!filePath) return false;

        const exists = await RNFS.exists(filePath);
        if (exists) {
            // Also check if we can access the file (get its stats)
            try {
                const stats = await RNFS.stat(filePath);
                return stats.isFile() && stats.size > 0;
            } catch (statError) {
                console.error(`[GLOBAL] Error getting file stats for ${filePath}:`, statError);
                return false;
            }
        }
        return false;
    } catch (error) {
        console.error(`[GLOBAL] Error checking if file exists: ${filePath}`, error);
        return false;
    }
};

export default AudioRecordService;