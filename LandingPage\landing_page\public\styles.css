/* styles.css */
@import
url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');


html {
  scroll-behavior: smooth;
}

body {
  font-family: "Poppins", sans-serif;
  overflow-x: hidden;
}

.slick-dots {
  bottom: -50px !important;
}
.slick-dots li button:before {
  font-size: 20px !important;
}

*{font-family: "Poppins", sans-serif;}

.mainContainer {
  width: 100vw;
  height: 100vh;
  display: inline;
  margin: 0;
}

.section1 {
  height: 100%;
  justify-content: center;
  align-items: center;
}

.header-parent-container {
  background: linear-gradient(45deg, white, #DCFFE8);
  height: 700px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-container {
  display: flex;
  position: relative;
  height: 700px;
  justify-content: space-between;
}

.header-content-custom {
  width: 215px;
  height: 53px;
  background-color: #DCFFE8;
  border-radius: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 200px;
  margin-left: 30px;
}

.header-label{
  font-family: "Poppins", sans-serif;
  color: #397954;
  font-weight: 900;
  text-align: center;
  margin: 0;
}

.img-container {
  width: 40%;
  height: 80%;
  margin-top: 80px;
  margin-right: 10%;
}

.backgroundImg {
  width: 100%;
  height: 100%;
  object-fit: contain;
  margin-right: 30px;
  position: relative;
}

.heart-icon {
  width: 70px;
  height: 70px;
  margin-left: 30px;
  position: absolute;
  right: 10%;
  top: 15%;
}

.slogan {
  width: 547px;
  height: 188px;
  margin-left: 30px;
}



.slogan-content {
  color: #575757;
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-size: 72px;
}

.navMenuContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  display: flex;
  transition: top 0.3s;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.dropdown {
  display: none;
}

.logo {
  height: 60px;

}

.navMenuList {
  display: flex;
  gap: 40px;
  font-size: 20px;
  font-weight: bold;
  list-style: none;
  margin-left: -710px;
}

.navMenuList a {
  font-family: "Poppins", sans-serif;
  text-decoration: none;
  color: #575757;
}

.navMenuLink:hover {
  text-decoration-line: none;
  color: #397954;
}

.navMenuLink.active {
    color: #397954;
    font-weight: bolder;
}

.feedback {
  font-family: "Poppins", sans-serif;
  width: 290px;
  height: fit-content;
  background-color: #FFFFFF;
  border-radius: 50px;
  border: 1px solid #D6F4DD;
  position: relative;
  bottom: 150px;
  padding: 8px;
  display: flex;
  align-items: center;
  left: 60px;
}

.smile-face {
  height: 48px;
  object-fit: contain;
}

.section2 {
  height: 100%;
  justify-content: center;
  align-items: center;
  padding-right: 30px;
  padding-left: 30px;
  padding-top: 100px;
}

.section2Content {
  height: fit-content;
  display: flex;
  gap: 20px;
}

.section2Title {
  font-family: "Poppins", sans-serif;
  font-size: 48px;
  min-width: 540px;
  font-weight: bolder;
  flex:1;
}

.content {
  flex:4;
  margin-top: 10px;
  margin-left: 50px;
  color: #828282;
  height: fit-content;
}

.cardContainer {
  display: flex;
  justify-content: center;
  padding: 20px;
  gap: 20px;
}

.card-custom {
  width: 270px;
  border-radius: 40px;
  background-color: white;
  margin: 20px;
  box-shadow: 2px 5px 100px #D6F4DD;

}


.cardHeader {
  padding: 20px;
}

.icon {
  width: 116px;
  height: 116px;
  font-size: 40px;
}

.cardContent {
  margin-top: 50px;
  margin-bottom: 50px;
  padding: 20px;
  color: #575757;
}

.cardContent-title {
  font-family: arial;
  font-weight: 900;
}

}

.cardTitle {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
}

.section3 {
  position: relative;
  height: 100%;
}

.section3Title {
  font-size: 50px;
  font-weight: bolder;
  margin-left: 20px;
}


.carousel-container {
  display:flex;
  align-items: center;
}

.carousel-container .d-flex {
  flex:0.5;

}
.carousel-item img {
  height: calc(100vh - 130px);
  object-fit: contain;
}

.carousel .carousel-indicators li {
  background-color: gray;
  bottom: -100px;
}

.carousel-indicators li.active {
  background-color: #397954;
}

.carousel-control-next,
.carousel-control-prev {
    filter: invert(100%);

}

.carousel-content {
    color: #585858;

  display: flex;
  flex:0.5;
  justify-content: center;
  height: fit-content;
  margin-top: 10px;
}

.carousel-content p {
  font-size: 20px;
  text-align: center;
  width: 70%;
}

.download {
  text-align: center;
  padding: 30px;
  background-color: #C1F3CF;
  flex: 1;
  height: fit-content;
  border-radius: 30px;
  margin-left: 15%;
  margin-top: 5%;
  width: 70%;
  display:none;
}

.qr-code {
  width: 30%;
  height: 30%;
  margin-top: 20px;
}

.button-container {
  display: flex;
  justify-content: flex-end;
  height: 50px;
}

.button-container .btn {
  margin-left: 10px;
  margin-right: 10px;
}

.footer {
  margin-top:70px;
  padding-top:70px !important;
  padding-bottom:70px !important;
  color: #333;
  padding: 20px;
  background-color: #498D58 ;
}

.footer-label{
  font-weight: 900;
  color:white;
}

.footer-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
}

.footer-section {
  text-align: left;
  flex:1;

}

.footer-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.footer-company {
  color: white;
}

.footer-heading {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
}

.footer-contact {
  font-size: 0.875rem;
  color: white;
}

.icon {
  font-size: 1.2rem;
  margin-right: 8px;
}


.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

.justify-between {
  justify-content: space-between;
}

.bg-white {
  background-color: #fff;
}

.text-gray-800 {
  color: #333;
}

.p-5 {
  padding: 20px;
}

.md\:px-20 {
  padding-left: 80px;
  padding-right: 80px;
}

.md\:ml-20 {
  margin-left: 80px;
}

.ml-10 {
  margin-left: 40px;
}

.md\:mb-10 {
  margin-bottom: 40px;
}

.mb-10 {
  margin-bottom: 40px;
}

.md\:mx-10 {
  margin-left: 40px;
  margin-right: 40px;
}

.font-bold {
  font-weight: bold;
}

.text-lg {
  font-size: 1.125rem;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-green-500 {
  color: #4caf50;
}

.text-gray-600 {
  color: #575757;
}

.space-y-4 > * + * {
  margin-top: 16px;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.lg\:ml-2 {
  margin-left: 8px;
}

.lg\:mr-3 {
  margin-right: 12px;
}

.mr-2 {
  margin-right: 8px;
}

.text-sm {
  font-size: 0.875rem;
}

@media only screen and (max-width: 1024px) {
  .slogan {
    width: 360px;
    height: 100px;
  }
  .slogan p {
    font-size: 50px;
  }
  .navMenuContainer {
    display: flex;
    justify-content: space-between;
    align-items: right;
  }
  .logo {
    height: 50px;
  }
  .navMenu {
    display: flex;
  }
  .navMenuList {

    flex: 1;
    margin-top: 20px;
    gap: 20px;
    font-size: 16px;
    justify-content: right;
    margin-top: 20px;
    gap: 20px;
    font-size: 16px;
  }


  .cardContainer {
    gap: 10px;
  }
  .cardContent {
    margin-top: 20px;
  }
  .content {
    text-align: left;
    height:fit-content;
    margin-left: 0px;
  }
  .section2Title {
    font-family: "Poppins", sans-serif;
    font-size: 40px;
    min-width: 450px;
    font-weight: bolder;
    flex:1;
  }


  .cardContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .section3 {
    height: fit-content;
  }
  .section3Title {
    font-size: 30px;
  }
  .carouselContainer {
    margin-top: 20px;
  }
  .carousel-control-prev,
  .carousel-control-next {
    bottom: 50% !important;
  }
  .footer-container {
    flex-direction: column;
    text-align: left;
  }
  .footer-section {
    text-align: left;
  }
}

@media only screen and (max-width: 576px){
  .navMenuList {
    display: none;
  }

  .slogan {
    width: 100%;
  }

  .header-container {
    display: flex;
    position: relative;
    height: 600px;
    width:100%;
    justify-content: space-between;
  }

  .dropbtn {
    background-color: #04AA6D;
    color: white;
    padding: 16px;
    font-size: 16px;
    border-radius: 10px;
    border: none;
  }

  .dropdown {
    position: relative;
    display: inline-block;
    border-radius: 20px;
  }

  .dropdown-content {
    display: none;
    position: absolute;
    width: 100vw;
    top: 60px;
    right: -20px;
    background-color: #f1f1f1;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
  }

  .dropdown-content a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
  }
  .dropdown-content a:hover {
    background-color: #ddd;
    color: black;
    text-decoration: none;
  }

  .dropdown:hover .dropdown-content {
    display: block;
  }

  .dropdown:hover .dropbtn {
    background-color: #3e8e41;
  }

  .backgroundImg {
    display: none;
  }



  .section2Title {
    font-family: "Poppins", sans-serif;
    min-width: 100%;
    font-size: 30px;
    font-weight: bolder;
    flex:1;
  }


  .section2Content {
    flex-direction: column;
  }

  .content {
    margin-left: 0;
    text-align: justify;
  }

  .feedback {
    position: unset;
    margin-bottom: 50px;
  }

  .text-center h1 {
    font-size: 20px;
  }

  .carousel-container {
    flex-direction:column;
  }

  .carousel-item img {
    height: calc(100vh - 130px);
    object-fit: contain;
  }

  .footer {
    margin-top:50px;
  }
  .footer-container {
    margin: 0;
  }

  .footer-section {
    text-align: left;
  }


}






