import subprocess
import requests
import os
import re
import shutil
import tarfile
from packaging.version import parse as parse_version

# Configs
GITHUB_API = "https://api.github.com/repos/praat/praat/releases/latest"
DOWNLOAD_DIR = os.path.expanduser("~/")
TOOLS_DIR = os.path.expanduser("~/Documents/voiceback/app/backend/tools")
DEST_PATH = "/usr/local/bin/praat"

def get_local_praat_version():
    try:
        result = subprocess.run(['praat', '--version'], capture_output=True, text=True)
        match = re.search(r'(\d+\.\d+\.\d+)', result.stdout)
        return match.group(1) if match else None
    except FileNotFoundError:
        return None

def get_latest_github_version():
    res = requests.get(GITHUB_API)
    if res.status_code != 200:
        return None, None
    data = res.json()
    version = data["tag_name"].lstrip("v")
    return version, version.replace('.', '')

def get_architecture():
    result = subprocess.run(['uname', '-m'], capture_output=True, text=True)
    arch = result.stdout.strip()
    if arch == "x86_64":
        return "linux-intel64"
    elif arch in ("aarch64", "arm64"):
        return "linux-arm64"
    else:
        print(f"Unsupported architecture: {arch}")
        return None

def download_latest_praat(version_string, arch_suffix):
    filename = f"praat{version_string}_{arch_suffix}.tar.gz"
    url = f"https://fon.hum.uva.nl/praat/{filename}"
    dest_path = os.path.join(DOWNLOAD_DIR, filename)

    print(f"Downloading: {url}")
    result = subprocess.run(['curl', '-kLO', url], cwd=DOWNLOAD_DIR)
    if result.returncode != 0:
        print("Download failed.")
        return None
    return dest_path

def extract_and_install(tar_path):
    print("Extracting...")
    with tarfile.open(tar_path, "r:gz") as tar:
        tar.extractall(path=DOWNLOAD_DIR)

    praat_binary = os.path.join(DOWNLOAD_DIR, "praat")
    if not os.path.exists(praat_binary):
        print("praat binary not found after extraction.")
        return False

    os.chmod(praat_binary, 0o755)

    print("Copying to /usr/local/bin...")
    subprocess.run(["sudo", "cp", praat_binary, DEST_PATH])

    print(f"Copying to tools directory: {TOOLS_DIR}")
    os.makedirs(TOOLS_DIR, exist_ok=True)
    shutil.copy(praat_binary, TOOLS_DIR)

    return True

def main():
    arch_suffix = get_architecture()
    if not arch_suffix:
        return

    local_version = get_local_praat_version()
    latest_version, version_string = get_latest_github_version()

    if not latest_version:
        print("Failed to retrieve latest version.")
        return

    print(f"Local Praat version:  {local_version or 'Not installed'}")
    print(f"Latest Praat version: {latest_version}")

    if not local_version or parse_version(local_version) < parse_version(latest_version):
        print("Update available. Starting download...")
        tar_file = download_latest_praat(version_string, arch_suffix)
        if tar_file and extract_and_install(tar_file):
            print("Praat successfully updated.")
        else:
            print("Update failed.")
    else:
        print("Praat is already up to date.")

if __name__ == "__main__":
    main()