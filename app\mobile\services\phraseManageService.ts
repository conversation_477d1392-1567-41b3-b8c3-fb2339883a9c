import {Dispatch, SetStateAction} from 'react';
import {openDatabase, SQLiteDatabase} from 'react-native-sqlite-storage';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type Phrase = {
  phrase_id: number;
  text: string;
  isLiked: boolean;
  useCount: number;
  lastUsed: string;
  languageCode: string;
};

type PhraseAction = {
  setPhrases: Dispatch<SetStateAction<Phrase[]>>;
};

export type PhraseService = Phrase & PhraseAction;

export const openPhrasesDatabase = () => {
  return openDatabase({
    name: 'phrases.db',
    location: 'default',
  });
};

export const sortingPhrases = (phrases: Phrase[], sortOption: string) => {
  if (sortOption === 'liked') {
    return [...phrases].sort((a, b) => {
      if (a.isLiked === b.isLiked) {
        return 0;
      }
      return a.isLiked ? -1 : 1;
    });
  }

  if (sortOption === 'most') {
    return [...phrases].sort((a, b) => b.useCount - a.useCount);
  }

  if (sortOption === 'recent') {
    return [...phrases].sort((a, b) => {
      return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
    });
  }
  return phrases;
};

export const updatePhraseTable = async (db: SQLiteDatabase) => {
  // Update table to accomadate new field languageCode
  const [table] = await db.executeSql(`PRAGMA table_info(Phrases);`);

  const columnExists = table.rows
    .raw()
    .some((row: any) => row.name === 'languageCode');

  if (!columnExists) {
    await db.executeSql(`ALTER TABLE Phrases ADD COLUMN languageCode TEXT;`);
    await db.executeSql(
      `UPDATE Phrases SET languageCode = ? WHERE languageCode IS NULL;`,
      ['en-US'],
    );
    console.log('Added language column');
  }
};

export const fetchPhrases = async (
  db: SQLiteDatabase,
  setPhrases: Dispatch<SetStateAction<Phrase[]>>,
  sortOption: string,
) => {
  try {
    // Fetch phrases
    const results = await db.executeSql('SELECT * FROM Phrases;');
    const rows = results[0].rows;
    let phrases: Phrase[] = [];

    for (let i = 0; i < rows.length; i++) {
      phrases.push(rows.item(i));
    }
    phrases = [...phrases].reverse();

    if (sortOption) {
      if (sortOption.trim() !== '') {
        phrases = sortingPhrases(phrases, sortOption);
      }
    }

    setPhrases(phrases);
  } catch (error) {
    console.error('Error fetching phrases:', error);
  }
};

export const insertPhrase = async (
  db: SQLiteDatabase,
  text: string,
  setPhrases?: Dispatch<SetStateAction<Phrase[]>>,
  sortOption?: string,
  languageCode?: string,
) => {
  try {
    const language = await AsyncStorage.getItem('@language');

    const [existingPhrase] = await db.executeSql(
      'SELECT * FROM Phrases WHERE text = ?;',
      [text],
    );
    if (existingPhrase.rows.length > 0) {
      return;
    }

    await db.executeSql(
      'INSERT INTO Phrases (text, isLiked, useCount, lastUsed, languageCode) VALUES (?, ?, ?, ?, ?);',
      [text, 0, 0, '', languageCode || 'en-US'],
    );

    if (sortOption && setPhrases) {
      await fetchPhrases(db, setPhrases, sortOption);
    }
  } catch (error) {
    console.error('Error inserting phrase:', error);
  }
};

// export const createNewPhraseTable = async (db: SQLiteDatabase) => {
//   try {
//     const results = await db.executeSql('SELECT * FROM Phrases;');
//     const rows = results[0].rows;
//     let phrases: Phrase[] = [];

//     for (let i = 0; i < rows.length; i++) {
//       phrases.push(rows.item(i));
//     }
//     await db.executeSql(
//       `CREATE TABLE IF NOT EXISTS new_phrases (
//         phrase_id INTEGER PRIMARY KEY AUTOINCREMENT,
//         text TEXT,
//         isLiked BOOLEAN DEFAULT 0,
//         useCount INTEGER DEFAULT 0,
//         lastUsed TEXT,
//         languageCode TEXT
//       );`,
//     );

//     phrases.map(async phrase => {
//       await db.executeSql(
//         'INSERT INTO new_phrases (text, isLiked, useCount, lastUsed, languageCode) VALUES (?, ?, ?, ?, ?);',
//         [
//           phrase.text,
//           phrase.isLiked,
//           phrase.useCount,
//           phrase.lastUsed,
//           phrase.languageCode,
//         ],
//       );
//     });

//     await db.executeSql('DROP TABLE Phrases;');
//     await db.executeSql('ALTER TABLE new_phrases RENAME TO Phrases;');
//   } catch (error) {
//     console.error('Error creating new table:', error);
//   }
// };

export const createPhraseTable = async (db: SQLiteDatabase) => {
  try {
    await db.executeSql(
      `CREATE TABLE IF NOT EXISTS Phrases (
        phrase_id INTEGER PRIMARY KEY AUTOINCREMENT,
        text TEXT,
        isLiked BOOLEAN DEFAULT 0,
        useCount INTEGER DEFAULT 0,
        lastUsed TEXT,
        languageCode TEXT
      );`,
    );
    console.log('Phrase table created successfully');
  } catch (error) {
    console.error('Error creating table:', error);
  }
};

export const deletePhrase = async (
  db: SQLiteDatabase,
  id: number,
  setPhrases: Dispatch<SetStateAction<Phrase[]>>,
  sortOption: string,
) => {
  try {
    await db.executeSql('DELETE FROM Phrases WHERE phrase_id = ?;', [id]);

    await fetchPhrases(db, setPhrases, sortOption);
    db.close();
  } catch (error) {
    console.error('Error deleting phrase:', error);
  }
};

export const updatePhraseIsLiked = async (
  db: SQLiteDatabase,
  phrase: Phrase,
  isLiked: boolean,
  setPhrases: Dispatch<SetStateAction<Phrase[]>>,
  sortOption: string,
) => {
  try {
    await db.executeSql('UPDATE Phrases SET isLiked = ? WHERE phrase_id = ?;', [
      isLiked ? 1 : 0,
      phrase.phrase_id,
    ]);

    await fetchPhrases(db, setPhrases, sortOption);
  } catch (error) {
    console.error('Error updating phrase:', error);
  }
};

export const updatePhraseUseCount = async (
  db: SQLiteDatabase,
  phrase: Phrase,
  useCount: number,
  setPhrases: Dispatch<SetStateAction<Phrase[]>>,
  sortOption: string,
) => {
  try {
    const date = new Date().toISOString();
    await db.executeSql(
      'UPDATE Phrases SET useCount = ?, lastUsed = ? WHERE phrase_id = ?;',
      [useCount, date, phrase.phrase_id],
    );
    await fetchPhrases(db, setPhrases, sortOption);
  } catch (error) {
    console.error('Error updating phrase:', error);
  }
};

export const updatePhraseLanguage = async (
  db: SQLiteDatabase,
  phrase: Phrase,
  languageCode: string,
  setPhrases: Dispatch<SetStateAction<Phrase[]>>,
  sortOption: string,
) => {
  try {
    const date = new Date().toISOString();
    await db.executeSql(
      'UPDATE Phrases SET languageCode = ? WHERE phrase_id = ?;',
      [languageCode, phrase.phrase_id],
    );
    await fetchPhrases(db, setPhrases, sortOption);
  } catch (error) {
    console.error('Error updating phrase:', error);
  }
};
