// useBottomSheet.js
import {useCallback, useEffect, useMemo} from 'react';
import {useSharedValue} from 'react-native-reanimated';

const useBottomSheet = (bottomSheetModalRef: React.RefObject<any>) => {
  const snapPoints = useMemo(() => ['25%', '94%'], []);

  const reduceMotion = useSharedValue(false);

  // const derivedConfig = useDerivedValue(() => {
  //   return {reduceMotion: reduceMotion.value};
  // });

  // const updateReduceMotion = () => {
  //   reduceMotion.value = true; // Update shared value
  // };

  // const setReduceMotion = (index: number) => {
  //   reduceMotion.value = index === 0; // example logic for setting reduceMotion
  // };

  useEffect(() => {
    reduceMotion.value = false;
  }, []);

  const showBottomSheet = useCallback(() => {
    bottomSheetModalRef?.current?.present();
  }, [bottomSheetModalRef]);

  const dismissBottomSheet = useCallback(() => {
    bottomSheetModalRef?.current?.dismiss();
  }, [bottomSheetModalRef]);

  const handleSheetChanges = useCallback((index: number) => {
    'worklet';
    // const newReduceMotion = {...reduceMotion.value};
    // newReduceMotion.value = index;
    // reduceMotion.value = newReduceMotion;
    // runOnJS(setReduceMotion)(index);

    return index;
  }, []);

  return {
    snapPoints,
    showBottomSheet,
    dismissBottomSheet,
    handleSheetChanges,
  };
};

export default useBottomSheet;
