import React, {useState} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {useTheme} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {TopNav} from '../../components/TopNav/TopNav';
import StepperComp from '../../components/VoiceEvaluation/Stepper/StepperComp';
import LanguageSelection from '../../components/LanguageSelection/LanguageSelection';
import {BottomSheetModal} from '@gorhom/bottom-sheet';
import {createRef} from 'react';

const RecordAudioScreen = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const [index, setIndex] = useState(1);

  // Create a dummy ref to pass to StepperComp
  const bottomSheetModalRef = createRef<BottomSheetModal>();

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <TopNav title="Record audio" backFunction={handleBack}>
        <View style={styles.languageContainer}>
          <LanguageSelection />
        </View>
      </TopNav>

      {/* Content - Using the exact same StepperComp from the modal */}
      <View style={styles.content}>
        <StepperComp
          isActive={true}
          index={index}
          bottomSheetModalRef={bottomSheetModalRef}
          isScreen={true} // Add this prop to indicate it's a screen
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  languageContainer: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
});

export default RecordAudioScreen;
