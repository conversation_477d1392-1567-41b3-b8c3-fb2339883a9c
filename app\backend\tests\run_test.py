import sys
import time
import requests
import base64
import os
import re
from dotenv import load_dotenv
import subprocess

load_dotenv()

API_BASE = os.getenv("API_BASE", "http://server:8000")
AVQI_TXT = "/outputs/praat/avqi.txt"
ABI_TXT = "/outputs/praat/abi.txt"
GENERATED_IMAGES = ["abi_plot.png", "avqi_plot.png", "spectrogram.png", "waveform.png"]

VALUE_LIMITS = {
    "AVQI": (0.0, 10.0),
    "ABI": (0.0, 10.0),
    "CPPS": (0.0, 30.0),
    "Jitter": (0.0, 5.0),
    "GNE": (0.0, 1.0),
    "HFNO": (0.0, 1.0),
    "HNR": (0.0, 40.0),
    "HNRD": (0.0, 40.0),
    "H1H2": (-5.0, 20.0),
    "Shimmer (%)": (0.0, 10.0),
    "Shimmer (dB)": (0.0, 2.0),
    "Period SD": (0.0, 0.005),
    "Slope": (-60.0, 0.0),
    "Tilt": (-20.0, 5.0),
}


def wait_for_api(url, timeout=30):
    print("────────────────────────────────────────────")
    print("Waiting for FastAPI to be ready...")
    print("────────────────────────────────────────────")

    start = time.time()
    while True:
        try:
            r = requests.get(url)
            if r.status_code < 500:
                print("✔ FastAPI is up!")
                break
        except requests.exceptions.ConnectionError:
            pass
        if time.time() - start > timeout:
            print("\n============================================")
            print("✖ FASTAPI STARTUP TIMEOUT")
            print("============================================\n")
            exit(1)
        time.sleep(2)


def get_access_token():
    client_id = os.getenv("CLIENT_ID")
    client_secret = os.getenv("CLIENT_SECRET")
    assert client_id and client_secret, "✖ Missing CLIENT_ID or CLIENT_SECRET"

    basic_token = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode(
        "utf-8"
    )
    headers = {"Authorization": f"Basic {basic_token}"}
    response = requests.post(f"{API_BASE}/api/auth/token", headers=headers)
    assert (
        response.status_code == 200
    ), f"✖ Auth failed: {response.status_code}, {response.text}"
    return response.json()["access_token"]


def analyze_audio_files(token, file_cs_path, file_sv_path):
    headers = {"Authorization": f"Bearer {token}"}
    files = [("files", open(file_cs_path, "rb")), ("files", open(file_sv_path, "rb"))]
    response = requests.post(f"{API_BASE}/api/analyze", headers=headers, files=files)
    assert (
        response.status_code == 202
    ), f"✖ Analyze failed: {response.status_code}, {response.text}"
    data = response.json()
    assert "tasks" in data and isinstance(data["tasks"], list), "✖ No task IDs returned"
    return data["tasks"]


def extract_value_from_txt(file_path, label):
    assert os.path.exists(file_path), f"✖ Missing output file: {file_path}"
    with open(file_path, "r") as f:
        content = f.read()
    match = re.search(rf"{label}[:\s]*([\d\.\-]+)", content, re.IGNORECASE)
    assert match, f"✖ {label} value not found in {file_path}"
    value = float(match.group(1))
    validate_range(label, value)
    return value


def validate_range(label, value):
    if label in VALUE_LIMITS:
        min_val, max_val = VALUE_LIMITS[label]
        assert (
            min_val <= value <= max_val
        ), f"✖ {label} value {value} is out of expected range ({min_val}-{max_val})"


def check_praat_version():
    print("────────────────────────────────────────────")
    print("Checking Praat version...")
    print("────────────────────────────────────────────")
    try:
        output = subprocess.check_output(["praat", "--version"], text=True)
        print(f"✔ Praat version: {output.strip()}\n")
    except FileNotFoundError:
        print("✖ Praat is not installed in the container.\n")
    except subprocess.CalledProcessError as e:
        print(f"✖ Error checking Praat version: {e}\n")


def wait_for_file(path, timeout=60):
    print(f"Waiting for file: {path}")
    for i in range(timeout):
        if os.path.exists(path):
            print(f"✔ File found.\n")
            return True
        time.sleep(1)
    raise TimeoutError(f"✖ Timeout waiting for {path}\n")


def wait_for_folder(path, timeout=60):
    print(f"Waiting for folder: {path}")
    for i in range(timeout):
        if os.path.exists(path) and os.path.isdir(path):
            print(f"✔ Folder found.\n")
            return True
        time.sleep(1)
    raise TimeoutError(f"✖ Timeout waiting for folder: {path}\n")


def check_task_images(task_id, timeout=60):
    task_output_dir = f"/outputs/tasks/{task_id}"

    wait_for_folder(task_output_dir, timeout)

    for filename in GENERATED_IMAGES:
        img_path = os.path.join(task_output_dir, filename)
        print(f"Waiting for image: {img_path}")
        for i in range(timeout):
            if os.path.exists(img_path):
                print(f"✔ Image found.\n")
                break
            time.sleep(1)
        else:
            raise TimeoutError(f"✖ Timeout waiting for image: {img_path}")


if __name__ == "__main__":
    try:
        wait_for_api(f"{API_BASE}/docs")

        print("\n============================================")
        print("TEST STARTED")
        print("============================================\n\n")

        check_praat_version()

        print("────────────────────────────────────────────")
        print("Getting access token...")
        print("────────────────────────────────────────────")
        token = get_access_token()
        print("✔ Token retrieved successfully.\n")

        print("────────────────────────────────────────────")
        print("Uploading audio files for analysis...")
        print("────────────────────────────────────────────")
        file_cs = "tests/assets/cs_sample.wav"
        file_sv = "tests/assets/sv_sample.wav"

        assert os.path.exists(file_cs), f"Missing test file: {file_cs}"
        assert os.path.exists(file_sv), f"Missing test file: {file_sv}"

        start_time = time.time()

        tasks = analyze_audio_files(token, file_cs, file_sv)
        print("✔ Audio files uploaded. Tasks created: ")
        for t in tasks:
            print(f"Task ID: {t['task_id']}")
            print(f"CS: {t['file_cs']}")
            print(f"SV: {t['file_sv']}")
        print()

        print("────────────────────────────────────────────")
        print("Waiting for output files to be generated...")
        print("────────────────────────────────────────────")
        wait_for_file(AVQI_TXT)
        wait_for_file(ABI_TXT)

        print("────────────────────────────────────────────")
        print("Checking AVQI and ABI values...")
        print("────────────────────────────────────────────")
        avqi = extract_value_from_txt(AVQI_TXT, "AVQI")
        abi = extract_value_from_txt(ABI_TXT, "ABI")

        print(f"✔ AVQI: {avqi}")
        print(f"✔ ABI:  {abi}\n")

        print("────────────────────────────────────────────")
        print("Checking generated images...")
        print("────────────────────────────────────────────")
        for t in tasks:
            task_id = t["task_id"]
            print(f"Checking images for task: {task_id}\n")
            check_task_images(task_id)

        end_time = time.time()
        duration = end_time - start_time

        print("\n============================================")
        print("✔ ALL TESTS PASSED")
        print(f"Total processing duration: {duration:.2f} seconds")
        print("============================================\n")

    except AssertionError as e:
        print("\n============================================")
        print("✖ TEST FAILED")
        print("============================================")
        print(f"{e}\n")
        sys.exit(1)

    except TimeoutError as e:
        print("\n============================================")
        print("✖ FILES WAIT TIMEOUT")
        print("============================================")
        print(f"{e}\n")
        sys.exit(1)

    else:
        sys.exit(0)
