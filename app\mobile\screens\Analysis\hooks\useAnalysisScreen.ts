import React, {useEffect, useState} from 'react';
import {
  fetchGraph_3m,
  Graph,
  LessonCount,
  openGraphDatabase,
} from '../../../services/graphManageService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useLessonNavigation} from '../../../utils/useAppNavigation';
import {useTheme} from 'react-native-paper';

export const useAnalysisScreen = () => {
  const theme = useTheme();
  const navigation = useLessonNavigation();
  const [isSelected, setIsSelected] = useState('');
  const [visible, setVisible] = React.useState(false);
  const showModal = () => setVisible(true);
  const hideModal = () => setVisible(false);
  const [selectedData, setSelectedData] = useState<Graph[]>([]);
  const [barData, setBarData] = useState<Graph[]>([]);
  const [barData30, setBarData30] = useState<Graph[]>([]);
  const [barData90, setBarData90] = useState<Graph[]>([]);
  const barColor = [
    '#002614',
    '#115C38',
    '#2B7A54',
    '#3E8A6D',
    '#4F9A86',
    '#5FAA9F',
    '#6FBAB8',
    '#7FCAD1',
    '#8FD9EA',
    '#9FE9F9',
  ];

  const {graph, lessons, setGraph} = useSorting();
  useEffect(() => {
    async () => {
      const db = await openGraphDatabase();
      await fetchGraph_3m(db, setGraph);
    };

    (async () => {
      const now = new Date();
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(now.getDate() - 7);

      const monthAgo = new Date();
      monthAgo.setMonth(now.getMonth() - 1);

      // Filter the graphs list to get entries from 7 days ago till now
      const sevenDaysGraph = graph.filter(graph => {
        const graphDate = new Date(graph.date);
        return graphDate >= sevenDaysAgo && graphDate <= now;
      });

      // Filter the graphs list to get entries from 1 month ago till now
      const monthGraph = graph.filter(graph => {
        const graphDate = new Date(graph.date);
        return graphDate >= monthAgo && graphDate <= now;
      });

      setSelectedData(sevenDaysGraph);
      setBarData(sevenDaysGraph);
      setBarData30(monthGraph);
      setBarData90(graph);
      setIsSelected('7 days');
    })();
  }, [graph]);

  const calculateCompleteTimeEachLesson = (graphData: Graph[]) => {
    let lessonTimes: LessonCount[] = [];

    graphData.forEach(dataItem => {
      dataItem.complete.forEach(lesson => {
        // Check if the lesson already exists in the array
        const existingLesson = lessonTimes.find(lt => lt.id === lesson.id);
        if (existingLesson) {
          // If it exists, update the count
          existingLesson.count += lesson.count;
        } else {
          // If it doesn't exist, add a new entry
          lessonTimes.push({
            id: lesson.id,
            name: lesson.name,
            count: lesson.count,
          });
        }
      });
    });
    return lessonTimes;
  };

  const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const getWeekDays = (stringDate: Date) => {
    const dayString = weekdays[stringDate.getDay()];
    return dayString;
  };

  const getDayMonth = (stringDate: Date) => {
    const month = stringDate.getMonth() + 1;
    const day = stringDate.getDate();
    return `${day}/${month}`;
  };

  const groupGraphDataByWeek = (graph: Graph[]) => {
    if (graph.length === 0) return [];
    let count = 0;
    let temp: Graph[] = [];
    let tempGraphData: Graph[] = [];

    for (let i = 0; i < graph.length; i++) {
      temp.push(graph[i]);
      count++;

      // Group data every 7 days or at the end of the array
      if (count === 7 || i === graph.length - 1) {
        const tempGraph: Graph = {
          id: i,
          complete: calculateCompleteTimeEachLesson(temp),
          date: graph[i].date,
        };
        tempGraphData.push(tempGraph);
        temp = []; // Reset temp array for the next group
        count = 0; // Reset count for the next group
      }
    }
    return tempGraphData;
  };

  const barData7 = selectedData.map((item, index) => ({
    stacks:
      isSelected === '7 days' || isSelected === '1 month'
        ? item.complete.map((lesson, index) => {
            index = lesson.id - 1;
            const color = barColor[index];
            // console.log(`Lesson ID: ${lesson.id}, Color: ${color}`);
            return {
              value: lesson.count,
              color: color,
            };
          })
        : item.complete.map((lesson, index) => {
            index = lesson.id - 1;
            const color = barColor[index];
            // console.log(`Lesson ID: ${lesson.id}, Color: ${color}`);
            return {
              value: lesson.count,
              color: color,
            };
          }),
    label:
      isSelected === '7 days'
        ? getWeekDays(item.date)
        : isSelected === '1 month' && index % 7 === 0
        ? getDayMonth(item.date)
        : isSelected === '3 months' && index % 2 === 0
        ? getDayMonth(item.date)
        : '',
  }));

  const handleClick = (click: string) => {
    setIsSelected(click);
    setSelectedData(
      click === '7 days'
        ? barData
        : click === '1 month'
        ? barData30
        : click === '3 months'
        ? groupGraphDataByWeek(graph)
        : [],
    );
  };

  const totalActivities = Math.round(
    selectedData.reduce((total, current) => {
      const sumOfCurrent = current.complete.reduce(
        (sum, stack) => sum + stack.count,
        0,
      );
      return total + sumOfCurrent;
    }, 0),
  );

  const completeTimeEachLesson = calculateCompleteTimeEachLesson(selectedData);

  return {
    lessons,
    barColor,
    navigation,
    handleClick,
    isSelected,
    theme,
    visible,
    hideModal,
    showModal,
    totalActivities,
    completeTimeEachLesson,
    barData7,
  };
};
