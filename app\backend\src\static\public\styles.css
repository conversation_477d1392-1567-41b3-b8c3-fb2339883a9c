/* styles.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');

html {
    scroll-behavior: smooth;
}

body {
    font-family: "Poppins", sans-serif;
    overflow-x: hidden;
}

.mainContainer {
    width: 100vw;
    height: 100vh;
    margin: 0;
}

.section1 {
    height: 100%;
    justify-content: center;
    align-items: center;
}

.header-parent-container {
    background: linear-gradient(45deg, white, #DCFFE8);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.header-container {
    display: flex;
    position: relative;
    height: 100%;
    justify-content: space-between;
}

.header-content-custom {
    width: 187px;
    height: 53px;
    background-color: #DCFFE8;
    border-radius: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 200px;
    margin-left: 30px;
}

.img-container {
    width: 40%;
    height: 80%;
    margin-top: 80px;
    margin-right: 10%;
}

.backgroundImg {
    width: 100%;
    height: 100%;
    object-fit: contain;
    margin-right: 30px;
    position: relative;
}

.heart-icon {
    width: 70px;
    height: 70px;
    margin-left: 30px;
    position: absolute;
    right: 10%;
    top: 15%;
}

.slogan {
    width: 547px;
    height: 188px;
    margin-left: 30px;
}

.slogan-content {
    color: #575757;
    font-weight: 900;
    font-size: 80px;
}

.navMenuContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 10;
    display: flex;
    transition: top 0.3s;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
}

.dropdown {
    display: none;
}

.logo {
    height: 60px;
    width: 80px;
}

.navMenuList {
    display: flex;
    gap: 40px;
    font-size: 20px;
    font-weight: bold;
    list-style: none;
    margin-left: -710px;
}

.navMenuList a {
    text-decoration: none;
    color: #575757;
}

.navMenuLink:hover {
    text-decoration-line: none;
    color: #397954;
}

.navMenuLink.active {
    color: #397954;
    font-weight: bolder;
}

.feedback {
    width: 290px;
    height: fit-content;
    background-color: #FFFFFF;
    border-radius: 50px;
    border: 1px solid #D6F4DD;
    position: absolute;
    bottom: 20%;
    padding: 8px;
    display: flex;
    align-items: center;
    left: 45%;
}

.smile-face {
    height: 48px;
    object-fit: contain;
}

.section2 {
    height: 100%;
    justify-content: center;
    align-items: center;
    padding-top: 100px;
}

.section2Content {
    height: fit-content;
    display: flex;
    gap: 20px;
}

.section2Title {
    font-size: 50px;
    font-weight: bolder;
    margin-left: 20px;
    width: 70%;
}

.content {
    margin-top: 10px;
    margin-left: 200px;
    color: #828282;
    height: 150px;
}

.cardContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    gap: 20px;
}

.card-custom {
    width: 270px;
    height: fit-content;
    border-radius: 40px;
    border: 1px solid #D6F4DD;
}

.card2 {
    width: 270px;
    height: fit-content;
    border-color: #FFFFFF;
    border-radius: 40px;
    box-shadow: 0px 60px 100px -30px rgba(12, 151, 0, 0.2);
}

.cardHeader {
    padding: 20px;
}

.icon {
    width: 80px;
    height: 80px;
}

.cardContent {
    margin-top: 50px;
    padding: 20px;
    color: #575757;
}

.cardTitle {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 10px;
}

.section3 {
    position: relative;
    height: 100%;
}

.section3Title {
    font-size: 50px;
    font-weight: bolder;
    margin-left: 20px;
}

.carousel-item img {
    width: 310px;
    object-fit: contain;
}

.carousel .carousel-indicators li {
    background-color: gray;
    bottom: -100px;
}

.carousel-indicators li.active {
    background-color: #397954;
}

.carousel-control-next,
.carousel-control-prev {
    filter: invert(100%);

}

.carousel-content {
    display: flex;
    justify-content: center;
    width: 100%;
    height: fit-content;
    margin-top: 10px;
}

.carousel-content p {
    font-size: 20px;
    text-align: center;
    width: 70%;
}

.download {
    text-align: center;
    padding: 30px;
    background-color: #C1F3CF;
    flex: 1;
    height: fit-content;
    border-radius: 30px;
    margin-left: 15%;
    margin-top: 5%;
    width: 70%;
}

.qr-code {
    width: 30%;
    height: 30%;
    margin-top: 20px;
}

.button-container {
    display: flex;
    justify-content: flex-end;
    height: 50px;
}

.button-container .btn {
    margin-left: 10px;
    margin-right: 10px;
}

.footer {
    color: #333;
    padding: 20px;
}

.footer-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 0 170px;
}

.footer-section {
    text-align: center;
}

.footer-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4caf50;
}

.footer-company {
    color: #575757;
}

.footer-heading {
    font-size: 1.25rem;
    font-weight: bold;
    color: #4caf50;
}

.footer-contact {
    font-size: 0.875rem;
    color: #575757;
}

.icon {
    font-size: 1.2rem;
    margin-right: 8px;
}


.flex {
    display: flex;
}

.flex-row {
    flex-direction: row;
}

.flex-1 {
    flex: 1;
}

.justify-between {
    justify-content: space-between;
}

.bg-white {
    background-color: #fff;
}

.text-gray-800 {
    color: #333;
}

.p-5 {
    padding: 20px;
}

.md\:px-20 {
    padding-left: 80px;
    padding-right: 80px;
}

.md\:ml-20 {
    margin-left: 80px;
}

.ml-10 {
    margin-left: 40px;
}

.md\:mb-10 {
    margin-bottom: 40px;
}

.mb-10 {
    margin-bottom: 40px;
}

.md\:mx-10 {
    margin-left: 40px;
    margin-right: 40px;
}

.font-bold {
    font-weight: bold;
}

.text-lg {
    font-size: 1.125rem;
}

.mb-4 {
    margin-bottom: 16px;
}

.text-green-500 {
    color: #4caf50;
}

.text-gray-600 {
    color: #575757;
}

.space-y-4>*+* {
    margin-top: 16px;
}

.flex-row {
    flex-direction: row;
}

.items-center {
    align-items: center;
}

.lg\:ml-2 {
    margin-left: 8px;
}

.lg\:mr-3 {
    margin-right: 12px;
}

.mr-2 {
    margin-right: 8px;
}

.text-sm {
    font-size: 0.875rem;
}

@media only screen and (max-width: 1024px) {
    .slogan {
        width: 360px;
        height: 100px;
    }

    .slogan p {
        font-size: 50px;
    }

    .navMenuContainer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .logo {
        margin-left: 20px;
        width: 50px;
        height: 50px;
    }

    .navMenuList {
        margin-top: 20px;
        gap: 20px;
        font-size: 16px;
    }

    .section2 {
        padding-left: 10px;
        height: fit-content;
        padding-top: 100px;
    }

    .cardContainer {
        gap: 10px;
    }

    .cardContent {
        margin-top: 20px;
    }

    .content {
        text-align: left;
        margin-left: 50px;
    }

    .section2Title {
        font-size: 30px;
        margin-left: 0;
    }

    .cardContainer {
        display: flex;
        flex-direction: column;
    }

    .section3 {
        height: fit-content;
        padding-bottom: 100px;
    }

    .section3Title {
        font-size: 30px;
    }

    .carouselContainer {
        margin-top: 20px;
    }

    .carousel-control-prev,
    .carousel-control-next {
        bottom: 50% !important;
    }

    .footer-container {
        flex-direction: column;
        text-align: left;
    }

    .footer-section {
        text-align: left;
    }
}

@media only screen and (max-width: 500px) {
    .navMenuList {
        display: none;
    }

    .dropbtn {
        background-color: #04AA6D;
        color: white;
        padding: 16px;
        font-size: 16px;
        border-radius: 10px;
        border: none;
    }

    .dropdown {
        position: relative;
        display: inline-block;
        border-radius: 20px;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        background-color: #f1f1f1;
        min-width: 160px;
        box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
        z-index: 1;
    }

    .dropdown-content a {
        color: black;
        padding: 12px 16px;
        text-decoration: none;
        display: block;
    }

    .dropdown-content a:hover {
        background-color: #ddd;
    }

    .dropdown:hover .dropdown-content {
        display: block;
    }

    .dropdown:hover .dropbtn {
        background-color: #3e8e41;
    }

    .backgroundImg {
        display: none;
    }

    .section2Content {
        flex-direction: column;
    }

    .content {
        margin-left: 0;
        text-align: justify;
        padding: 10px;
    }

    .feedback {
        position: unset;
        margin-bottom: 200px;
    }

    .text-center h1 {
        font-size: 20px;
    }

    .footer-container {
        margin: 0;
    }

    .footer-section {
        text-align: left;
    }


}