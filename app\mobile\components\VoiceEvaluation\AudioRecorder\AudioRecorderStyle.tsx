import { StyleSheet } from 'nativewind';
import { useTheme } from 'react-native-paper';

const useAudioRecorderStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    container: {
      paddingHorizontal: 25,
      display: 'flex',
      justifyContent: 'space-between',
      flexDirection: 'row',
      borderRadius: 50,
      backgroundColor: theme.colors.primary,
      height: 60,
    },
    textContainer: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    fileName: {
      color: 'white',
      fontSize: 20,
      textAlign: 'center',
    },
    staticWaveformView: {
      flex: 1,
      height: 60,
    },
  });
};

export default useAudioRecorderStyle;