import { Dispatch, SetStateAction } from 'react';
import {
    createExerciseTable,
    createNewExerciseTable,
    Exercise,
    insertEx,
    openExerciseDatabase,
} from '../services/exerciseManageService';
import {
    createLessonTable,
    createNewLessonTable,
    insertLesson,
    Lesson,
    openLessonDatabase,
} from '../services/lessonManageService';
import {
    createGraphTable,
    createGraphTemp,
    fetchGraph_3m,
    Graph,
    closeDatabase,
    openGraphDatabase,
    openGraphTemp,
    LessonCount,
} from '../services/graphManageService';
import { openPhrasesDatabase, Phrase } from '../services/phraseManageService';
import { DB_VERSION } from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
    createPhraseTable,
    fetchPhrases,
    insertPhrase,
} from '../services/phraseManageService';

const initialPhrasesDatabase = async () => {
    const db = await openPhrasesDatabase();
    await createPhraseTable(db);
    initialPhrases.map(async data => {
        await insertPhrase(db, data.text);
    });
};

const initialLessonsDatabase = async () => {
    const db = await openLessonDatabase();
    await createLessonTable(db);
    initialLessons.map(async data => {
        await insertLesson(db, data.name);
    });
};

const initialExercisesDatabase = async () => {
    const db = await openExerciseDatabase();
    await createExerciseTable(db);
    initialExercises.map(async data => {
        await insertEx(db, data.lesson_id, data.name, data.content);
    });
};

const initialGraphDatabase = async () => {
    const db = await openGraphDatabase();
    const dbTemp = await openGraphTemp();
    await createGraphTable(db);
    await createGraphTemp(dbTemp);
    // await fetchGraph_3m(db, setGraph);
    await closeDatabase(db);
};

type PhraseDatabase = {
    text: string;
};

export const initialData = async () => {
    const isInserted = await AsyncStorage.getItem('@isInserted');

    if (isInserted === null || isInserted === 'false') {
        await initialPhrasesDatabase();
        await initialExercisesDatabase();
        await initialLessonsDatabase();
        await initialGraphDatabase();
        await AsyncStorage.setItem('@isInserted', 'true');
    }
};

// CREATE TABLE OF PHRASES
const initialPhrases: PhraseDatabase[] = [
    { text: 'Hello, how are you?' },
    { text: 'I am fine, thank you.' },
    { text: 'What is your name?' },
    { text: 'My name is John.' },
];

// CREATE TABLE OF LESSONS
export type Lesson_temp = {
    name: string;
};

const initialLessons: Lesson_temp[] = [
    { name: 'Lesson 1' },
    { name: 'Lesson 2' },
    { name: 'Lesson 3' },
    { name: 'Lesson 4' },
];

// CREATE TABLE OF EXERCISES
export type Exercise_temp = {
    lesson_id: number;
    name: string;
    content: string[];
};

const initialExercises: Exercise_temp[] = [
    {
        lesson_id: 1,
        name: 'E1',
        content: ['A', 'O', 'U', 'O', 'A'],
    },
    {
        lesson_id: 1,
        name: 'E2',
        content: ['U', 'O', 'A', 'O', 'U'],
    },
    {
        lesson_id: 1,
        name: 'E3',
        content: ['A', 'E', 'I', 'E', 'A'],
    },
    {
        lesson_id: 1,
        name: 'E4',
        content: ['I', 'E', 'A', 'E', 'I'],
    },
    {
        lesson_id: 1,
        name: 'E5',
        content: ['U', 'E', 'A', 'E', 'U'],
    },
    {
        lesson_id: 1,
        name: 'E6',
        content: ['O', 'E', 'I', 'E', 'O'],
    },
    {
        lesson_id: 1,
        name: 'E7',
        content: ['A', 'E', 'I', 'O', 'U'],
    },
    {
        lesson_id: 1,
        name: 'E8',
        content: ['U', 'O', 'I', 'E', 'A'],
    },
    {
        lesson_id: 2,
        name: 'E1',
        content: ['KA', 'KO', 'KU', 'KO', 'KA'],
    },
    {
        lesson_id: 2,
        name: 'E2',
        content: ['KU', 'KO', 'KA', 'KO', 'KU'],
    },
    {
        lesson_id: 2,
        name: 'E3',
        content: ['KA', 'KE', 'KI', 'KE', 'KA'],
    },
    {
        lesson_id: 2,
        name: 'E4',
        content: ['KI', 'KE', 'KA', 'KE', 'KI'],
    },
    {
        lesson_id: 2,
        name: 'E5',
        content: ['KU', 'KE', 'KA', 'KE', 'KU'],
    },
    {
        lesson_id: 2,
        name: 'E6',
        content: ['KO', 'KE', 'KI', 'KE', 'KO'],
    },
    {
        lesson_id: 2,
        name: 'E7',
        content: ['KA', 'KE', 'KI', 'KO', 'KU'],
    },
    {
        lesson_id: 2,
        name: 'E8',
        content: ['KU', 'KO', 'KI', 'KE', 'KA'],
    },
    {
        lesson_id: 3,
        name: 'E1',
        content: ['TA', 'TO', 'TU', 'TO', 'TA'],
    },
    {
        lesson_id: 3,
        name: 'E2',
        content: ['TU', 'TO', 'TA', 'TO', 'TU'],
    },
    {
        lesson_id: 3,
        name: 'E3',
        content: ['TA', 'TE', 'TI', 'TE', 'TA'],
    },
    {
        lesson_id: 3,
        name: 'E4',
        content: ['TI', 'TE', 'TA', 'TE', 'TI'],
    },
    {
        lesson_id: 3,
        name: 'E5',
        content: ['TU', 'TE', 'TA', 'TE', 'TU'],
    },
    {
        lesson_id: 3,
        name: 'E6',
        content: ['TO', 'TE', 'TI', 'TE', 'TO'],
    },
    {
        lesson_id: 3,
        name: 'E7',
        content: ['TA', 'TE', 'TI', 'TO', 'TU'],
    },
    {
        lesson_id: 3,
        name: 'E8',
        content: ['TU', 'TO', 'TI', 'TE', 'TA'],
    },
    {
        lesson_id: 4,
        name: 'E1',
        content: ['PA', 'PO', 'PU', 'PO', 'PA'],
    },
    {
        lesson_id: 4,
        name: 'E2',
        content: ['PU', 'PO', 'PA', 'PO', 'PU'],
    },
    {
        lesson_id: 4,
        name: 'E3',
        content: ['PA', 'PE', 'PI', 'PE', 'PA'],
    },
    {
        lesson_id: 4,
        name: 'E4',
        content: ['PI', 'PE', 'PA', 'PE', 'PI'],
    },
    {
        lesson_id: 4,
        name: 'E5',
        content: ['PU', 'PE', 'PA', 'PE', 'PU'],
    },
    {
        lesson_id: 4,
        name: 'E6',
        content: ['PO', 'PE', 'PI', 'PE', 'PO'],
    },
    {
        lesson_id: 4,
        name: 'E7',
        content: ['PA', 'PE', 'PI', 'PO', 'PU'],
    },
    {
        lesson_id: 4,
        name: 'E8',
        content: ['PU', 'PO', 'PI', 'PE', 'PA'],
    },
];

export { openLessonDatabase };
