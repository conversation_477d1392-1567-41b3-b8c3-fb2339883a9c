import {<PERSON><PERSON>hart3, <PERSON><PERSON>ide<PERSON>, RotateCcw} from 'lucide-react-native';
import React from 'react';
import {Image, ScrollView, StyleSheet, Text, View} from 'react-native';
import {Button, IconButton, Modal, Portal} from 'react-native-paper';
import LessonCard from './components/LessonCard';
import {TopNav} from '../../components/TopNav/TopNav';
import AddTemplate from './components/AddTemplate';
import {useLessonPage} from './hooks/useLessonPage';
import {LessonPageStyle} from './styles/LessonPageStyle';
import {syncExercisesFromServer} from '../../services/exerciseManageService';

const LessonPage = () => {
  const {
    showGraph,
    visible,
    hideModal,
    currentLesson,
    deleteL,
    mergedLessons,
    confirmDeleteLesson,
    deleteLessonNoConfirm,
    selectedLesson,
    selectedExercise,
    turnOffPlayAllLesson,
    turnOnPlayAllLesson,
    setExercises,
    lastSync,
    patientId,
    theme,
  } = useLessonPage();

  const styles = LessonPageStyle();

  return (
    <View style={styles.safeViewStyle}>
      <TopNav title="Voice Recover Session">
        <IconButton
          icon={() => (
            <Image
              source={require('../../assets/topNavIcons/lessonsChart.png')}
              style={{width: 24, height: 24}}
            />
          )}
          onPress={() => showGraph()}
        />

        <IconButton
          icon={() => <RotateCcw size={24} color="#FFFFFF" />}
          onPress={async () => {
            console.log('Syncing Exercises...');
            await syncExercisesFromServer(patientId, lastSync, 1, setExercises);
            console.log('Sync Complete');
          }}
        />
      </TopNav>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={hideModal}
          contentContainerStyle={styles.modalContainer}>
          <Text style={styles.modalTitle}>Delete "{currentLesson.name}"?</Text>
          <Text style={styles.modalMessage}>This action cannot be undone.</Text>
          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              onPress={() => hideModal()}
              contentStyle={styles.deleteButton}>
              <Text style={[styles.textStyle, {color: '#4c4c4c'}]}>Cancel</Text>
            </Button>
            <Button
              mode="contained"
              onPress={() => deleteL()}
              contentStyle={styles.deleteButton}>
              <Text style={[styles.textStyle, {color: 'white'}]}>Confirm</Text>
            </Button>
          </View>
        </Modal>
      </Portal>

      <ScrollView contentContainerStyle={styles.scrollViewContainer}>
        {mergedLessons.map(lesson => (
          <LessonCard
            key={lesson.id + lesson.name}
            text={lesson.name}
            count={lesson.lessonCount}
            target={lesson.target}
            buttonOne={() => deleteLessonNoConfirm(lesson)}
            buttonTwo={() => selectedLesson(lesson)}
            buttonThree={() => {
              turnOffPlayAllLesson();
              selectedExercise(lesson);
            }}
          />
        ))}
        <View style={styles.sectionDivider}></View>
        <View style={styles.addButton}>
          <AddTemplate />
        </View>
      </ScrollView>

      <View style={styles.playAllButton}>
        <IconButton
          style={{borderWidth: 0}}
          mode="contained-tonal"
          icon={() => <ListVideo size={30} color="white" />}
          size={40}
          containerColor={theme.colors.primary}
          onPress={() => {
            turnOnPlayAllLesson();
            selectedExercise(mergedLessons[0]);
          }}
        />
      </View>
    </View>
  );
};

export default LessonPage;
