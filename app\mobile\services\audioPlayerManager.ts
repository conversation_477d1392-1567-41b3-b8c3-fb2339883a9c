import { Platform } from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';

// Simple event listener type
type Listener = (data: { id: string, isPlaying: boolean }) => void;

// Create a singleton audio player manager
class AudioPlayerManager {
  private static instance: AudioPlayerManager;
  private audioPlayer: AudioRecorderPlayer;
  private currentlyPlayingId: string | null = null;
  private listeners: Listener[] = [];

  private constructor() {
    this.audioPlayer = new AudioRecorderPlayer();
  }

  public static getInstance(): AudioPlayerManager {
    if (!AudioPlayerManager.instance) {
      AudioPlayerManager.instance = new AudioPlayerManager();
    }
    return AudioPlayerManager.instance;
  }

  // Play audio and notify listeners
  public async playAudio(filePath: string, id: string): Promise<void> {
    try {
      if (!filePath) {
        console.error('AudioPlayerManager: File path is empty or undefined');
        return;
      }

      if (this.currentlyPlayingId) {
        await this.stopAudio();
      }

      if (Platform.OS === 'ios') {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      try {
        await this.audioPlayer.startPlayer(filePath);
        this.audioPlayer.setVolume(1.0);
        this.currentlyPlayingId = id;

        this.notifyListeners({ id, isPlaying: true });

        this.audioPlayer.addPlayBackListener((e: any) => {
          if (e.current_position === e.duration) {
            this.stopAudio();
            this.audioPlayer.removePlayBackListener();
          }
        });
      } catch (playError) {
        console.error(`AudioPlayerManager: Error starting player: ${playError}`);
        this.currentlyPlayingId = null;

        this.notifyListeners({ id, isPlaying: false });
      }
    } catch (error) {
      console.error(`AudioPlayerManager: Unexpected error in playAudio: ${error}`);
      this.currentlyPlayingId = null;
    }
  }

  public async stopAudio(): Promise<void> {
    try {
      if (this.currentlyPlayingId) {
        const stoppedId = this.currentlyPlayingId;

        try {
          await this.audioPlayer.stopPlayer();
        } catch (stopError) {
          console.error(`AudioPlayerManager: Error stopping player: ${stopError}`);
        }

        try {
          this.audioPlayer.removePlayBackListener();
        } catch (listenerError) {
          console.error(`AudioPlayerManager: Error removing listener: ${listenerError}`);
        }

        this.currentlyPlayingId = null;
        this.notifyListeners({ id: stoppedId, isPlaying: false });
      }
    } catch (error) {
      console.error(`AudioPlayerManager: Unexpected error in stopAudio: ${error}`);
      this.currentlyPlayingId = null;
    }
  }

  public isPlaying(id: string): boolean {
    return this.currentlyPlayingId === id;
  }

  private notifyListeners(data: { id: string, isPlaying: boolean }): void {
    this.listeners.forEach(listener => listener(data));
  }

  public onPlayStateChanged(callback: Listener): () => void {
    this.listeners.push(callback);

    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  public async cleanup(): Promise<void> {
    await this.stopAudio();
    this.listeners = [];
  }
}

export default AudioPlayerManager.getInstance();
