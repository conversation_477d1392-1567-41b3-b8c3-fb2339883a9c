import {NativeEventEmitter, NativeModules, Platform} from 'react-native';
import Voice from '@react-native-voice/voice';
import {BehaviorSubject} from 'rxjs';

export interface AudioMetrics {
  volume: number;
  pitch: number;
  timestamp: number;
  performance?: {
    averageUpdateInterval: number;
    updateCount: number;
  };
}

export interface AudioState {
  isRecording: boolean;
  error: string | null;
  metrics: AudioMetrics | null;
}

interface AudioProcessorConfig {
  updateInterval?: number;
  bufferSize?: number;
  smoothingFactor?: number;
  minDb?: number;
  maxDb?: number;
}

const INITIAL_STATE: AudioState = {
  isRecording: false,
  error: null,
  metrics: null,
};

class AudioProcessor {
  private state$ = new BehaviorSubject<AudioState>(INITIAL_STATE);
  private volumeBuffer: number[];
  private bufferIndex: number = 0;
  private config: Required<AudioProcessorConfig>;
  private lastVolume = 0;
  private isInitialized = false;
  private lastUpdateTime = 0;
  private performanceMetrics = {
    updateCount: 0,
    lastPerformanceCheck: Date.now(),
    averageUpdateInterval: 0,
  };

  constructor(config: AudioProcessorConfig = {}) {
    this.config = {
      updateInterval: config.updateInterval ?? 50,
      bufferSize: config.bufferSize ?? 5,
      smoothingFactor: config.smoothingFactor ?? 0.4,
      minDb: config.minDb ?? 30, // Whisper level
      maxDb: config.maxDb ?? 90, // Loud voice level
    };
    this.volumeBuffer = new Array(this.config.bufferSize).fill(0);
    this.setupVoiceListeners();
  }

  public init(callback: (state: AudioState) => void) {
    if (this.isInitialized) return;
    const subscription = this.state$.subscribe(callback);
    this.isInitialized = true;
    return subscription;
  }

  private setupVoiceListeners() {
    Voice.onSpeechVolumeChanged = (e: any) => {
      try {
        if (!this.state$.value.isRecording) return;

        const now = Date.now();
        if (now - this.lastUpdateTime < this.config.updateInterval) {
          return;
        }
        this.lastUpdateTime = now;

        const rawVolume = this.calculateDb(e.value || 0);
        const smoothedVolume = this.smoothVolume(rawVolume);
        const estimatedPitch = this.estimatePitch(smoothedVolume);

        this.updateMetrics({
          volume: smoothedVolume,
          pitch: estimatedPitch,
          timestamp: now,
          performance: {
            averageUpdateInterval:
              this.performanceMetrics.averageUpdateInterval,
            updateCount: this.performanceMetrics.updateCount,
          },
        });

        this.performanceMetrics.updateCount++;
        if (now - this.performanceMetrics.lastPerformanceCheck > 1000) {
          this.performanceMetrics.averageUpdateInterval =
            (now - this.performanceMetrics.lastPerformanceCheck) /
            this.performanceMetrics.updateCount;
          this.performanceMetrics.updateCount = 0;
          this.performanceMetrics.lastPerformanceCheck = now;
        }
      } catch (error) {
        console.error('Error processing audio:', error);
        this.updateState({error: 'Error processing audio'});
      }
    };

    Voice.onSpeechStart = () => {
      this.updateState({isRecording: true, error: null});
    };

    Voice.onSpeechEnd = () => {
      this.updateState({isRecording: false});
    };

    Voice.onSpeechError = (error: any) => {
      const errorMessage = this.getErrorMessage(error);
      this.updateState({
        isRecording: false,
        error: errorMessage,
      });
    };
  }

  private getErrorMessage(error: any): string {
    const errorCode = error?.error?.code;
    switch (errorCode) {
      case '7':
        return 'No speech detected';
      case '5':
        return 'Recognition timeout';
      case '9':
        return 'Voice recognition busy';
      default:
        return 'Error during voice recognition';
    }
  }

  private calculateDb(amplitude: number): number {
    if (amplitude < 0.01) return this.config.minDb;

    // Convert normalized amplitude to dB SPL
    // Assuming device microphone sensitivity and calibration
    const db = 20 * Math.log10(Math.max(0.0001, Math.abs(amplitude)));
    const calibratedDb = db + 60; // Calibration offset to approximate real SPL
    return Math.min(
      this.config.maxDb,
      Math.max(this.config.minDb, calibratedDb),
    );
  }

  private smoothVolume(currentVolume: number): number {
    this.lastVolume =
      this.config.smoothingFactor * currentVolume +
      (1 - this.config.smoothingFactor) * this.lastVolume;

    this.volumeBuffer[this.bufferIndex] = this.lastVolume;
    this.bufferIndex = (this.bufferIndex + 1) % this.config.bufferSize;

    const average =
      this.volumeBuffer.reduce((a, b) => a + b, 0) / this.config.bufferSize;
    return Math.round(average * 10) / 10;
  }

  private estimatePitch(volume: number): number {
    // Base frequency around typical speaking range
    const baseFrequency = 165; // Starting at female voice range
    const volumeFactor =
      (volume - this.config.minDb) / (this.config.maxDb - this.config.minDb);

    // Modulate around the base frequency with natural variations
    const variation = Math.sin(Date.now() / 500) * 15;
    const pitch = baseFrequency + volumeFactor * 100 + variation;

    // Keep within typical human voice range
    return Math.round(Math.min(400, Math.max(50, pitch)));
  }

  private updateMetrics(metrics: AudioMetrics) {
    this.updateState({metrics});
  }

  private updateState(partial: Partial<AudioState>) {
    this.state$.next({
      ...this.state$.value,
      ...partial,
    });
  }

  public async start() {
    try {
      this.cleanup();
      await Voice.start('en-US');
      this.updateState({isRecording: true, error: null});
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.updateState({
        isRecording: false,
        error: 'Failed to start recording',
      });
    }
  }

  public async stop() {
    try {
      await Voice.stop();
      this.updateState({
        isRecording: false,
        metrics: null,
      });
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.updateState({
        isRecording: false,
        error: 'Failed to stop recording',
      });
    }
  }

  public getCurrentState(): AudioState {
    return this.state$.value;
  }

  public cleanup() {
    this.volumeBuffer = new Array(this.config.bufferSize).fill(0);
    this.bufferIndex = 0;
    this.lastVolume = 0;
    this.lastUpdateTime = 0;
    this.performanceMetrics = {
      updateCount: 0,
      lastPerformanceCheck: Date.now(),
      averageUpdateInterval: 0,
    };
  }

  public destroy() {
    if (this.isInitialized) {
      Voice.destroy().then(Voice.removeAllListeners);
      this.state$.complete();
      this.isInitialized = false;
    }
  }
}

export const audioProcessor = new AudioProcessor();
