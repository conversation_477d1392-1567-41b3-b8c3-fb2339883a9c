worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;   
    default_type  application/octet-stream;

    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=static_cache:10m inactive=60m;

    upstream backend {
        server server:8000;
    }

    server {
        listen 80;
        server_name localhost; 

        client_max_body_size 100M;

        root /app/backend/src/static/public; 
        index index.html;

        # Proxy API requests to backend
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Serve landing page normally
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Serve static assets (images, css, js)
        location /assets/ {
            alias /app/backend/src/static/public/assets/;
        }
    }
}
