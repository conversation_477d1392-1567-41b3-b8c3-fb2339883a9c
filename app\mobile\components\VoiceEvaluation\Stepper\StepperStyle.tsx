import { StyleSheet } from 'nativewind';
import { useTheme } from 'react-native-paper';

const useStepperStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    container: {
      height: '94%',
      width: '100%',
      position: 'relative',
      display: 'flex',

      paddingVertical: 12,
    },

    stepContent: {
      display: 'flex',
      height: 500,
      paddingVertical: 12,
      alignItems: 'center',
      justifyContent: 'flex-start',
    },

    buttonContainer: {
      height: 60,
      flexDirection: 'row',
      paddingHorizontal: 24,
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: 10,
      overflow: 'hidden',
      marginTop: 15,
    },

    buttonContentStyle: {
      height: 54,
      width: '100%',
      justifyContent: 'center',
      padding: 0,
    },

    textModalContainer: {
      height: '27%',
      width: '100%',
      position: 'absolute',
      flexDirection: 'column',
      gap: 10,
      backgroundColor: 'rgba(0, 0, 0, 1)',
      paddingTop: 20,
      top: 82,
    },

    textStyle: {
      fontSize: 20,
      color: 'white',
      fontWeight: 'bold',
      textAlign: 'center',
      textShadowColor: 'black',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 1,
    },

    // instructIcon: {
    //   transform: [
    //     {
    //       scale: 0.5,
    //     },
    //     {translateY: 10},
    //   ],
    // },
  });
};

export const useStepIndicatorStyles = (theme: any) => ({
  stepIndicatorSize: 25,
  currentStepIndicatorSize: 35,
  separatorStrokeWidth: 1,
  separatorStrokeUnfinishedWidth: 1,
  separatorStrokeFinishedWidth: 2,
  currentStepStrokeWidth: 3,
  stepStrokeCurrentColor: theme.colors.primary,
  stepStrokeWidth: 2,
  stepStrokeFinishedColor: theme.colors.primary,
  stepStrokeUnFinishedColor: '#aaaaaa',
  separatorFinishedColor: theme.colors.primary,
  separatorUnFinishedColor: '#aaaaaa',
  stepIndicatorFinishedColor: theme.colors.primary,
  stepIndicatorUnFinishedColor: '#ffffff',
  stepIndicatorCurrentColor: '#ffffff',
  stepIndicatorLabelFontSize: 14,
  currentStepIndicatorLabelFontSize: 16,
  stepIndicatorLabelCurrentColor: theme.colors.primary,
  stepIndicatorLabelFinishedColor: '#ffffff',
  stepIndicatorLabelUnFinishedColor: '#aaaaaa',
  labelColor: '#999999',
  labelSize: 14,
  currentStepLabelColor: theme.colors.primary,
});

export default useStepperStyle;