worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;   
    default_type  application/octet-stream;

    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=static_cache:10m inactive=60m;

    # upstream frontend {
    #     server frontend:3000;
    # }

    upstream backend {
        server server:8000;
    }

    # Server for HTTP (port 80)
    server {
        listen 80;
        server_name voiceback.nextwaytech.vn;

        client_max_body_size 100M;

        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }

        # Redirect all other HTTP traffic to HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    # Server for HTTPS (port 443)
    server {
        listen 443 ssl;
        server_name voiceback.nextwaytech.vn;

        ssl_certificate /etc/letsencrypt/live/voiceback.nextwaytech.vn/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/voiceback.nextwaytech.vn/privkey.pem;
        include /etc/letsencrypt/options-ssl-nginx.conf;
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

        client_max_body_size 100M;

        root /app/backend/src/static/public;   

        index index.html;       

        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /assets/ {
            alias /app/backend/src/static/public/assets/;
        }

        #
        # location / {
        #     proxy_pass http://frontend;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        # }

        
    }
}
