import Tts from 'react-native-tts';
import {Platform} from 'react-native';
import {VolumeManager} from 'react-native-volume-manager';

let oldVolume: number = 0;

const setVolume = async (volume: number) => {
  await VolumeManager.setVolume(volume);
};

const getVolume = async () => {
  const volumePromise = await VolumeManager.getVolume();
  oldVolume = volumePromise.volume;
};

export const revertVolume = async () => {
  await VolumeManager.setVolume(oldVolume);
};

export const initTts = async () => {
  await Tts.getInitStatus();
  await Tts.setDefaultLanguage('en-US');

  if (Platform.OS === 'ios') {
    Tts.setIgnoreSilentSwitch("ignore");
    const voices = await Tts.voices();
    const selectedVoice = voices.find(
      v => v.language === 'en-US' && !v.notInstalled,
    );
    console.log(selectedVoice)
    if (selectedVoice) {
      Tts.setDefaultVoice(selectedVoice.id);
    }

    // Prewarm
    Tts.speak(' ');
    Tts.stop();
  }
};

export const setSpeakRate = (rate: number) => {
  Tts.setDefaultRate(rate);
};

export const play = (text: string) => {
  getVolume();
  setVolume(1);
  Tts.speak(text);
};

export const stop = () => {
  Tts.stop();
};

export const pause = () => {
  Tts.pause();
};

export const resume = () => {
  Tts.resume();
};

export const isSpeaking = () => {
  return new Promise((resolve, reject) => {
    Tts.addEventListener('tts-start', () => resolve(true));
    Tts.addEventListener('tts-finish', () => resolve(false));
    Tts.addEventListener('tts-cancel', () => resolve(false));
  });
};

export const setLanguage = (language: string) => {
  Tts.setDefaultLanguage(language);
  // console.log('Set language to: ' + language);
};

export const setVoice = (voice: string) => {
  Tts.setDefaultVoice(voice);
};

export const removeListeners = () => {
  Tts.removeAllListeners('tts-start');
  Tts.removeAllListeners('tts-finish');
  Tts.removeAllListeners('tts-cancel');
};
