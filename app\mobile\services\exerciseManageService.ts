import { Dispatch, SetStateAction } from 'react';
import { SQLiteDatabase, openDatabase } from 'react-native-sqlite-storage';
import uuid from 'react-native-uuid';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SERVER_API_URL } from './config';

export type Exercise = {
    id: number;
    lesson_id: number;
    name: string;
    content: string[];
    client_exercise_id: string;
    last_updated: string;
    is_synced: boolean;
};

export const openExerciseDatabase = async () => {
    return openDatabase({
        name: 'Exercises.db',
        location: 'default',
    });
};

export const createExerciseTable = async (db: SQLiteDatabase) => {
    try {
        await db.executeSql(`
            CREATE TABLE IF NOT EXISTS Exercises (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                lesson_id INTEGER,
                name TEXT,
                content TEXT,
                client_exercise_id TEXT UNIQUE,
                last_updated TEXT,
                is_synced INTEGER DEFAULT 0
            );
        `);

    } catch (error) {
        console.error('Error creating Exercise table:', error);
    }
};

// Fetch Exercises by Lesson ID
export const fetchExercises = async (
    db: SQLiteDatabase,
    lesson_id: number,
    setExercises: Dispatch<SetStateAction<Exercise[]>>
) => {
    try {
        const results = await db.executeSql('SELECT * FROM Exercises WHERE lesson_id = ?;', [lesson_id]);
        const rows = results[0].rows;
        let l_exercises: Exercise[] = [];
        for (let i = 0; i < rows.length; i++) {
            let item = rows.item(i);
            item.content = JSON.parse(item.content);
            item.is_synced = item.is_synced === 1;
            l_exercises.push(item);
        }
        setExercises([...l_exercises]);
    } catch (error) {
        console.error('Error fetching exercises by lesson_id:', error);
    }
};


export const insertEx = async (
    db: SQLiteDatabase,
    lesson_id: number,
    name: string,
    content: string[],
    setExercises?: Dispatch<SetStateAction<Exercise[]>>
) => {
    try {
        const client_exercise_id = uuid.v4() as string;
        const last_updated = new Date().toISOString();
        const contentJSON = JSON.stringify(content);

        await db.executeSql(
            'INSERT INTO Exercises (lesson_id, name, content, client_exercise_id, last_updated, is_synced) VALUES (?, ?, ?, ?, ?, 0);',
            [lesson_id, name, contentJSON, client_exercise_id, last_updated]
        );

        if (setExercises) {
            await fetchExercises(db, lesson_id, setExercises);
        }

    } catch (error) {
        console.error('Error inserting exercise locally:', error);
    }
};



// Delete Exercise Locally
export const deleteEx = async (
    db: SQLiteDatabase,
    ex: Exercise,
    setExercises: Dispatch<SetStateAction<Exercise[]>>
) => {
    try {
        await db.executeSql('DELETE FROM Exercises WHERE client_exercise_id = ?;', [ex.client_exercise_id]);
        await fetchExercises(db, ex.lesson_id, setExercises);
    } catch (error) {
        console.error('Error deleting exercise:', error);
    }
};

export const updateExercise = async (
    ex: Exercise,
    db: SQLiteDatabase,
    setExercises: Dispatch<SetStateAction<Exercise[]>>
) => {
    try {
        const contentJSON = JSON.stringify(ex.content);
        const last_updated = new Date().toISOString();
        await db.executeSql(
            'UPDATE Exercises SET name = ?, content = ?, last_updated = ?, is_synced = 0 WHERE client_exercise_id = ?',
            [ex.name, contentJSON, last_updated, ex.client_exercise_id]
        );
        await fetchExercises(db, ex.lesson_id, setExercises);
    } catch (error) {
        console.error('Error updating exercise:', error);
    }
};

export const selectExercise = (
    ex: Exercise,
    exercises: Exercise[],
    setCurrentExercise: Dispatch<SetStateAction<Exercise>>,
    setNextExercise: Dispatch<SetStateAction<Exercise>>
) => {
    setCurrentExercise(ex);
    const index = exercises.findIndex(item => item.client_exercise_id === ex.client_exercise_id);
    if (index === exercises.length - 1) {
        setNextExercise(ex);
    } else {
        setNextExercise(exercises[index + 1]);
    }
};


export const syncExercisesFromServer = async (
    patientId: string,
    lastSync: number,
    currentLessonId: number,
    setExercises: Dispatch<SetStateAction<Exercise[]>>
) => {
    try {
        const db = await openExerciseDatabase();

        // TRIGGER SYNC 
        const pushLocalExercise = async (ex: any) => {
            const checkExistExercise = `${SERVER_API_URL}/exercise/${ex.client_exercise_id}`;
            try {
                const checkResponse = await fetch(checkExistExercise);
                if (checkResponse.ok) {
                    const serverExercise = await checkResponse.json();
                    const serverTime = new Date(serverExercise.last_updated).getTime();
                    const localTime = new Date(ex.last_updated).getTime();


                    if (localTime > serverTime) {
                        const response = await fetch(checkExistExercise, {
                            method: 'PUT',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                lesson_id: ex.lesson_id,
                                name: ex.name,
                                content: JSON.parse(ex.content),
                                last_updated: ex.last_updated,
                            }),
                        });
                        if (response.ok) {
                        } else {
                            console.warn(`Failed to update exercise ${ex.client_exercise_id}`, await response.text());
                        }
                    } else if (serverTime > localTime) {
                        await db.executeSql(
                            'UPDATE Exercises SET name = ?, content = ?, last_updated = ?, is_synced = 0 WHERE client_exercise_id = ?;',
                            [serverExercise.name, JSON.stringify(serverExercise.content), serverExercise.last_updated, ex.client_exercise_id]
                        );
                    } else {
                        await db.executeSql('UPDATE Exercises SET is_synced = 1 WHERE client_exercise_id = ?;', [ex.client_exercise_id]);
                    }

                } else if (checkResponse.status === 404) {
                    // Not exists -> use POST
                    const postResponse = await fetch(`${SERVER_API_URL}/exercise`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            lesson_id: ex.lesson_id,
                            name: ex.name,
                            content: JSON.parse(ex.content),
                            client_exercise_id: ex.client_exercise_id,
                            last_updated: ex.last_updated,
                        }),
                    });
                    if (postResponse.ok) {
                        await db.executeSql('UPDATE Exercises SET is_synced = 1 WHERE client_exercise_id = ?;', [ex.client_exercise_id]);
                    } else {
                        console.warn(`Failed to create exercise ${ex.client_exercise_id}`, await postResponse.text());
                    }
                } else {
                    console.warn(`Unexpected status checking ${ex.client_exercise_id}: ${checkResponse.status}`);
                }
            } catch (error) {
                console.error(`Error syncing exercise ${ex.client_exercise_id}:`, error);
            }
        };

        const [localResults] = await db.executeSql('SELECT * FROM Exercises WHERE is_synced = 0;');
        const localRows = localResults.rows;
        for (let i = 0; i < localRows.length; i++) {
            await pushLocalExercise(localRows.item(i));
        }

        // Pull sync queue from server
        const syncUrl = `${SERVER_API_URL}/sync?patient_id=${patientId}&last_sync=${lastSync}`;
        const response = await fetch(syncUrl);
        const syncData = await response.json();

        if (!syncData.data || syncData.data.length === 0) return;

        let latestSyncTime = lastSync;

        for (const item of syncData.data) {
            if (item.type !== 'exercise' && item.type !== 'exercise_update') continue;

            try {
                const exUrl = `${SERVER_API_URL}/exercise/${item.record_id}`;
                const exerciseResponse = await fetch(exUrl);
                const exerciseData = await exerciseResponse.json();

                const [localResponse] = await db.executeSql(
                    'SELECT * FROM Exercises WHERE client_exercise_id = ?;',
                    [exerciseData.client_exercise_id]
                );

                if (localResponse.rows.length > 0) {
                    const localEx = localResponse.rows.item(0);
                    const serverTime = new Date(exerciseData.last_updated).getTime();
                    const localTime = new Date(localEx.last_updated).getTime();

                    if (serverTime > localTime) {
                        await db.executeSql(
                            'UPDATE Exercises SET name = ?, content = ?, last_updated = ?, is_synced = 0 WHERE client_exercise_id = ?;',
                            [exerciseData.name, JSON.stringify(exerciseData.content), exerciseData.last_updated, exerciseData.client_exercise_id]
                        );
                    } else {
                    }
                } else {
                    await db.executeSql(
                        'INSERT INTO Exercises (lesson_id, name, content, client_exercise_id, last_updated, is_synced) VALUES (?, ?, ?, ?, ?, 0);',
                        [exerciseData.lesson_id, exerciseData.name, JSON.stringify(exerciseData.content), exerciseData.client_exercise_id, exerciseData.last_updated]
                    );
                }

                // Acknowledge
                const ackResponse = await fetch(`${SERVER_API_URL}/sync/acknowledge`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ sync_id: item.sync_id }),
                });

                if (ackResponse.ok) {
                    await db.executeSql('UPDATE Exercises SET is_synced = 1 WHERE client_exercise_id = ?;', [item.record_id]);
                    latestSyncTime = Math.max(latestSyncTime, item.last_sync);
                }

            } catch (error) {
                console.error(`❗ Error processing sync item ${item.sync_id}:`, error);
            }
        }

        if (latestSyncTime > lastSync) {
            await AsyncStorage.setItem('@lastSync', String(latestSyncTime));
            await fetch(`${SERVER_API_URL}/patient/${patientId}/last_sync`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ last_sync: latestSyncTime }),
            });
        }

        await fetchExercises(db, currentLessonId, setExercises);

    } catch (error) {
        console.error('❗ Error during full sync flow:', error);
    }
};