from fastapi import <PERSON><PERSON><PERSON><PERSON>, Header
from fastapi.responses import JSONResponse
from app.routes.auth import validate_token
from app.extensions import celery

router = APIRouter(tags=["Task Status"])

@router.get("/health")
async def task_status_health_check():
    return {"status": "ok", "message": "Task status route is reachable"}

@router.get("/status/{task_id}")
async def task_status(task_id: str, authorization: str = Header(None)):
    if not authorization:
        return JSONResponse(status_code=401, content={"error": "Unauthorized"})
    
    token = authorization.split()[1]
    if not validate_token(token):
        return JSONResponse(status_code=405, content={"error": "Invalid or expired token"})

    task = celery.AsyncResult(task_id)
    
    if task.state == 'PENDING':
        return JSONResponse(content={'state': 'PENDING', 'status': 'Pending'})
    elif task.state == 'STARTED':
        return JSONResponse(content={'state': 'STARTED', 'status': 'Processing'})
    elif task.state == 'SUCCESS':
        return JSONResponse(content={'state': 'SUCCESS', 'status': 'Completed', 'result': task.result})
    elif task.state == 'FAILURE':
        return JSONResponse(content={'state': 'FAILURE', 'status': 'Failed', 'result': str(task.result)})
    else:
        return JSONResponse(content={'state': task.state, 'status': 'Unknown'})

