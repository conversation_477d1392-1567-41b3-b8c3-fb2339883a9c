import { Platform } from 'react-native';
import AudioSession from 'react-native-audio-session';

/**
 * AudioSessionManager - Handles audio session configuration for iOS
 */
class AudioSessionManager {
  private static instance: AudioSessionManager;
  private isConfigured: boolean = false;

  private constructor() {}

  public static getInstance(): AudioSessionManager {
    if (!AudioSessionManager.instance) {
      AudioSessionManager.instance = new AudioSessionManager();
    }
    return AudioSessionManager.instance;
  }

  /**
   * Configure audio session for recording
   */
  public async configureForRecording(): Promise<void> {
    if (Platform.OS !== 'ios') return;

    try {
      console.log('[AudioSessionManager] Configuring audio session for recording');
      await AudioSession.setCategory('PlayAndRecord', 'MixWithOthers');
      await AudioSession.setActive(true);
      this.isConfigured = true;
      console.log('[AudioSessionManager] Audio session configured for recording');
    } catch (error) {
      console.error('[AudioSessionManager] Error configuring audio session for recording:', error);
    }
  }

  /**
   * Configure audio session for playback
   */
  public async configureForPlayback(): Promise<void> {
    if (Platform.OS !== 'ios') return;

    try {
      console.log('[AudioSessionManager] Configuring audio session for playback');
      await AudioSession.setCategory('Playback');
      await AudioSession.setActive(true);
      this.isConfigured = true;
      console.log('[AudioSessionManager] Audio session configured for playback');
    } catch (error) {
      console.error('[AudioSessionManager] Error configuring audio session for playback:', error);
    }
  }

  /**
   * Deactivate the audio session
   */
  public async deactivate(): Promise<void> {
    if (Platform.OS !== 'ios' || !this.isConfigured) return;

    try {
      console.log('[AudioSessionManager] Deactivating audio session');
      await AudioSession.setActive(false);
      this.isConfigured = false;
      console.log('[AudioSessionManager] Audio session deactivated');
    } catch (error) {
      console.error('[AudioSessionManager] Error deactivating audio session:', error);
    }
  }

  /**
   * Reset the audio session
   */
  public async reset(): Promise<void> {
    if (Platform.OS !== 'ios') return;

    try {
      console.log('[AudioSessionManager] Resetting audio session');

      await this.deactivate();

      await new Promise(resolve => setTimeout(resolve, 300));

      try {
        await AudioSession.setCategory('Ambient');
        console.log('[AudioSessionManager] Set temporary Ambient category');
      } catch (categoryError) {
        console.log('[AudioSessionManager] Error setting temporary category:', categoryError);
      }

      await new Promise(resolve => setTimeout(resolve, 100));

      await AudioSession.setCategory('PlayAndRecord', 'MixWithOthers');
      await AudioSession.setActive(true);
      this.isConfigured = true;

      console.log('[AudioSessionManager] Audio session reset complete');
    } catch (error) {
      console.error('[AudioSessionManager] Error resetting audio session:', error);

      try {
        console.log('[AudioSessionManager] Attempting aggressive reset');
        await AudioSession.setActive(false);
        await new Promise(resolve => setTimeout(resolve, 500));
        await AudioSession.setCategory('PlayAndRecord', 'MixWithOthers');
        await AudioSession.setActive(true);
        this.isConfigured = true;
        console.log('[AudioSessionManager] Aggressive reset complete');
      } catch (retryError) {
        console.error('[AudioSessionManager] Aggressive reset also failed:', retryError);
      }
    }
  }
}

export default AudioSessionManager.getInstance();