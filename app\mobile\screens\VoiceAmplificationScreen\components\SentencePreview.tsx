import {Image, Text, TouchableWithoutFeedback, View} from 'react-native';
import {SentencePreviewStyle} from '../styles/SentencePreviewStyle';
import {Dispatch, SetStateAction} from 'react';
import useSentencePreview from '../hooks/useSentencePreview';
import {IconButton} from 'react-native-paper';
import {SkipForward, X} from 'lucide-react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {TopNav} from '../../../components/TopNav/TopNav';

type SentencePreviewProps = {
  sentence: string;
  highlightedWord: number;
  image: any;
  setIsSpeaking: Dispatch<SetStateAction<boolean>>;
  setPlayingPreview: Dispatch<SetStateAction<boolean>>;
};

export const SentencePreview: React.FC<SentencePreviewProps> = ({
  sentence,
  highlightedWord,
  image,
  setIsSpeaking,
  setPlayingPreview,
}) => {
  const {words, highlightAll, handleSkipPreview, closePreview, previewSkipped} =
    useSentencePreview(sentence, setIsSpeaking, setPlayingPreview);

  const styles = SentencePreviewStyle();
  return (
    <View style={styles.container}>
      <TopNav
        backFunction={previewSkipped ? closePreview : handleSkipPreview}
        title="Voice Amplification"></TopNav>

      <ScrollView>
        <Text style={styles.text}>
          {words.map((word, index) => (
            <Text
              key={index}
              style={[
                styles.word,
                (index <= highlightedWord || highlightAll) &&
                  styles.highlighted,
              ]}>
              {word + ' '}
            </Text>
          ))}
        </Text>
      </ScrollView>
    </View>
  );
};
