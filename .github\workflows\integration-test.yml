name: Run Integration Tests

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      redis:
        image: redis:alpine
        ports:
          - 6379:6379

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Run integration test
        run: |
          docker compose -f docker-compose.yml up --build --abort-on-container-exit --exit-code-from test

      - name: Show logs on failure
        if: failure()
        run: docker compose logs
