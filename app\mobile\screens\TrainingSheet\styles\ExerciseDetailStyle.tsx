import { StyleSheet } from 'react-native';
import { useTheme } from 'react-native-paper';

export const ExerciseDetailStyle = (isDirect?: boolean) => {
  const theme = useTheme();
  return StyleSheet.create({
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      backgroundColor: theme.colors.background,
    },
    textContainer: {
      alignItems: 'flex-start',
      height: 100,
    },

    rightButtons: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      height: '100%',
    },

    buttons: {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      backgroundColor: 'transparent',
      position: 'absolute',
      bottom: 20,
      paddingHorizontal: 20,
      width: '100%',
    },

    leftButtons: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#3C80F3',
    },

    checkButton: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#4FC3C7',
    },
  });
};
