"use client";
import React, { useState, useEffect } from "react";


type ServerResponse = {
  message: string; // Adjust this based on the actual shape of your API response
};

export default function HelloWorld() {

const [data, setData] = useState<ServerResponse | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const res = await fetch('http://localhost:8000/api/hello');
        const json = await res.json();
        setData(json);
      } catch (error) {
        console.error('Failed to fetch data', error);
      }
    }

    fetchData();
  }, []);

  if (!data) {
    return <div>Loading...</div>;
  }

  return (
    <div> {data.message} </div>
  );


}
