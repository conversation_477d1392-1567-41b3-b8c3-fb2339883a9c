import React from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  TouchableWithoutFeedback,
  TextInput,
} from 'react-native';
import {Text} from 'react-native-paper';
import {ChevronDown, ChevronUp} from 'lucide-react-native';
import DatePicker from 'react-native-date-picker';
import CustomSlider from '../../components/CustomSlider/CustomSlider';
import CustomSwitch from '../../components/CustomSwitch/CustomSwitch';
import {TopNav} from '../../components/TopNav/TopNav';
import useSettingsScreenStyle from './styles/SettingsScreenStyle';
import LanguageSelector from '../../components/LanguageSelector/LanguageSelector';
import useSettingsScreen from './hooks/useSettingsScreen';

const SettingsScreen = () => {
  const styles = useSettingsScreenStyle();

  const {
    activeTab,
    setShowLanguageSelect,
    language,
    images,
    handleChangeLanguage,
    showLanguageSelect,
    localPlaybackSpeed,
    handleSetPlaybackSpeed,
    autoExit,
    handlePlaybackAutoExit,
    isEditingName,
    tempName,
    setTempName,
    setName,
    setIsEditingName,
    name,
    setShowDatePicker,
    dateOfBirth,
    showDatePicker,
    dateOfBirthDate,
    handleDateChange,
    showGenderDropdown,
    setShowGenderDropdown,
    gender,
    genderOptions,
    handleGenderSelect,
    isEditingEmail,
    setIsEditingEmail,
    tempEmail,
    setTempEmail,
    email,
    setEmail,
    hasProfileChanges,
    handleSaveProfile,
    handleTabPress,
  } = useSettingsScreen();

  const renderTabContent = () => {
    switch (activeTab) {
      case 'App':
        return (
          <>
            {/* App Language Setting */}
            <View style={styles.settingRow}>
              <Text style={styles.settingLabel}>App Language</Text>
              <TouchableOpacity
                style={styles.languageButton}
                onPress={() => setShowLanguageSelect(true)}>
                <Text style={styles.languageText}>
                  {language ? language.name : 'English'}
                </Text>
                <Image
                  source={
                    images[
                      language
                        ? (language.code as keyof typeof images)
                        : 'en-US'
                    ]
                  }
                  style={styles.flagImage}
                />
              </TouchableOpacity>

              <LanguageSelector
                languageCode={language.code}
                handleChangeLanguageGlobal={handleChangeLanguage}
                showLanguage={showLanguageSelect}
                setShowLanguage={setShowLanguageSelect}
              />
            </View>
            <View style={styles.divider} />

            {/* Playback Speed Setting */}
            <View style={styles.settingRow}>
              <Text style={styles.settingLabel}>Playback Speed</Text>
              <View style={styles.sliderContainer}>
                <CustomSlider
                  value={localPlaybackSpeed}
                  onValueChange={handleSetPlaybackSpeed}
                  minimumValue={0.5}
                  maximumValue={1.5}
                  step={0.1}
                />
                <Text style={styles.sliderValue}>
                  x{localPlaybackSpeed.toFixed(1)}
                </Text>
              </View>
            </View>
            <View style={styles.divider} />

            {/* Auto Exit Setting */}
            <View style={styles.settingRow}>
              <Text style={styles.settingLabel}>Auto exit after playback</Text>
              <CustomSwitch
                value={autoExit}
                onValueChange={handlePlaybackAutoExit}
              />
            </View>
            <View style={styles.divider} />
          </>
        );
      case 'Profile':
        return (
          <View style={styles.profileContainer}>
            {/* Profile Picture */}
            <TouchableOpacity>
              <Image
                source={require('../../assets/images/profile-placeholder.png')}
                style={styles.profilePicture}
                defaultSource={require('../../assets/images/profile-placeholder.png')}
              />
            </TouchableOpacity>

            {/* Profile Information */}
            <View style={[styles.profileInfoContainer, {zIndex: 1}]}>
              {/* Name */}
              <View style={styles.profileInfoRow}>
                <Text style={styles.profileLabel}>Name</Text>
                {isEditingName ? (
                  <TextInput
                    style={styles.editableInput}
                    value={tempName}
                    onChangeText={setTempName}
                    autoFocus
                    onBlur={() => {
                      setName(tempName);
                      setIsEditingName(false);
                    }}
                    onSubmitEditing={() => {
                      setName(tempName);
                      setIsEditingName(false);
                    }}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={() => {
                      setTempName(name);
                      setIsEditingName(true);
                    }}>
                    <Text style={styles.profileValue}>{name}</Text>
                  </TouchableOpacity>
                )}
              </View>

              {/* Date of Birth */}
              <View style={styles.profileInfoRow}>
                <Text style={styles.profileLabel}>Date of Birth</Text>
                <TouchableOpacity onPress={() => setShowDatePicker(true)}>
                  <Text style={styles.profileValue}>{dateOfBirth}</Text>
                </TouchableOpacity>
              </View>

              {/* Date Picker Modal */}
              <DatePicker
                modal
                open={showDatePicker}
                date={dateOfBirthDate}
                mode="date"
                maximumDate={new Date()}
                onConfirm={handleDateChange}
                onCancel={() => setShowDatePicker(false)}
              />

              {/* Gender */}
              <View style={[styles.profileInfoRow, {zIndex: 2}]}>
                <Text style={styles.profileLabel}>Gender</Text>
                <View style={{zIndex: 3, position: 'relative'}}>
                  <TouchableOpacity
                    style={styles.profileValueContainer}
                    onPress={() => setShowGenderDropdown(!showGenderDropdown)}>
                    <Text style={styles.profileValue}>{gender}</Text>
                    <View style={{marginLeft: 8}}>
                      {showGenderDropdown ? (
                        <ChevronUp size={20} color="#3C80F3" />
                      ) : (
                        <ChevronDown size={20} color="#3C80F3" />
                      )}
                    </View>
                  </TouchableOpacity>

                  {/* Gender Dropdown */}
                  {showGenderDropdown && (
                    <>
                      <TouchableWithoutFeedback
                        onPress={() => setShowGenderDropdown(false)}>
                        <View style={styles.dropdownBackdrop} />
                      </TouchableWithoutFeedback>
                      <View style={styles.dropdownContainer}>
                        {genderOptions.map(option => (
                          <TouchableOpacity
                            key={option}
                            style={[
                              styles.dropdownItem,
                              gender === option && styles.dropdownItemSelected,
                            ]}
                            onPress={() => handleGenderSelect(option)}>
                            <Text
                              style={[
                                styles.dropdownItemText,
                                gender === option &&
                                  styles.dropdownItemTextSelected,
                              ]}>
                              {option}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </>
                  )}
                </View>
              </View>

              {/* Email */}
              <View style={[styles.profileInfoRow, {zIndex: 1}]}>
                <Text style={styles.profileLabel}>Email</Text>
                {isEditingEmail ? (
                  <TextInput
                    style={styles.editableInput}
                    value={tempEmail}
                    onChangeText={setTempEmail}
                    placeholder="Enter your email"
                    placeholderTextColor="#AAAAAA"
                    keyboardType="email-address"
                    autoFocus
                    onBlur={() => {
                      setEmail(tempEmail);
                      setIsEditingEmail(false);
                    }}
                    onSubmitEditing={() => {
                      setEmail(tempEmail);
                      setIsEditingEmail(false);
                    }}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={() => {
                      setTempEmail(email);
                      setIsEditingEmail(true);
                    }}>
                    {email ? (
                      <Text style={styles.profileValue}>{email}</Text>
                    ) : (
                      <Text style={styles.profilePlaceholder}>Your email</Text>
                    )}
                  </TouchableOpacity>
                )}
              </View>

              {/* Save Button - Only shown when there are changes */}
              {hasProfileChanges() && (
                <TouchableOpacity
                  style={[styles.saveButton, {zIndex: 1}]}
                  onPress={handleSaveProfile}>
                  <Text style={styles.saveButtonText}>Save Changes</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        );
      case 'Connect':
        return <View></View>;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <TopNav title="Settings" />
      <View style={styles.content}>
        {/* Segmented Buttons */}
        <View style={styles.segmentedButtonContainer}>
          <TouchableWithoutFeedback onPress={() => handleTabPress('App')}>
            <View
              style={[
                styles.segmentButton,
                activeTab === 'App' && styles.segmentButtonSelected,
              ]}>
              <Text
                style={[
                  styles.segmentButtonText,
                  activeTab === 'App' && styles.segmentButtonTextSelected,
                ]}>
                App
              </Text>
            </View>
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback onPress={() => handleTabPress('Profile')}>
            <View
              style={[
                styles.segmentButton,
                activeTab === 'Profile' && styles.segmentButtonSelected,
              ]}>
              <Text
                style={[
                  styles.segmentButtonText,
                  activeTab === 'Profile' && styles.segmentButtonTextSelected,
                ]}>
                Profile
              </Text>
            </View>
          </TouchableWithoutFeedback>

          <TouchableWithoutFeedback onPress={() => handleTabPress('Connect')}>
            <View
              style={[
                styles.segmentButton,
                activeTab === 'Connect' && styles.segmentButtonSelected,
              ]}>
              <Text
                style={[
                  styles.segmentButtonText,
                  activeTab === 'Connect' && styles.segmentButtonTextSelected,
                ]}>
                Connect
              </Text>
            </View>
          </TouchableWithoutFeedback>
        </View>

        {/* Tab Content */}
        {renderTabContent()}
      </View>
    </View>
  );
};

export default SettingsScreen;
