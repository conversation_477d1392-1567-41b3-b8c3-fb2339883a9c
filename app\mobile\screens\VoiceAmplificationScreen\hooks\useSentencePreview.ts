import {Dispatch, SetStateAction, useEffect, useState} from 'react';
import {stop} from '../../../services/ttsService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';

const useSentencePreview = (
  sentence: string,
  setIsSpeaking: Dispatch<SetStateAction<boolean>>,
  setPlayingPreview: Dispatch<SetStateAction<boolean>>,
) => {
  const words = sentence.split(' ');
  const [highlightAll, setHighlightAll] = useState<boolean>(false);
  const [previewSkipped, setPreviewSkipped] = useState<boolean>(false);
  const {autoEndPreview} = useSorting();

  useEffect(() => {
    if (!autoEndPreview) {
      setPreviewSkipped(true);
    }
  }, []);

  const handleSkipPreview = () => {
    setHighlightAll(true);
    stop();
    setPreviewSkipped(true);
  };

  const closePreview = () => {
    stop();
    setPreviewSkipped(false);
    setIsSpeaking(false);
    setPlayingPreview(false);
  };

  return {
    words,
    highlightAll,
    handleSkipPreview,
    closePreview,
    previewSkipped,
  };
};

export default useSentencePreview;
