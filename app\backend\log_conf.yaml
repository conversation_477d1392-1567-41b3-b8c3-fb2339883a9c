version: 1
disable_existing_loggers: false

formatters:
  default:
    "()": "uvicorn.logging.DefaultFormatter"
    fmt: "%(levelprefix)s [%(asctime)s] %(message)s"
    use_colors: true

  access:
    "()": "uvicorn.logging.AccessFormatter"
    fmt: '%(levelprefix)s [%(asctime)s] %(client_addr)s - "%(request_line)s" %(status_code)s'

handlers:
  default:
    formatter: default
    class: logging.StreamHandler
    stream: ext://sys.stderr

  access:
    formatter: access
    class: logging.StreamHandler
    stream: ext://sys.stdout

loggers:
  uvicorn:
    handlers: [default]
    level: INFO

  uvicorn.error:
    handlers: [default]
    level: INFO
    propagate: false

  uvicorn.access:
    handlers: [access]
    level: INFO
    propagate: false

  # Your app logger
  app:
    handlers: [default]
    level: DEBUG
    propagate: false
