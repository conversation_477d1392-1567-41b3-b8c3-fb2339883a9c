import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import React, {useCallback, useReducer, useRef} from 'react';
import SQLite from 'react-native-sqlite-storage';
import {LanguageItem} from '../components/LanguageSelection/LanguageSelection';
import {AudioFile, updateAudioFile} from './audioFileManageService';
import {Exercise} from './exerciseManageService';
import {Graph, LessonCount} from './graphManageService';
import {Lesson} from './lessonManageService';
import {Phrase} from './phraseManageService';

// Define types for state and action
interface SortingState {
  sortOption: string;
  phrases: Phrase[];
  language: LanguageItem;
  lessons: Lesson[];
  exercises: Exercise[];
  currentLesson: Lesson;
  currentExercise: Exercise;
  nextExercise: Exercise;
  audioFiles: AudioFile[];
  lessonCount: LessonCount[];
  graph: Graph[];
  token: string;
  autoEndPreview: boolean;
  isPlayAllMode: boolean;
  playbackSpeed: number;
}

interface SortingAction {
  type: string;
  payload: any;
}

const sortingReducer = (state: SortingState, action: SortingAction) => {
  switch (action.type) {
    case 'SET_SORT_OPTION':
      return {
        ...state,
        sortOption: action.payload,
      };
    case 'SET_PHRASES':
      return {
        ...state,
        phrases: action.payload,
      };
    case 'SET_LANGUAGE':
      return {
        ...state,
        language: action.payload,
      };
    case 'SET_LESSONS':
      return {
        ...state,
        lessons: action.payload,
      };
    case 'SET_EXERCISES':
      return {
        ...state,
        exercises: action.payload,
      };

    case 'SET_CURRENT_LESSON':
      return {
        ...state,
        currentLesson: action.payload,
      };
    case 'SET_CURRENT_EXERCISE':
      return {
        ...state,
        currentExercise: action.payload,
      };
    case 'SET_NEXT_EXERCISE':
      return {
        ...state,
        nextExercise: action.payload,
      };
    case 'SET_AUDIO_FILES':
      return {
        ...state,
        audioFiles: action.payload,
      };
    case 'SET_LESSON_COUNT':
      return {
        ...state,
        lessonCount: action.payload,
      };
    case 'SET_GRAPH':
      return {
        ...state,
        graph: action.payload,
      };
    case 'SET_TOKEN':
      return {
        ...state,
        token: action.payload,
      };
    case 'SET_PREVIEW_END':
      return {
        ...state,
        autoEndPreview: action.payload,
      };
    case 'SET_PLAY_ALL_LESSON':
      return {
        ...state,
        isPlayAllMode: action.payload,
      };
    case 'SET_PLAYBACK_SPEED':
      return {
        ...state,
        playbackSpeed: action.payload,
      };
    default:
      return state;
  }
};

const SortingProviderService = () => {
  // const intervalRef = useRef<ReturnType<typeof setInterval>>(null);
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const language: LanguageItem = {
    name: 'English',
    code: 'en-US',
    voiceCode: 'en_US',
    isSelected: false,
  };
  const [state, dispatch] = useReducer(sortingReducer, {
    sortOption: 'liked',
    phrases: [] as Phrase[],
    language: language,
    lessons: [] as Lesson[],
    exercises: [] as Exercise[],
    currentLesson: {} as Lesson,
    currentExercise: {} as Exercise,
    nextExercise: {} as Exercise,
    audioFiles: [] as AudioFile[],
    lessonCount: [] as LessonCount[],
    graph: [] as Graph[],
    token: '',
    autoEndPreview: true,
    isPlayAllMode: false,
    playbackSpeed: 0.5,
  } as SortingState);

  const setSortOption = async (option: string) => {
    try {
      await AsyncStorage.setItem('@sortOption', option);
      dispatch({type: 'SET_SORT_OPTION', payload: option});
    } catch (error) {
      console.error('Error setting sort option', error);
    }
  };

  const setPhrases = (phrasesTmp: Phrase[]) => {
    if (Array.isArray(phrasesTmp)) {
      dispatch({type: 'SET_PHRASES', payload: [...phrasesTmp]});
    }
  };

  const setLanguage = async (language: LanguageItem) => {
    try {
      await AsyncStorage.setItem('@language', JSON.stringify(language));
      dispatch({type: 'SET_LANGUAGE', payload: language});
    } catch (error) {
      console.error('Error setting language', error);
    }
  };

  const setLessons = (lessonsTmp: Lesson[]) => {
    if (Array.isArray(lessonsTmp)) {
      dispatch({type: 'SET_LESSONS', payload: [...lessonsTmp]});
    }
  };

  const setExercises = (exercisesTmp: Exercise[]) => {
    if (Array.isArray(exercisesTmp)) {
      dispatch({type: 'SET_EXERCISES', payload: [...exercisesTmp]});
    }
  };

  const setCurrentLesson = (currentLessonTmp: Lesson) => {
    dispatch({type: 'SET_CURRENT_LESSON', payload: currentLessonTmp});
  };

  const setCurrentExercise = (currentExerciseTmp: Exercise) => {
    dispatch({type: 'SET_CURRENT_EXERCISE', payload: currentExerciseTmp});
  };

  const setNextExercise = (nextExerciseTmp: Exercise) => {
    dispatch({type: 'SET_NEXT_EXERCISE', payload: nextExerciseTmp});
  };

  // This function can accept either an array or a function that returns an array
  const setAudioFiles = (
    audioFilesTmp: AudioFile[] | ((prev: AudioFile[]) => AudioFile[]),
  ) => {
    if (typeof audioFilesTmp === 'function') {
      const updater = audioFilesTmp;
      dispatch({type: 'SET_AUDIO_FILES', payload: updater(state.audioFiles)});
    } else if (Array.isArray(audioFilesTmp)) {
      dispatch({type: 'SET_AUDIO_FILES', payload: [...audioFilesTmp]});
    }
  };

  const setLessonCount = (lessonCountTmp: LessonCount[]) => {
    dispatch({type: 'SET_LESSON_COUNT', payload: lessonCountTmp});
  };

  const setGraph = (graphTmp: Graph[]) => {
    dispatch({type: 'SET_GRAPH', payload: graphTmp});
  };

  const setToken = async (token: string) => {
    try {
      await AsyncStorage.setItem('@token', token);
      dispatch({type: 'SET_TOKEN', payload: token});
    } catch (error) {
      console.error('Error setting token', error);
    }
  };

  const setPreviewEnd = async (autoEndPreview: boolean) => {
    try {
      await AsyncStorage.setItem(
        '@autoEndPreview',
        JSON.stringify(autoEndPreview),
      );
      dispatch({type: 'SET_PREVIEW_END', payload: autoEndPreview});
    } catch (error) {
      console.error('Error setting preview end setting', error);
    }
  };

  const setPlayAllLesson = async (isPlayAllMode: boolean) => {
    try {
      dispatch({type: 'SET_PLAY_ALL_LESSON', payload: isPlayAllMode});
    } catch (error) {
      console.error('Error setting play all lessons setting', error);
    }
  };

  const setPlaybackSpeed = async (playbackSpeed: number) => {
    try {
      await AsyncStorage.setItem('@playbackSpeed', playbackSpeed.toFixed(2));
      dispatch({type: 'SET_PLAYBACK_SPEED', payload: playbackSpeed});
    } catch (error) {
      console.error('Error setting play back speed setting', error);
    }
  };

  const convertJSONData = (data: Record<string, any>) => {
    let entries = Object.entries(data);
    const keysToRemove = [
      'encoded_spectrogram',
      'encoded_waveform',
      'encoded_avqi',
      'encoded_abi',
    ];
    entries = entries.filter(([key, _]) => !keysToRemove.includes(key));
    let modifiedData = entries.reduce((acc, [key, value]) => {
      let formattedKey = key.replace(/_/g, ' ');
      acc[formattedKey] = value;
      return acc;
    }, {} as Record<string, any>);
    let dataString = JSON.stringify(modifiedData);
    return dataString;
  };

  const requestResult = async (audioFile: AudioFile) => {
    try {
      // Get a token if we don't have one
      let token = state.token;
      if (!token) {
        try {
          // Based on the Postman collection, we need to use client-id and client-secret
          const clientId = 'client-id';
          const clientSecret = 'client-secret';

          // Create Basic auth header
          const basicAuth = `Basic ${Buffer.from(
            `${clientId}:${clientSecret}`,
          ).toString('base64')}`;

          console.log('Getting token for task status check...');
          const authResponse = await axios.post(
            'https://voiceback.nextwaytech.vn/api/auth/token',
            {},
            {
              headers: {
                Authorization: basicAuth,
                'Content-Type': 'application/json',
              },
            },
          );

          if (authResponse.data && authResponse.data.access_token) {
            token = authResponse.data.access_token;
            console.log('Successfully obtained token for task status check');
          }
        } catch (authError) {
          console.error('Error getting token for task status:', authError);
        }
      }

      // Use the production URL
      const config = {
        method: 'GET',
        url: `https://voiceback.nextwaytech.vn/api/task/status/${audioFile.taskId}`,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        timeout: 10000,
      };

      // Log the task ID prominently for easy reference
      console.log('==============================================');
      console.log(`CHECKING STATUS FOR TASK ID: ${audioFile.taskId}`);
      console.log('==============================================');

      const result = await axios.request(config);

      // Log the task status response prominently
      console.log('==============================================');
      console.log(`TASK ${audioFile.taskId} STATUS: ${result.data.status}`);
      console.log(
        `TASK ${audioFile.taskId} STATE: ${result.data.state || 'N/A'}`,
      );
      if (result.data.result) {
        console.log(
          `TASK ${audioFile.taskId} RESULT CODE: ${
            result.data.result.code || 'N/A'
          }`,
        );
      }
      console.log('==============================================');

      console.log(`Task status response:`, result.data);

      // Don't log file paths during task status check to avoid confusion
      // This will prevent the "Using existing file path" logs for multiple files

      if (result.data.status === 'Completed') {
        console.log(`Task ${audioFile.taskId} completed successfully`);
        if (result.data.result !== undefined) {
          if (result.data.result.code === 500) {
            console.log(`Task ${audioFile.taskId} returned error code 500`);
            handleUpdateData(
              audioFile,
              'Failed',
              audioFile.taskId,
              '',
              '',
              '',
              '',
              '',
              setAudioFiles,
            );
          } else {
            console.log(
              `Task ${audioFile.taskId} returned data:`,
              result.data.result.data,
            );

            // Check if we have all the required data
            if (result.data.result.data) {
              // Log the data structure to understand what we're getting
              console.log('==============================================');
              console.log('ANALYSIS DATA STRUCTURE:');
              Object.keys(result.data.result.data).forEach(key => {
                console.log(`- ${key}: ${typeof result.data.result.data[key]}`);
                if (typeof result.data.result.data[key] === 'object') {
                  Object.keys(result.data.result.data[key]).forEach(subKey => {
                    console.log(
                      `  - ${subKey}: ${typeof result.data.result.data[key][
                        subKey
                      ]}`,
                    );
                  });
                }
              });
              console.log('==============================================');

              // Extract the data we need
              let avqiData = '';
              let abiData = '';
              let spectrogramData = '';
              let waveformData = '';
              let avqiGramData = '';
              let abiGramData = '';

              console.log('==============================================');
              console.log('EXTRACTING ANALYSIS DATA:');

              // Check if avqi data exists
              if (result.data.result.data.avqi) {
                avqiData = JSON.stringify(result.data.result.data.avqi);
                console.log('AVQI data found and extracted');

                // Check for encoded plots in avqi
                if (result.data.result.data.avqi.encoded_plot) {
                  avqiGramData = result.data.result.data.avqi.encoded_plot;
                  console.log('AVQI plot data found and extracted');
                }

                // Check for spectrogram data in AVQI
                if (result.data.result.data.avqi.encoded_spectrogram) {
                  spectrogramData =
                    result.data.result.data.avqi.encoded_spectrogram;
                  console.log('Spectrogram data found in AVQI and extracted');
                }

                // Check for waveform data in AVQI
                if (result.data.result.data.avqi.encoded_waveform) {
                  waveformData = result.data.result.data.avqi.encoded_waveform;
                  console.log('Waveform data found in AVQI and extracted');
                }
              }

              // Check if abi data exists
              if (result.data.result.data.abi) {
                abiData = JSON.stringify(result.data.result.data.abi);
                console.log('ABI data found and extracted');

                // Check for encoded plots in abi
                if (result.data.result.data.abi.encoded_plot) {
                  abiGramData = result.data.result.data.abi.encoded_plot;
                  console.log('ABI plot data found and extracted');
                }
              }

              // Check for top-level spectrogram data
              if (result.data.result.data.encoded_spectrogram) {
                spectrogramData = result.data.result.data.encoded_spectrogram;
                console.log('Top-level spectrogram data found and extracted');
              }

              // Check for top-level waveform data
              if (result.data.result.data.encoded_waveform) {
                waveformData = result.data.result.data.encoded_waveform;
                console.log('Top-level waveform data found and extracted');
              }

              console.log('==============================================');

              // Create a combined data object with both AVQI and ABI data
              let combinedData = {};

              // Add AVQI data if available
              if (avqiData) {
                try {
                  const avqiObj = JSON.parse(avqiData);
                  combinedData = {...combinedData, ...avqiObj};
                } catch (e) {
                  console.error('Error parsing AVQI data:', e);
                }
              }

              // Add ABI data if available
              if (abiData) {
                try {
                  const abiObj = JSON.parse(abiData);
                  combinedData = {
                    ...combinedData,
                    abi: abiObj.value || abiObj.abi || 0,
                    abi_cpps: abiObj.cpps || 0,
                    abi_jitter: abiObj.jitter || 0,
                    abi_gne: abiObj.gne || 0,
                    abi_hfno: abiObj.hfno || 0,
                    abi_hnrd: abiObj.hnrd || 0,
                    abi_h1h2: abiObj.h1h2 || 0,
                    abi_shimmer_db: abiObj.shimmer_db || 0,
                    abi_shimmer_percent: abiObj.shimmer_percent || 0,
                    abi_period_sd: abiObj.period_sd || 0,
                    abi_encoded_plot: abiObj.encoded_plot || '',
                  };
                  console.log('ABI data flattened to top level');
                } catch (e) {
                  console.error('Error parsing ABI data:', e);
                }
              }

              console.log('==============================================');
              console.log('SENDING TO DATABASE:');
              console.log(`Status: ${result.data.status}`);
              console.log(`Task ID: ${audioFile.taskId}`);
              console.log(
                `Combined Data: ${JSON.stringify(combinedData).substring(
                  0,
                  100,
                )}...`,
              );
              console.log(
                `Spectrogram: ${spectrogramData ? 'Present' : 'Missing'}`,
              );
              console.log(`Waveform: ${waveformData ? 'Present' : 'Missing'}`);
              console.log(`AVQI Plot: ${avqiGramData ? 'Present' : 'Missing'}`);
              console.log(`ABI Plot: ${abiGramData ? 'Present' : 'Missing'}`);
              console.log('==============================================');

              const combinedDataString = JSON.stringify(combinedData);

              // Update the data in the database
              handleUpdateData(
                audioFile,
                result.data.status,
                audioFile.taskId,
                combinedDataString,
                spectrogramData,
                waveformData,
                avqiGramData,
                abiGramData,
                setAudioFiles,
              );
            } else {
              console.error(`Task ${audioFile.taskId} missing data`);
              handleUpdateData(
                audioFile,
                'Failed',
                audioFile.taskId,
                '',
                '',
                '',
                '',
                '',
                setAudioFiles,
              );
            }
          }
        } else {
          console.error(`Task ${audioFile.taskId} missing result data`);
          handleUpdateData(
            audioFile,
            'Failed',
            audioFile.taskId,
            '',
            '',
            '',
            '',
            '',
            setAudioFiles,
          );
        }
      } else {
        console.log(`Task ${audioFile.taskId} status: ${result.data.status}`);
        handleUpdateData(
          audioFile,
          result.data.status,
          audioFile.taskId,
          '',
          '',
          '',
          '',
          '',
          setAudioFiles,
        );
      }
    } catch (error) {
      console.error(
        `Error requesting result for task ${audioFile.taskId}:`,
        error,
      );

      // Check if we've been trying for too long (more than 5 minutes)
      const creationTime = new Date(audioFile.name.split(',')[1].trim());
      const now = new Date();
      const timeDiff = now.getTime() - creationTime.getTime();
      const minutesDiff = timeDiff / (1000 * 60);

      if (minutesDiff > 5) {
        console.log(
          `Task ${audioFile.taskId} has been pending for more than 5 minutes, marking as failed`,
        );
        handleUpdateData(
          audioFile,
          'Failed',
          audioFile.taskId,
          '',
          '',
          '',
          '',
          '',
          setAudioFiles,
        );
      } else {
        // Keep it as pending if it's been less than 5 minutes
        console.log(
          `Task ${audioFile.taskId} has been pending for ${minutesDiff.toFixed(
            1,
          )} minutes, keeping as pending`,
        );
      }
    }
  };

  const handleUpdateData = async (
    audioFile: AudioFile,
    status: string,
    task_id: string,
    result: any,
    spectrogramPath: string,
    waveFormPath: string,
    avqiGram: string,
    abiGram: string,
    setAudioFiles: (
      audioFilesTmp: AudioFile[] | ((prev: AudioFile[]) => AudioFile[]),
    ) => void,
  ) => {
    const db = await SQLite.openDatabase({
      name: 'audioFiles.db',
      location: 'default',
    });

    await updateAudioFile(
      db,
      {
        ...audioFile,
        isAnalysed: true,
        status: status,
        result: result.toString(),
        taskId: task_id,
        spectrogramPath: spectrogramPath,
        avqiGram: avqiGram,
        abiGram: abiGram,
        // powerCepstrogram: powerCepstrogram,
        waveFormPath: waveFormPath,
      },
      setAudioFiles,
    );
  };

  const startAutoRequest = useCallback(
    (audioFiles: AudioFile[]) => {
      if (intervalRef.current) clearInterval(intervalRef.current);

      intervalRef.current = setInterval(() => {
        audioFiles.forEach(audioFile => {
          if (
            audioFile.status.toLowerCase() === 'pending' ||
            (audioFile.status.toLowerCase() === 'processing' &&
              audioFile.taskId)
          ) {
            requestResult(audioFile);
          }
        });

        const allCompleted = audioFiles.every(
          file =>
            file.status.toLowerCase() === 'completed' ||
            file.status.toLowerCase() === 'failed',
        );

        if (allCompleted) stopAutoRequest();
      }, 3 * 1000);
    },
    [state.audioFiles],
  );

  const stopAutoRequest = useCallback(() => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = null;
  }, []);

  return {
    setSortOption,
    setPhrases,
    setLanguage,
    setLessons,
    setExercises,
    setCurrentExercise,
    setCurrentLesson,
    setNextExercise,
    setAudioFiles,
    setLessonCount,
    setGraph,
    setToken,
    setPreviewEnd,
    startAutoRequest,
    stopAutoRequest,
    setPlayAllLesson,
    setPlaybackSpeed,
    state,
    dispatch,
  };
};

export {SortingProviderService};