import {
  Exercise,
  deleteEx,
  fetchExercises,
  insertEx,
  openExerciseDatabase,
  selectExercise,
  updateExercise,
} from '../../../services/exerciseManageService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useLessonNavigation} from '../../../utils/useAppNavigation';
import {
  editLessonName,
  openLessonDatabase,
  setTarget,
} from '../../../services/lessonManageService';
import useBottomSheet from '../../../components/BottomSheetComponent/useBottomSheet';

import {BottomSheetModal} from '@gorhom/bottom-sheet';
import {useTheme} from 'react-native-paper';
import React, {useEffect, useRef, useState} from 'react';
import {Alert} from 'react-native';

export const useExerciseSheet = () => {
  const navigation = useLessonNavigation();
  const theme = useTheme();
  const [visible, setVisible] = React.useState(false);
  const {
    exercises,
    currentLesson,
    currentExercise,
    setCurrentLesson,
    setLessons,
    setCurrentExercise,
    setNextExercise,
    setExercises,
  } = useSorting();

  const showModal = () => setVisible(true);
  const hideModal = () => setVisible(false);
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const {showBottomSheet, dismissBottomSheet} =
    useBottomSheet(bottomSheetModalRef);
  const [newName, setNewName] = useState('');
  const [newContents, setNewContents] = useState(['']);
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(currentExercise.name);
  const [inputFieldNumber, setInputFieldNumber] = useState<string[]>([]);

  //identify the id of exercise to navigate to Exercise Detail
  const selectedExercise = async (ex: Exercise) => {
    selectExercise(ex, exercises, setCurrentExercise, setNextExercise);
    navigation.navigate('ExerciseDetail');
  };

  const confirmDeleteEx = async (ex: Exercise) => {
    setCurrentExercise(ex);
    setValue('delete');
    setValue('delete');
    showModal();
  };

  const handleDelete = async () => {
    const db = await openExerciseDatabase();
    await deleteEx(db, currentExercise, setExercises);
    hideModal();
  };

  const handleDeleteNoConfirm = async (ex: Exercise) => {
    setCurrentExercise(ex);
    setValue('delete');
    const db = await openExerciseDatabase();
    await deleteEx(db, ex, setExercises);
  };

  const handleContentChange = (text: string, index: number) => {
    const updatedContents = inputFieldNumber.map((content, i) =>
      i === index ? text : content,
    );
    setInputFieldNumber(updatedContents);
  };

  const handleCancelContent = (index: number) => {
    const updatedContents = inputFieldNumber.filter((_, i) => i !== index);
    setInputFieldNumber(updatedContents);
  };

  const addContentField = () => {
    setInputFieldNumber([...inputFieldNumber, '']);
  };

  const handleAddExercise = async () => {
    if (
      !newName ||
      inputFieldNumber.length === 0 ||
      inputFieldNumber.every(content => content.trim() === '')
    ) {
      Alert.alert('Error', 'Please fill out all fields');
      return;
    }
    const db = await openExerciseDatabase();
    const lesson_id = currentLesson.id;
    await insertEx(db, lesson_id, newName, inputFieldNumber, setExercises);

    // Clear the input fields
    setNewName('');
    setNewContents(['']);
    dismissBottomSheet();
  };

  useEffect(() => {
    (async () => {
      const e_db = await openExerciseDatabase();
      await fetchExercises(e_db, currentLesson.id, setExercises);
      setCounter(currentLesson.target);
    })();
  }, []);

  const editExercise = (exercise: Exercise) => {
    if (exercise) {
      setIsEditing(true);
      setInputFieldNumber(exercise.content);
      setCurrentExercise(exercise);
      setEditName(exercise.name);
      showBottomSheet();
    }
  };

  const handleUpdate = async () => {
    for (let i = 0; i < inputFieldNumber.length; i++) {
      if (inputFieldNumber[i].trim() === '') {
        Alert.alert('Error', 'Please fill out all fields');
        return;
      }
    }
    if (!editName || inputFieldNumber.length === 0) {
      Alert.alert('Error', 'Please fill out all fields');
      return;
    }
    const updatedExercise = {
      ...currentExercise,
      name: editName,
      content: inputFieldNumber,
    };
    const db = await openExerciseDatabase();
    updateExercise(updatedExercise, db, setExercises);
    dismissBottomSheet();
  };

  const [value, setValue] = useState('edit');

  const editModal = async () => {
    setValue('edit');
    showModal();
  };
  const handleEdit = async () => {
    if (!newName) {
      Alert.alert('Error', 'Please fill out all fields');
      return;
    }
    const db = await openLessonDatabase();
    const lesson_id = currentLesson.id;
    await editLessonName(db, lesson_id, newName, setLessons, setCurrentLesson);

    // Clear the input fields
    setNewName('');
    setNewContents(['']);
    hideModal();
  };

  const [counter, setCounter] = useState(0);

  const incrementCounter = () => {
    setCounter(prevCounter => prevCounter + 1);
    targetSet(counter + 1);
  };
  const decrementCounter = () => {
    if (counter > 0) {
      setCounter(prevCounter => prevCounter - 1);
      targetSet(counter - 1);
    }
  };

  const targetSet = async (target: number) => {
    const db = await openLessonDatabase();
    await setTarget(db, currentLesson.id, target, setLessons, setCurrentLesson);
  };

  return {
    currentLesson,
    navigation,
    editModal,
    theme,
    decrementCounter,
    counter,
    incrementCounter,
    exercises,
    visible,
    hideModal,
    value,
    newName,
    setNewName,
    handleEdit,
    currentExercise,
    handleDelete,
    handleDeleteNoConfirm,
    showBottomSheet,
    setIsEditing,
    setInputFieldNumber,
    isEditing,
    bottomSheetModalRef,
    editName,
    setEditName,
    inputFieldNumber,
    handleContentChange,
    handleCancelContent,
    handleUpdate,
    handleAddExercise,
    addContentField,
    confirmDeleteEx,
    editExercise,
    selectedExercise,
  };
};
