PODS:
  - <PERSON> (5.0.0):
    - Pitchy (~> 3.0)
  - boost (1.83.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.74.0)
  - fmt (9.1.0)
  - glog (0.3.5)
  - hermes-engine (0.74.0):
    - hermes-engine/Pre-built (= 0.74.0)
  - hermes-engine/Pre-built (0.74.0)
  - ios-voice-processor (1.2.0)
  - Mute (0.6.1)
  - Pitchy (3.0.0)
  - RCT-Folly (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2024.01.01.00)
  - RCT-Folly/Default (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2024.01.01.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCTDeprecation (0.74.0)
  - RCTRequired (0.74.0)
  - RCTTypeSafety (0.74.0):
    - FBLazyVector (= 0.74.0)
    - RCTRequired (= 0.74.0)
    - React-Core (= 0.74.0)
  - React (0.74.0):
    - React-Core (= 0.74.0)
    - React-Core/DevSupport (= 0.74.0)
    - React-Core/RCTWebSocket (= 0.74.0)
    - React-RCTActionSheet (= 0.74.0)
    - React-RCTAnimation (= 0.74.0)
    - React-RCTBlob (= 0.74.0)
    - React-RCTImage (= 0.74.0)
    - React-RCTLinking (= 0.74.0)
    - React-RCTNetwork (= 0.74.0)
    - React-RCTSettings (= 0.74.0)
    - React-RCTText (= 0.74.0)
    - React-RCTVibration (= 0.74.0)
  - React-callinvoker (0.74.0)
  - React-Codegen (0.74.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/CoreModulesHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/Default (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/DevSupport (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.0)
    - React-Core/RCTWebSocket (= 0.74.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTBlobHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTImageHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTTextHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTWebSocket (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTDeprecation
    - React-Core/Default (= 0.74.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-CoreModules (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety (= 0.74.0)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.74.0)
    - React-jsi (= 0.74.0)
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.74.0)
    - ReactCommon
    - SocketRocket (= 0.7.0)
  - React-cxxreact (0.74.0):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.0)
    - React-debug (= 0.74.0)
    - React-jsi (= 0.74.0)
    - React-jsinspector
    - React-logger (= 0.74.0)
    - React-perflogger (= 0.74.0)
    - React-runtimeexecutor (= 0.74.0)
  - React-debug (0.74.0)
  - React-Fabric (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.74.0)
    - React-Fabric/attributedstring (= 0.74.0)
    - React-Fabric/componentregistry (= 0.74.0)
    - React-Fabric/componentregistrynative (= 0.74.0)
    - React-Fabric/components (= 0.74.0)
    - React-Fabric/core (= 0.74.0)
    - React-Fabric/imagemanager (= 0.74.0)
    - React-Fabric/leakchecker (= 0.74.0)
    - React-Fabric/mounting (= 0.74.0)
    - React-Fabric/scheduler (= 0.74.0)
    - React-Fabric/telemetry (= 0.74.0)
    - React-Fabric/templateprocessor (= 0.74.0)
    - React-Fabric/textlayoutmanager (= 0.74.0)
    - React-Fabric/uimanager (= 0.74.0)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.74.0)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.74.0)
    - React-Fabric/components/modal (= 0.74.0)
    - React-Fabric/components/rncore (= 0.74.0)
    - React-Fabric/components/root (= 0.74.0)
    - React-Fabric/components/safeareaview (= 0.74.0)
    - React-Fabric/components/scrollview (= 0.74.0)
    - React-Fabric/components/text (= 0.74.0)
    - React-Fabric/components/textinput (= 0.74.0)
    - React-Fabric/components/unimplementedview (= 0.74.0)
    - React-Fabric/components/view (= 0.74.0)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - RCTRequired (= 0.74.0)
    - RCTTypeSafety (= 0.74.0)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.74.0)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.74.0)
  - React-graphics (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core/Default (= 0.74.0)
    - React-utils
  - React-hermes (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.0)
    - React-jsi
    - React-jsiexecutor (= 0.74.0)
    - React-jsinspector
    - React-perflogger (= 0.74.0)
    - React-runtimeexecutor
  - React-ImageManager (0.74.0):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.74.0):
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.74.0):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
  - React-jsiexecutor (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-cxxreact (= 0.74.0)
    - React-jsi (= 0.74.0)
    - React-jsinspector
    - React-perflogger (= 0.74.0)
  - React-jsinspector (0.74.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-featureflags
    - React-jsi
    - React-runtimeexecutor (= 0.74.0)
  - React-jsitracing (0.74.0):
    - React-jsi
  - React-logger (0.74.0):
    - glog
  - React-Mapbuffer (0.74.0):
    - glog
    - React-debug
  - react-native-audio-waveform (2.0.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-check-permissions (0.1.6):
    - React-Core
  - react-native-date-picker (5.0.12):
    - React-Core
  - react-native-document-picker (9.3.0):
    - React-Core
  - react-native-html-to-pdf (0.12.0):
    - React-Core
  - react-native-pitch-detector (0.1.6):
    - Beethoven
    - React-Core
  - react-native-safe-area-context (4.10.8):
    - React-Core
  - react-native-slider (4.5.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-sqlite-storage (6.0.1):
    - React-Core
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-voice (3.2.4):
    - React-Core
  - react-native-voice-processor (1.2.3):
    - ios-voice-processor (~> 1.2.0)
    - React-Core
  - react-native-volume-manager (2.0.8):
    - Mute
    - React-Core
  - react-native-webview (13.13.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.74.0)
  - React-NativeModulesApple (0.74.0):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.74.0)
  - React-RCTActionSheet (0.74.0):
    - React-Core/RCTActionSheetHeaders (= 0.74.0)
  - React-RCTAnimation (0.74.0):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.74.0):
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-CoreModules
    - React-debug
    - React-Fabric
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCommon
  - React-RCTBlob (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.74.0):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.74.0):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.74.0)
    - React-jsi (= 0.74.0)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.74.0)
  - React-RCTNetwork (0.74.0):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.74.0):
    - RCT-Folly (= 2024.01.01.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.74.0):
    - React-Core/RCTTextHeaders (= 0.74.0)
    - Yoga
  - React-RCTVibration (0.74.0):
    - RCT-Folly (= 2024.01.01.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
  - React-rncore (0.74.0)
  - React-RuntimeApple (0.74.0):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-utils
  - React-RuntimeCore (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.74.0):
    - React-jsi (= 0.74.0)
  - React-RuntimeHermes (0.74.0):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.01.01.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.74.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-debug
    - React-jsi (= 0.74.0)
  - ReactCommon (0.74.0):
    - ReactCommon/turbomodule (= 0.74.0)
  - ReactCommon/turbomodule (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.0)
    - React-cxxreact (= 0.74.0)
    - React-jsi (= 0.74.0)
    - React-logger (= 0.74.0)
    - React-perflogger (= 0.74.0)
    - ReactCommon/turbomodule/bridging (= 0.74.0)
    - ReactCommon/turbomodule/core (= 0.74.0)
  - ReactCommon/turbomodule/bridging (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.0)
    - React-cxxreact (= 0.74.0)
    - React-jsi (= 0.74.0)
    - React-logger (= 0.74.0)
    - React-perflogger (= 0.74.0)
  - ReactCommon/turbomodule/core (0.74.0):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - React-callinvoker (= 0.74.0)
    - React-cxxreact (= 0.74.0)
    - React-debug (= 0.74.0)
    - React-jsi (= 0.74.0)
    - React-logger (= 0.74.0)
    - React-perflogger (= 0.74.0)
    - React-utils (= 0.74.0)
  - RNAudioRecord (0.2.2):
    - React
  - RNAudioRecorderPlayer (3.6.10):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.17.1):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (3.14.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.32.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNShare (11.0.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNSoundLevel (1.3.0):
    - React
  - RNSVG (12.5.1):
    - React-Core
  - RNVectorIcons (10.1.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.01.01.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Codegen
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SocketRocket (0.7.0)
  - TextToSpeech (4.1.1):
    - React-Core
  - VisionCamera (4.5.0):
    - VisionCamera/Core (= 4.5.0)
    - VisionCamera/React (= 4.5.0)
  - VisionCamera/Core (4.5.0)
  - VisionCamera/React (4.5.0):
    - React-Core
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - "react-native-audio-waveform (from `../node_modules/@simform_solutions/react-native-audio-waveform`)"
  - react-native-check-permissions (from `../node_modules/react-native-check-permissions`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-html-to-pdf (from `../node_modules/react-native-html-to-pdf`)
  - react-native-pitch-detector (from `../node_modules/react-native-pitch-detector`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-sqlite-storage (from `../node_modules/react-native-sqlite-storage`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - "react-native-voice (from `../node_modules/@react-native-voice/voice`)"
  - "react-native-voice-processor (from `../node_modules/@picovoice/react-native-voice-processor`)"
  - react-native-volume-manager (from `../node_modules/react-native-volume-manager`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RNAudioRecord (from `../node_modules/react-native-audio-record`)
  - RNAudioRecorderPlayer (from `../node_modules/react-native-audio-recorder-player`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSoundLevel (from `../node_modules/react-native-sound-level`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - TextToSpeech (from `../node_modules/react-native-tts`)
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - Beethoven
    - ios-voice-processor
    - Mute
    - Pitchy
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-02-20-RNv0.74.0-999cfd9979b5f57b1269119679ab8cdf60897de9
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-audio-waveform:
    :path: "../node_modules/@simform_solutions/react-native-audio-waveform"
  react-native-check-permissions:
    :path: "../node_modules/react-native-check-permissions"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-html-to-pdf:
    :path: "../node_modules/react-native-html-to-pdf"
  react-native-pitch-detector:
    :path: "../node_modules/react-native-pitch-detector"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-sqlite-storage:
    :path: "../node_modules/react-native-sqlite-storage"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-voice:
    :path: "../node_modules/@react-native-voice/voice"
  react-native-voice-processor:
    :path: "../node_modules/@picovoice/react-native-voice-processor"
  react-native-volume-manager:
    :path: "../node_modules/react-native-volume-manager"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNAudioRecord:
    :path: "../node_modules/react-native-audio-record"
  RNAudioRecorderPlayer:
    :path: "../node_modules/react-native-audio-recorder-player"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSoundLevel:
    :path: "../node_modules/react-native-sound-level"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  TextToSpeech:
    :path: "../node_modules/react-native-tts"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  Beethoven: e318691697c38933a69081d4bf4d2506213505fd
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  DoubleConversion: 76ab83afb40bddeeee456813d9c04f67f78771b5
  FBLazyVector: 026c8f4ae67b06e088ae01baa2271ef8a26c0e8c
  fmt: 4c2741a687cc09f0634a2e2c72a838b99f1ff120
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  hermes-engine: 6eae7edb2f563ee41d7c1f91f4f2e57c26d8a5c3
  ios-voice-processor: 6b5ca08962f39e434fe39dca0f483d923a3b1b97
  Mute: 20135a96076f140cc82bfc8b810e2d6150d8ec7e
  Pitchy: 15407e4425b6d88fa3e7eef8927ba659fab52d7a
  RCT-Folly: 5f972de9f7d384c7d0e7380dd7da506228e568f5
  RCTDeprecation: 3ca8b6c36bfb302e1895b72cfe7db0de0c92cd47
  RCTRequired: 9fc183af555fd0c89a366c34c1ae70b7e03b1dc5
  RCTTypeSafety: db1dd5ad1081a5e160d30bb29ef922693d5ac4b1
  React: 8650d592d90b99097504b8dcfebab883972aed71
  React-callinvoker: 6bb8b399ab8cec59e52458c3a592aa1fca130b68
  React-Codegen: 9f2e860b1ac0b9395529c9573a8b07dbc3886a3e
  React-Core: 70f3fb32d27fe0dd16e5600c9ebbbc861de59192
  React-CoreModules: c5791800e490979b15b819e13ceaee42aa4a2672
  React-cxxreact: 666dc368c229154ef3088e86b94b847294cb36ba
  React-debug: 41175f3e30dfa8af6eab2631261e1eac26307f9f
  React-Fabric: 5127d63a4233207891ea79a6d9f751309319a6f5
  React-FabricImage: ee08313993abcd1666880113eaa00f728e2620a1
  React-featureflags: 5e7e78c607661fe7f72bc38c6f03736e0876753a
  React-graphics: ea6e3c3f77683565552986548ba6a2938cb83251
  React-hermes: 5f20efa62d7fc7e4df8cdfa5b6c54f43a50f1a51
  React-ImageManager: 49a461cd14ed15749fe7371afb1924e8a72aecc1
  React-jserrorhandler: bccc0691bf5195f4da1292a4d2fbaa13fa895f89
  React-jsi: 7f62c2cecdcf64c9e3e7518a523652433795fed2
  React-jsiexecutor: 59436ebad9bdc490ba194a3a61f763b32f2e0219
  React-jsinspector: 76a5c6385781b120f4ff0c09e515fb16528bfc4a
  React-jsitracing: d30048b056e8c9673dfbe67813bdb874c03558a5
  React-logger: 5ae0978955199c132e71e8cf7797f619a6d17164
  React-Mapbuffer: 3b85b3778e447cd1f06d353b8e967af50f272829
  react-native-audio-waveform: 13bf5af1a029db5a5bdebdd0c20915af9dca5d94
  react-native-check-permissions: ad906064b2ea86394708cccbb1a878a8dbf45e33
  react-native-date-picker: 2eb0ed05ab9dcb4f337deda77deaa779807c4c61
  react-native-document-picker: 6b08d834d4e4252bb8aad13d852e27666294b5b9
  react-native-html-to-pdf: 7a49e6c58ac5221bcc093027b195f4b214f27a9d
  react-native-pitch-detector: 7c4ffa35449c71c87859dac07000003b71f29a8b
  react-native-safe-area-context: b72c4611af2e86d80a59ac76279043d8f75f454c
  react-native-slider: bcfc4f2683c072497b157a3c3f00e514a05feec5
  react-native-splash-screen: 95994222cc95c236bd3cdc59fe45ed5f27969594
  react-native-sqlite-storage: 0c84826214baaa498796c7e46a5ccc9a82e114ed
  react-native-view-shot: d1a701eb0719c6dccbd20b4bb43b1069f304cb70
  react-native-voice: 908a0eba96c8c3d643e4f98b7232c6557d0a6f9c
  react-native-voice-processor: e4d40fbac0c1ac60f085160927de033346692ee0
  react-native-volume-manager: cdd3c3857158c1df7b9fbea071a9946395cee06c
  react-native-webview: f1c37a2047ac0a9493c6de67c7301a39d5a3a6ad
  React-nativeconfig: 951ec32f632e81cbd7d40aebb3211313251c092e
  React-NativeModulesApple: c8963908368d05a75af1eef7451ff86efc9b6517
  React-perflogger: 271f1111779fef70f9502d1d38da5132e5585230
  React-RCTActionSheet: 5d6fb9adb11ab1bfbce6695a2b785767e4658c53
  React-RCTAnimation: 0d11291f869c8a15cff4fd21dca031a83f9e8527
  React-RCTAppDelegate: 5194e63613db66d448dfae9f98b269bf6291915c
  React-RCTBlob: 1689488d4a465692d29b783c8e310a45bf9c1c96
  React-RCTFabric: 5b018dc56bfedce0121992ddc44a46419a787778
  React-RCTImage: 80ba9b23ecf87536b14c5eb38bd76f9d2b842c8a
  React-RCTLinking: afd22b0854eba28eb277baad45c37ada5ef77bc3
  React-RCTNetwork: ffe5a1021f5a0bcbdf7944665dc44856493ab5bd
  React-RCTSettings: f8472ee7998de8d186c198e820c40fcaf9ce4571
  React-RCTText: f556484bf1ba49a7c9b1ce1138608657d80e0bcb
  React-RCTVibration: 236755b4231073ebac6cabc3864edb4cd6308d89
  React-rendererdebug: c1dac9f04b12f05929b6113a50aec5fcd5132b94
  React-rncore: a3ab9e7271a5c692918e2a483beb900ff0a51169
  React-RuntimeApple: a095023ded63a404c31732859b0732a545083273
  React-RuntimeCore: 4580bf6bc6a5c2d9c04995b4ded51751901894ff
  React-runtimeexecutor: 4471221991b6e518466a0422fbeb2158c07c36e1
  React-RuntimeHermes: 48b3c464990052c75a5ca0a385f1de8b2041a5e1
  React-runtimescheduler: 5ae8dc5d3195a1582ad076493ab6d9d3fd7c1287
  React-utils: c2c26076afce4fcf8d3d81dd75f8099a5a988383
  ReactCommon: b1dfa365018c636c73437981ee235c1a35f0e34f
  RNAudioRecord: b253d15d8141c5ef6bc614fc9d9f06577870bfc3
  RNAudioRecorderPlayer: cf71cb74c6457bb42f564319076f52c9d0fa9b83
  RNCAsyncStorage: b6410dead2732b5c72a7fdb1ecb5651bbcf4674b
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: 77ca8899a0bd9a9d948e74174ee401bbffa5e524
  RNPermissions: bd0d9ca7969ff7b999aa605ee2e5919c12522bfe
  RNReanimated: fe62058b0e1ecb46e252d63d27580f36cd6d9eb2
  RNScreens: 9b6c77e83fa17fb9cef0af508cdcb4f4dc07f468
  RNShare: 97c31041033806d27d8e008fe64df290ae7472d5
  RNSoundLevel: a48da8668fbde4f792c0bea948e27ab793bee7e1
  RNSVG: d7cb8bd34550cf4c4fc7edd7ac627905e2b71f3f
  RNVectorIcons: f733cb2133a8e1f7dc723b97a1d70dad681124c3
  SocketRocket: abac6f5de4d4d62d24e11868d7a2f427e0ef940d
  TextToSpeech: 89b239221ec378d213af422d14789a729705360b
  VisionCamera: 499873729ea4f22e82b726428b546b0d2531ff37
  Yoga: 56f906bf6c11c931588191dde1229fd3e4e3d557

PODFILE CHECKSUM: c5e5f208a0b82edf1ec5b4d640ed7e84cea86655

COCOAPODS: 1.16.2
